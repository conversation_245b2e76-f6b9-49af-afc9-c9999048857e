---
description: 
globs: 
alwaysApply: false
---
---
description: This rule defines coding standards and best practices for developing dashboard modules.
globs: src/app/dashboard**/*
---
## Directory Structure

Dashboard modules follow this standard structure:

```
src/app/dashboard/[module-name]/
├── actions/
│   └── index.ts                    # Server actions for data operations
├── _components/
│   ├── [module]-form.tsx           # Form component for create/edit
│   ├── [module]-listing-page.tsx   # Main listing page
│   ├── [module]-view-page.tsx      # View/edit page
│   └── [module]-tables/
│       ├── index.tsx               # Main table component
│       ├── columns.tsx             # Table column definitions
│       ├── cell-action.tsx         # Cell action buttons
│       └── use-[module]-table-filters.tsx # Table filter hooks
├── new/
│   └── page.tsx                    # Create new item page
├── [moduleId]/
│   └── page.tsx                    # Edit existing item page
└── page.tsx                        # Main listing page
```

## Implementation Guidelines

1. Replace "module" with your actual feature name in kebab-case
2. Use TypeScript for all components with proper types
3. Use server components by default for data fetching
4. Mark interactive components with "use client" directive
5. Use Zod for data validation in forms
6. Implement proper error handling and loading states
7. Leverage Suspense for asynchronous operations
8. Maintain consistent naming conventions
9. Follow the Next.js 15 App Router structure

## Page Templates

### Main Page Template (page.tsx)

```tsx
import { searchParamsCache } from "@/lib/searchparams";
import { SearchParams } from "nuqs/parsers";
import React from "react";
import ModuleListingPage from "./_components/module-listing-page";

type pageProps = {
  searchParams: SearchParams;
};

export const metadata = {
  title: "Dashboard : Module Name",
};

export default async function Page({ searchParams }: pageProps) {
  // Allow nested RSCs to access the search params (in a type-safe way)
  searchParamsCache.parse(searchParams);

  return <ModuleListingPage />;
}
```

### New Item Page Template (new/page.tsx)

```tsx
import ModuleViewPage from "../_components/module-view-page";

export const metadata = {
  title: "Dashboard : Create New Item",
};

export default function Page() {
  return <ModuleViewPage />;
}
```

### Item Edit Page Template ([moduleId]/page.tsx)

```tsx
import ModuleViewPage from "../_components/module-view-page";

export const metadata = {
  title: "Dashboard : Edit Item",
};

interface PageProps {
  params: {
    moduleId: string;
  };
}

export default function Page({ params }: PageProps) {
  return <ModuleViewPage moduleId={params.moduleId} />;
}
```

## Component Templates

### Listing Page Template (_components/module-listing-page.tsx)

```tsx
import PageContainer from "@/components/layout/page-container";
import { buttonVariants } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { searchParamsCache, serialize } from "@/lib/searchparams";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { DataTableSkeleton } from "@/components/ui/table/data-table-skeleton";
import ModuleTable from "./module-tables";
import { getItems } from "../actions";

type TModuleListingPage = object;

export default async function ModuleListingPage({}: TModuleListingPage) {
  // Get filter values from search params
  const page = searchParamsCache.get("page");
  const search = searchParamsCache.get("q");
  const pageLimit = searchParamsCache.get("limit");
  
  const filters = {
    page,
    limit: pageLimit,
    ...(search && { search }),
  };

  // Fetch data with filters
  const data = await getItems(filters);
  const totalItems = data.total;
  const items = data.items;

  // Suspense key for re-fetching when filters change
  const key = serialize({ ...filters });

  return (
    <PageContainer scrollable>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <Heading title={`Items (${totalItems})`} description="Manage items" />

          <Link
            href={"/dashboard/module/new"}
            className={cn(buttonVariants({ variant: "default" }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />
        <Suspense
          key={key}
          fallback={<DataTableSkeleton columnCount={7} rowCount={10} />}
        >
          <ModuleTable data={items} totalData={totalItems} />
        </Suspense>
      </div>
    </PageContainer>
  );
}
```

### View/Edit Page Template (_components/module-view-page.tsx)

```tsx
"use server";

import ModuleForm from "./module-form";
import PageContainer from "@/components/layout/page-container";
import { notFound } from "next/navigation";
import { unstable_noStore as noStore } from "next/cache";
import { getItemById } from "../actions";

interface ModuleViewPageProps {
  moduleId?: string;
}

async function getItem(moduleId: string) {
  noStore(); // Opt out of static rendering
  try {
    const result = await getItemById(moduleId);
    return result;
  } catch (error) {
    console.error("Error fetching item:", error);
    return null;
  }
}

export default async function ModuleViewPage({ moduleId }: ModuleViewPageProps) {
  if (moduleId) {
    const item = moduleId ? await getItem(moduleId) : undefined;
    if (!item) {
      notFound();
    }

    return (
      <PageContainer>
        <ModuleForm item={item} />
      </PageContainer>
    );
  } else {
    return (
      <PageContainer>
        <ModuleForm />
      </PageContainer>
    );
  }
}
```

## Server Actions Template (_actions/index.ts)

```ts
"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";

/**
 * Get items with pagination and filtering
 */
export async function getItems(filters) {
  try {
    const supabase = await createAdminClient();
    let query = supabase.from("items").select("*", { count: "exact" });
    
    // Apply filters
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }
    
    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    query = query.range(from, to).order("created_at", { ascending: false });
    
    const { data, error, count } = await query;
    
    if (error) {
      throw new Error(error.message);
    }
    
    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching items:", error);
    throw error;
  }
}

/**
 * Get a single item by ID
 */
export async function getItemById(itemId) {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("items")
      .select("*")
      .eq("id", itemId)
      .single();
      
    if (error) {
      throw new Error(error.message);
    }
    
    if (!data) {
      throw new Error("Item not found");
    }
    
    return data;
  } catch (error) {
    console.error("Error fetching item:", error);
    throw error;
  }
}

/**
 * Create a new item
 */
export async function createItem(itemData) {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("items")
      .insert(itemData)
      .select()
      .single();
      
    if (error) {
      throw new Error(error.message);
    }
    
    // Revalidate the items page to update the UI
    revalidatePath("/dashboard/module");
    
    return data;
  } catch (error) {
    console.error("Error creating item:", error);
    throw error;
  }
}

/**
 * Update an item
 */
export async function updateItem(itemId, itemData) {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("items")
      .update(itemData)
      .eq("id", itemId)
      .select()
      .single();
      
    if (error) {
      throw new Error(error.message);
    }
    
    // Revalidate the relevant pages to update the UI
    revalidatePath("/dashboard/module");
    revalidatePath(`/dashboard/module/${itemId}`);
    
    return data;
  } catch (error) {
    console.error("Error updating item:", error);
    throw error;
  }
}

/**
 * Delete an item
 */
export async function deleteItem(itemId) {
  try {
    const supabase = await createAdminClient();
    const { error } = await supabase
      .from("items")
      .delete()
      .eq("id", itemId);
      
    if (error) {
      throw new Error(error.message);
    }
    
    // Revalidate the items page to update the UI
    revalidatePath("/dashboard/module");
  } catch (error) {
    console.error("Error deleting item:", error);
    throw error;
  }
}
```

## Naming Conventions

- Use kebab-case for directories: ex: `src/app/dashboard/product-category/`
- Use kebab-case for component files: ex: `product-category-form.tsx`
- Use camelCase for functions, variables, and hooks: ex: `useProductCategoryFilters`
- Use PascalCase for components: ex: `ProductCategoryForm`
- Use UPPER_SNAKE_CASE for constants: ex: `MAX_ITEMS_PER_PAGE`

## TypeScript and Type Safety

- Define proper interfaces for all component props
- Use Zod schemas for data validation
- Export and reuse types across the module
- Use type inference where appropriate
- Place types in a dedicated types file for complex modules

Example:
```typescript
// Types pattern
interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

interface ProductCategoryFormProps {
  category?: ProductCategory;
  onSuccess?: () => void;
}
```

## Server Components vs Client Components

- Use Server Components by default
- Only use "use client" for interactive components
- Keep client components focused and minimal
- Server components should handle data fetching
- Use Suspense boundaries appropriately

## Data Fetching and Mutations

- Use server actions for all data operations
- Implement proper error handling
- Use revalidatePath for cache invalidation
- Use unstable_noStore for dynamic data
- Follow consistent patterns for filters and pagination

## Form Implementation

- Use controlled components for form inputs
- Implement proper form validation with Zod
- Show appropriate loading and error states
- Use React Hook Form for complex forms
- Implement proper accessibility attributes

## Error Handling

- Use try/catch blocks consistently
- Return meaningful error messages
- Implement error boundaries
- Use notFound() for 404 cases
- Log errors appropriately

## State Management

- Use search params for UI state when possible
- Implement URL-based filters and pagination
- Minimize client-side state
- Use React Context for shared state where necessary
- Follow the principles of unidirectional data flow

## Performance Considerations

- Optimize components for re-rendering
- Use proper suspense boundaries
- Implement efficient pagination
- Optimize images using Next.js Image component
- Use proper caching strategies

## Code Organization

- Group related components together
- Keep files small and focused
- Extract reusable logic into custom hooks
- Follow the principle of separation of concerns
- Maintain consistent file structure across modules