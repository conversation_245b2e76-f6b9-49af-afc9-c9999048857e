---
description:
globs:
alwaysApply: true
---
# Error Handling Guidelines

## Client-Side Error Handling

- Use try/catch blocks for all async operations
- Implement proper error boundary components
- Display user-friendly error messages
- Log errors to console in development
- Handle network errors gracefully
- Provide meaningful feedback to users

## Server-Side Error Handling

- Use try/catch blocks for all server actions
- Return proper error status codes
- Log server errors appropriately
- Don't expose sensitive information in error messages
- Implement proper error middleware
- Handle database errors gracefully

## Error Types and Classification

- Define custom error types for different scenarios
- Use consistent error structure
- Separate between user errors and system errors
- Handle validation errors consistently
- Implement proper error codes for API responses
- Document error types and their meanings

## Form Validation Errors

- Use Zod for input validation
- Display validation errors inline with form fields
- Provide clear error messages for each validation rule
- Handle form-wide errors appropriately
- Show proper error states for form fields
- Use consistent error styling

## Code Examples

### Client-Side Error Handling

```typescript
"use client";

import { useState } from "react";
import { toast } from "sonner";

export function useApiCall<T>(apiFunction: (...args: any[]) => Promise<T>) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);
  
  const execute = async (...args: any[]) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiFunction(...args);
      setData(result);
      return result;
    } catch (err: any) {
      const errorMessage = err.message || "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };
  
  return {
    execute,
    loading,
    error,
    data,
  };
}
```

### Server Action Error Handling

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { StatusCodes } from "http-status-codes";
import { logger } from "@/utils/logger";

export async function serverAction(data: any) {
  try {
    // Perform operation
    return { success: true, data: "result" };
  } catch (error: any) {
    // Log the error
    logger.error({
      message: "Error in serverAction",
      error: error.message,
      stack: error.stack,
    });
    
    // Return a structured error response
    return {
      success: false,
      error: {
        message: "Failed to process request",
        code: StatusCodes.INTERNAL_SERVER_ERROR,
      },
    };
  }
}
```

### Error Boundary Component

```tsx
"use client";

import { useEffect } from "react";
import { Button } from "@/components/ui/button";

interface ErrorBoundaryProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorBoundary({
  error,
  reset,
}: ErrorBoundaryProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex h-[calc(100vh-4rem)] w-full flex-col items-center justify-center">
      <h2 className="text-2xl font-bold">Something went wrong!</h2>
      <p className="text-muted-foreground mt-2">
        {process.env.NODE_ENV === "development" ? error.message : "An error occurred while loading this page."}
      </p>
      <Button
        onClick={reset}
        className="mt-4"
      >
        Try again
      </Button>
    </div>
  );
}
```

## Best Practices

- Implement global error handling
- Differentiate between expected and unexpected errors
- Don't silently swallow errors
- Provide retry mechanisms when appropriate
- Use structured error responses for APIs
- Handle edge cases explicitly
- Test error scenarios thoroughly
- Use descriptive error messages
- Implement proper logging for debugging
- Handle asynchronous errors properly
