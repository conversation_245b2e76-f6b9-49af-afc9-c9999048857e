---
description: 
globs: 
alwaysApply: true
---
# Search and Filtering Guidelines

## URL-Based State Management

- Use URL search params for filter state
- Implement proper URL serialization
- Handle URL param parsing properly
- Use proper URL state management library (nuqs)
- Implement proper filter persistence
- Handle deep linking properly

## Filter Components

- Create reusable filter components
- Implement proper filter reset
- Handle multiple filters properly
- Implement proper filter validation
- Show active filters clearly
- Provide clear filter labels

## Search Implementation

- Implement proper search debouncing
- Handle empty search results gracefully
- Implement proper search loading states
- Use proper search indexing on the backend
- Implement proper search algorithm
- Support advanced search syntax when needed

## Data Table Filtering

- Implement proper column filtering
- Handle multiple column filters
- Implement proper filter serialization
- Show active filters in the UI
- Provide clear filter controls
- Implement proper filter reset

## Code Examples

### URL Search Params Setup

```typescript
// src/lib/searchparams.ts
import { createParser } from "nuqs/server";
import { parseAsInteger, parseAsString } from "nuqs/parsers";

export const searchParamsSchema = {
  q: parseAsString.optional(),
  page: parseAsInteger.withDefault(1),
  limit: parseAsInteger.withDefault(10),
  sort: parseAsString.optional(),
  status: parseAsString.optional(),
  category: parseAsString.optional(),
};

// Create a parser for server components
export const searchParamsCache = createParser(searchParamsSchema);

// Serialize search params for client components
export function serialize(params: Record<string, any>) {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      searchParams.set(key, String(value));
    }
  });
  
  return searchParams.toString();
}
```

### Data Table Filters Hook

```typescript
// src/app/dashboard/product/_components/product-tables/use-product-table-filters.tsx
"use client";

import { usePathname, useRouter } from "next/navigation";
import { useCallback } from "react";
import { 
  useQueryState, 
  parseAsInteger, 
  parseAsString,
  QueryState
} from "nuqs";
import { debounce } from "use-debounce";

export function useProductTableFilters() {
  const router = useRouter();
  const pathname = usePathname();

  // Define filters with nuqs
  const [search, setSearch] = useQueryState("q", parseAsString.withDefault(""));
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [limit, setLimit] = useQueryState("limit", parseAsInteger.withDefault(10));
  const [status, setStatus] = useQueryState("status", parseAsString.optional());
  const [category, setCategory] = useQueryState("category", parseAsString.optional());

  // Debounced search handler
  const [handleSearchChange] = debounce((value: string) => {
    setSearch(value || null);
    setPage(1); // Reset to first page on search
  }, 300);

  // Reset all filters
  const resetFilters = useCallback(() => {
    setSearch(null);
    setStatus(null);
    setCategory(null);
    setPage(1);
  }, [setSearch, setStatus, setCategory, setPage]);

  // Get the active filters count
  const getActiveFiltersCount = useCallback(() => {
    let count = 0;
    if (search) count++;
    if (status) count++;
    if (category) count++;
    return count;
  }, [search, status, category]);

  return {
    // Filter values
    search,
    page,
    limit,
    status,
    category,
    
    // Filter setters
    setSearch: handleSearchChange,
    setPage,
    setLimit,
    setStatus,
    setCategory,
    
    // Helper functions
    resetFilters,
    activeFiltersCount: getActiveFiltersCount(),
  };
}
```

### Table Component with Filters

```tsx
// src/app/dashboard/product/_components/product-tables/index.tsx
"use client";

import { DataTable } from "@/components/ui/table/data-table";
import { columns } from "./columns";
import { useProductTableFilters } from "./use-product-table-filters";
import { TableFilters } from "./table-filters";
import { TablePagination } from "@/components/ui/table/data-table-pagination";

interface ProductTableProps {
  data: any[];
  totalData: number;
}

export default function ProductTable({ data, totalData }: ProductTableProps) {
  const filters = useProductTableFilters();
  
  return (
    <div className="space-y-4">
      <TableFilters
        search={filters.search}
        setSearch={filters.setSearch}
        status={filters.status}
        setStatus={filters.setStatus}
        category={filters.category}
        setCategory={filters.setCategory}
        resetFilters={filters.resetFilters}
        activeFiltersCount={filters.activeFiltersCount}
      />
      
      <DataTable
        columns={columns}
        data={data}
        isLoading={false}
      />
      
      <TablePagination
        currentPage={filters.page}
        totalItems={totalData}
        pageSize={filters.limit}
        onPageChange={filters.setPage}
        onPageSizeChange={filters.setLimit}
      />
    </div>
  );
}
```

### Table Filters Component

```tsx
// src/app/dashboard/product/_components/product-tables/table-filters.tsx
"use client";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Cross2Icon, MagnifyingGlassIcon, ReloadIcon } from "@radix-ui/react-icons";

interface TableFiltersProps {
  search: string;
  setSearch: (value: string) => void;
  status: string | null;
  setStatus: (value: string | null) => void;
  category: string | null;
  setCategory: (value: string | null) => void;
  resetFilters: () => void;
  activeFiltersCount: number;
}

export function TableFilters({
  search,
  setSearch,
  status,
  setStatus,
  category,
  setCategory,
  resetFilters,
  activeFiltersCount,
}: TableFiltersProps) {
  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-end md:space-x-4 md:space-y-0">
      {/* Search Input */}
      <div className="flex-1">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            className="pl-9"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          {search && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3"
              onClick={() => setSearch("")}
            >
              <Cross2Icon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Status Filter */}
      <div className="w-full md:w-[180px]">
        <Select
          value={status || ""}
          onValueChange={(value) => setStatus(value || null)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Category Filter */}
      <div className="w-full md:w-[180px]">
        <Select
          value={category || ""}
          onValueChange={(value) => setCategory(value || null)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Categories</SelectItem>
            <SelectItem value="electronics">Electronics</SelectItem>
            <SelectItem value="clothing">Clothing</SelectItem>
            <SelectItem value="furniture">Furniture</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Reset Filters */}
      {activeFiltersCount > 0 && (
        <Button
          variant="outline"
          size="icon"
          onClick={resetFilters}
          title="Reset filters"
        >
          <ReloadIcon className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
```

## Best Practices

- Keep filter state in URL params
- Implement proper debouncing for search
- Handle filter changes gracefully
- Implement proper loading states
- Reset pagination on filter changes
- Keep filters consistent across the application
- Test filter combinations thoroughly
- Provide clear visual feedback on active filters
- Implement proper error handling for filter operations
- Document filter options clearly
