---
description: 
globs: 
alwaysApply: false
---
---
description: <PERSON><PERSON><PERSON> dựng hệ thống Live Chat cho phép giao tiếp theo thời gian thực (realtime) giữa các nhóm người dùng
globs: src/app/dashboard**/*
---
## 🧩 **Live Chat Module**

### 🎯 Mục tiêu
Xây dựng hệ thống Live Chat cho phép **giao tiếp theo thời gian thực (realtime)** giữa các nhóm người dùng:
- **Customer ↔️ Admin/Staff**
- **Technician ↔️ Admin/Staff**

Hỗ trợ:
- Tạo hội thoại (conversation) 1:1  
- Gửi tin nhắn văn bản realtime  
- Hiển thị trạng thái “đã đọc”  
- Hiển thị danh sách hội thoại + lịch sử tin nhắn  
- Quản lý hiệu quả với dữ liệu lớn (nhiều user, nhiều hội thoại)  

> Tất cả cuộc trò chuyện với `admin` hoặc `staff` được đại diện bằng một user duy nhất: **`system`**

---

## 🧠 **Nguyên tắc xử lý dữ liệu**

### 1. Conversation
- Mỗi conversation chỉ giữa **1 user (customer hoặc technician)** và **system**
- Một user chỉ có **1 conversation duy nhất** với system (tránh trùng lặp)
- Vai trò `admin`, `staff` luôn đại diện là `system` trong bảng `conversations`

### 2. Message
- Tin nhắn là **1-1** giữa user và system  
- Chỉ hỗ trợ **text** ở giai đoạn đầu  
- Cấu trúc phải dễ mở rộng cho **file, ảnh, nhóm** sau này

### 3. Realtime
- Dùng **Supabase Realtime Postgres Changes** để lắng nghe:
  - Conversation list
  - Message content
- Auto scroll nếu đang ở cuối
- Nếu đang scroll lên → hiện nút "Back to latest"

### 4. Hiệu suất & Truy vấn
- Chỉ load dữ liệu liên quan đến user hiện tại (dựa vào role & quyền)
- Sử dụng **pagination**: mỗi lần tối đa 50 tin nhắn
- Auto indexing:
  - `conversation_id`, `user_id`, `user_role` (trong `conversations`)
  - `created_at`, `conversation_id`, `sender_id` (trong `messages`)
- Tối ưu truy vấn:
  - Tối thiểu truy vấn cần thiết
  - Chỉ load conversation liên quan đến user hiện tại
- Danh sách user khả dụng để tạo hội thoại là user **chưa có cuộc trò chuyện với system**
- Gửi tin nhắn theo kiểu **optimistic UI** (hiển thị ngay trước khi phản hồi realtime)

---

## 🖼️ **Cấu trúc Giao diện UI**

UI tuân thủ 100% theo [ShadCN UI Kit UI](https://shadcnuikit.com/dashboard/apps/chat) 

- **2 cột**:
  - Trái: Danh sách hội thoại  
  - Phải: Khung tin nhắn + input gửi

### 2. Tạo hội thoại mới
- Nút "New Conversation"
- Modal: Dropdown + search theo role
- Gửi request tạo hội thoại

### 3. Realtime UI
- Tin nhắn hiển thị tức thì
- UI cập nhật khi gửi/nhận message qua realtime events

---

## 📡 **Realtime - Supabase Realtime DB**

### Các bảng cần lắng nghe:
| Bảng         | Sự kiện        | Hành động UI                 |
|--------------|----------------|------------------------------|
| `messages`   | `INSERT`       | Thêm message vào UI         |
| `conversations` | `INSERT`, `UPDATE` | Cập nhật danh sách hội thoại |

### Thiết lập:
```ts
supabase.channel('realtime:messages')
supabase.channel('realtime:conversations')
```

### Lưu ý:
- Chỉ subscribe conversation đang mở
- Cleanup channel khi rời UI hoặc chuyển cuộc trò chuyện
- Dùng:  
```ts
supabase.channel().on('postgres_changes')
```

---

## 🧠 **Logic chính**

### ✅ 1. Tạo cuộc trò chuyện mới
- Tạo 1:1 giữa system và customer/technician
- Không cho phép tạo trùng

### 🔎 Lọc user khả dụng để tạo conversation
#### Mục tiêu:
Lấy danh sách user **chưa có hội thoại với system**

#### SQL:
```sql
SELECT *
FROM users
WHERE role = 'customer' -- hoặc 'technician'
  AND id NOT IN (
    SELECT user_id
    FROM conversations
    WHERE user_role = 'customer' -- hoặc 'technician'
  );
```

#### Tối ưu:
- Index: `users(id, role)`
- Index: `conversations(user_id, user_role)`
- Có thể dùng `LEFT JOIN ... IS NULL` nếu hiệu năng tốt hơn

#### UI filter:
- Cho phép lọc theo `role`
- Chỉ hiển thị user **chưa có hội thoại**

---

### ✅ 2. Gửi tin nhắn
- `INSERT` vào bảng `messages`
- Cập nhật `last_message_at` trong bảng `conversations`

### ✅ 3. Đánh dấu “đã đọc”
- Khi người nhận xem tin → cập nhật `read_at` tương ứng

---

### ✅ 4. Danh sách hội thoại
- Hiển thị conversation liên quan user hiện tại
- Bao gồm:
  - Tên đối phương
  - Tin nhắn cuối + thời gian
  - Trạng thái đọc / chưa đọc
- Sắp xếp theo `last_message_at DESC`

---

## 🧪 **Logic xử lý cần đảm bảo**

| Tính năng                                 | Mô tả |
|------------------------------------------|-------|
| Load conversation list                   | Chỉ load của user hiện tại |
| Load message content                     | Theo `conversation_id`, phân trang |
| Gửi tin nhắn                              | Tạo message, update `updated_at` |
| Auto scroll                               | Khi ở cuối và có message mới |
| Scroll load history                       | Cuộn lên sẽ `LIMIT 50` |
| Nút “Back to latest”                      | Khi user cuộn lên xa |
| Phân biệt tin nhắn mới                    | So sánh `created_at` với `read_at` |
| Lọc user khả dụng tạo conversation       | Lọc user chưa có hội thoại |
| UI hoàn toàn tiếng Anh, giữ chuẩn layout | Bắt buộc |

---

## 📈 **Khả năng mở rộng (Future-proof)**

- **Chat nhóm**: thêm bảng `conversation_participants`
- **Gửi ảnh, file**: thêm `message_type`, `file_url`
- **Tích hợp notification/push**:
  - Qua Supabase Functions
  - Hoặc FCM (Firebase Cloud Messaging)


## 📚 **Tài liệu tham khảo**
- [Supabase Realtime Postgres Changes](https://supabase.com/docs/guides/realtime/postgres-changes)
- [Supabase Reacltime Chat ](https://supabase.com/ui/docs/nextjs/realtime-chat)
- [Optimistic UI Pattern – React Docs](https://react.dev/learn/optimistic-updates)

