---
description: 
globs: 
alwaysApply: true
---
# Supabase Integration Guidelines

## Client Creation and Setup

- Use the appropriate client based on the context:
  - Server-side: Use `createAdminClient()` for privileged operations
  - Client-side: Use `createClient()` for authenticated user operations
  - Auth-specific: Use `createAuthClient()` for authentication operations

## Database Operations

- Use server actions for all database operations
- Implement proper error handling with try/catch blocks
- Return meaningful error messages to the client
- Use proper typing for all database operations
- Use proper parameterization for SQL queries
- Implement pagination and filtering for list operations

## Authentication

- Use Supabase Auth for authentication
- Implement proper session management
- Use middleware for route protection
- Implement proper role-based access control
- Handle authentication errors gracefully
- Provide clear feedback to users on auth state

## Storage

- Use Supabase Storage for file uploads
- Implement proper file type validation
- Set appropriate bucket policies
- Implement proper error handling for uploads
- Use signed URLs for secure file access
- Clean up unused files properly

## RLS (Row Level Security)

- Implement proper RLS policies for all tables
- Test RLS policies thoroughly
- Document RLS policies clearly
- Use service roles only when absolutely necessary
- Implement proper row-level access control

## Code Examples

### Server Action Template

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";

export async function fetchData(filters: any) {
  try {
    const supabase = await createAdminClient();
    const { data, error, count } = await supabase
      .from("table_name")
      .select("*", { count: "exact" })
      .order("created_at", { ascending: false });
      
    if (error) {
      throw new Error(error.message);
    }
    
    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
}
```

### Authentication Template

```typescript
"use client";

import { createAuthClient } from "@/utils/supabase";
import { useState } from "react";

export function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const signIn = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const supabase = createAuthClient();
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        throw error;
      }
      
      return true;
    } catch (err: any) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  };
  
  return {
    signIn,
    loading,
    error,
  };
}
```

## Best Practices

- Keep sensitive operations server-side
- Validate all user input before database operations
- Use appropriate error handling
- Implement proper logging for debugging
- Follow the principle of least privilege
- Document all database schema changes
- Use migrations for schema changes
- Test database operations thoroughly
