---
description: 
globs: 
alwaysApply: true
---
# Form Validation Guidelines

## Validation Schema

- Use Zod for form validation
- Define reusable validation schemas
- Place validation schemas in dedicated files
- Use descriptive error messages
- Implement proper type inference
- Handle complex validation rules properly

## Form State Management

- Use React Hook Form for form state management
- Implement proper form submission handling
- Handle form errors appropriately
- Show loading states during form submission
- Implement proper form reset
- Validate forms on both client and server

## Input Components

- Create reusable form input components
- Show inline validation errors
- Implement proper error states
- Use proper aria attributes for accessibility
- Implement proper form field labeling
- Provide clear feedback on validation state

## Advanced Validation

- Implement field dependencies when needed
- Handle conditional validation
- Implement custom validation rules
- Validate against backend data when necessary
- Handle async validation appropriately
- Implement cross-field validation when needed

## Code Examples

### Validation Schema

```typescript
// src/lib/form-schema.ts
import { z } from "zod";

export const userSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export type UserFormValues = z.infer<typeof userSchema>;
```

### Form Component

```tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { userSchema } from "@/lib/form-schema";
import { createUser } from "../actions";

type UserFormValues = z.infer<typeof userSchema>;

export function UserForm() {
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  async function onSubmit(values: UserFormValues) {
    try {
      const result = await createUser(values);
      if (result.success) {
        toast.success("User created successfully");
        form.reset();
      } else {
        toast.error(result.error.message);
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create user");
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={form.formState.isSubmitting}>
          {form.formState.isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </form>
    </Form>
  );
}
```

### Server Action Validation

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { userSchema } from "@/lib/form-schema";
import { createAdminClient } from "@/utils/supabase";
import { StatusCodes } from "http-status-codes";

export async function createUser(formData: unknown) {
  try {
    // Validate the form data
    const result = userSchema.safeParse(formData);
    
    if (!result.success) {
      return {
        success: false,
        error: {
          message: "Invalid form data",
          code: StatusCodes.BAD_REQUEST,
          formErrors: result.error.flatten(),
        },
      };
    }
    
    const { name, email, password } = result.data;
    
    // Perform database operation
    const supabase = await createAdminClient();
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      user_metadata: { name },
    });
    
    if (error) {
      throw error;
    }
    
    // Revalidate the users page
    revalidatePath("/dashboard/user");
    
    return { success: true, data };
  } catch (error: any) {
    console.error("Error creating user:", error);
    
    return {
      success: false,
      error: {
        message: error.message || "Failed to create user",
        code: StatusCodes.INTERNAL_SERVER_ERROR,
      },
    };
  }
}
```

## Best Practices

- Prefer controlled inputs
- Validate on blur when appropriate
- Show validation errors only after interaction
- Provide clear error messages
- Use form-level and field-level validation
- Implement proper form accessibility
- Handle async validation loading states
- Group related form fields logically
- Test form validation thoroughly
- Document validation rules clearly
