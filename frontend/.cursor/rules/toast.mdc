---
description: 
globs: 
alwaysApply: true
---
---
description: <PERSON><PERSON> định sử dụng toast từ thư viện sonner cho tất cả thông báo toast trong ứng dụng
globs: **/*.{ts,tsx}
---

# Quy định sử dụng Toast

## Thư viện bắt buộc

- Sử dụng **sonner** cho tất cả các thông báo toast trong ứng dụng
- Không sử dụng các thư viện toast khác như react-toastify, react-hot-toast, etc.

## Cách sử dụng cơ bản

```tsx
import { toast } from "sonner";

// Thông báo thành công
toast.success("Đã lưu thành công!");

// Thông báo lỗi
toast.error("Đã xảy ra lỗi, vui lòng thử lại");

// Thông báo cảnh báo
toast.warning("Hành động này không thể hoàn tác");

// Thông báo thông tin
toast.info("Dữ liệu đang được cập nhật");
```

## Quy định nội dung

- Viết thông báo ngắn gọn, súc tích
- Sử dụng ngôn ngữ thân thiện với người dùng
- Luôn thông báo kết quả của các hành động người dùng (thành công/thất bại)
- Đối với lỗi, nên cung cấp hướng dẫn để người dùng có thể khắc phục

## Thời điểm sử dụng

- Sau khi gửi form thành công hoặc thất bại
- Sau khi thực hiện các thao tác CRUD
- Khi cần thông báo về trạng thái hệ thống (bảo trì, cập nhật)
- Khi cần thông báo người dùng về các thay đổi quan trọng

## Tích hợp với server actions

```tsx
"use server";

import { revalidatePath } from "next/cache";

export async function createItem(formData: FormData) {
  try {
    // Xử lý logic tạo item
    
    // Revalidate đường dẫn
    revalidatePath("/items");
    
    // Trả về kết quả thành công
    return { success: true, message: "Đã tạo mục thành công" };
  } catch (error) {
    // Trả về kết quả lỗi
    return {
      success: false,
      message: "Không thể tạo mục. Vui lòng thử lại."
    };
  }
}

// Ở phía client component:
const handleSubmit = async (formData: FormData) => {
  const result = await createItem(formData);
  
  if (result.success) {
    toast.success(result.message);
  } else {
    toast.error(result.message);
  }
};
```

## Tránh sử dụng

- Không hiển thị quá nhiều toast cùng lúc
- Không sử dụng toast cho thông báo quan trọng cần xác nhận từ người dùng (sử dụng modal hoặc dialog thay thế)
- Không đặt nhiều action trong một toast