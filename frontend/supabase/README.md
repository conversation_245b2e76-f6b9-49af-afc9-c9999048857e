# Supabase User Management System

This directory contains the Supabase configuration and migrations for the user management system.

## Database Schema

The system uses a unified `users` table that stores all user types (customer, technician, staff, admin) with a role field to distinguish between them. This approach simplifies user management while maintaining proper separation of concerns.

### Users Table

The `users` table is linked to Supabase Auth's `auth.users` table via the `id` field, which is a UUID that matches the auth user's ID.

| Field      | Type              | Description                                   |
| ---------- | ----------------- | --------------------------------------------- |
| id         | UUID              | Primary key, references auth.users(id)        |
| name       | VARCHAR(100)      | User's full name                              |
| email      | VARCHAR(255)      | User's email (unique)                         |
| phone      | VARCHAR(20)       | User's phone number (unique)                  |
| address    | VARCHAR(255)      | User's address (optional)                     |
| role       | VARCHAR(20)       | User role: customer, technician, staff, admin |
| status     | VARCHAR(20)       | User status: active, inactive, pending        |
| avatar_url | TEXT              | URL to user's avatar image (optional)         |
| rating     | NUMERIC(3,1)      | Rating for technicians (0.0-5.0)              |
| exp        | INTEGER           | Years of experience for technicians           |
| certs      | TEXT[]            | Array of certifications for technicians       |
| created_at | TIMESTAMP WITH TZ | When the user was created                     |
| updated_at | TIMESTAMP WITH TZ | When the user was last updated                |

## Row Level Security (RLS)

The system implements Row Level Security to ensure users can only access and modify data they're authorized to:

- **Admins**: Can perform all operations on all users
- **Staff**: Can view all users but only update customer profiles
- **Technicians**: Can only view and update their own profile
- **Customers**: Can only view and update their own profile

## Automatic User Profile Creation

When a new user signs up through Supabase Auth, a trigger automatically creates a corresponding profile in the `users` table. This ensures that every authenticated user has a profile.

## UserService

The `UserService` class in `src/services/UserService.ts` provides methods to interact with the user management system:

- `getUsers()`: Get users with pagination and filtering
- `getUserById()`: Get a user by ID
- `createUser()`: Create a new user with Supabase Auth and profile
- `updateUser()`: Update a user profile
- `deleteUser()`: Delete a user (both auth and profile)
- `getUsersByRole()`: Get users by role
- `updateUserStatus()`: Update user status

## Usage Examples

### Creating a new user

```typescript
import { userService } from "@/services/UserService";

// Create a new customer
const newCustomer = await userService.createUser({
  name: "John Doe",
  email: "<EMAIL>",
  phone: "+1234567890",
  address: "123 Main St",
  role: "customer",
  password: "securePassword123",
});

// Create a new technician
const newTechnician = await userService.createUser({
  name: "Jane Smith",
  email: "<EMAIL>",
  phone: "+0987654321",
  address: "456 Tech Ave",
  role: "technician",
  exp: 5,
  certs: ["Certified HVAC Technician", "EPA 608 Certification"],
  password: "securePassword123",
});
```

### Updating a user

```typescript
// Update a user's profile
const updatedUser = await userService.updateUser(userId, {
  name: "John Updated",
  phone: "+1234567890",
  address: "789 New Address",
});

// Update a user's status
const deactivatedUser = await userService.updateUserStatus(userId, "inactive");
```

### Getting users

```typescript
// Get all users with pagination and filtering
const { items, total } = await userService.getUsers({
  page: 1,
  limit: 10,
  search: "john",
  role: "customer",
  status: "active",
});

// Get a specific user
const user = await userService.getUserById(userId);

// Get all technicians
const technicians = await userService.getUsersByRole("technician");
```

## Migrations

The migrations in this directory set up the database schema and security policies. To apply these migrations:

1. Make sure you have the Supabase CLI installed
2. Run `supabase db push` to apply the migrations to your Supabase project
