-- Drop triggers first
DROP TRIGGER IF EXISTS update_services_updated_at ON public.services;

-- Drop RLS policies
DROP POLICY IF EXISTS admin_all ON public.services;
DROP POLICY IF EXISTS staff_all ON public.services;
DROP POLICY IF EXISTS technician_view ON public.services;
DROP POLICY IF EXISTS customer_view_active ON public.services;

-- Drop indexes
DROP INDEX IF EXISTS services_category_idx;
DROP INDEX IF EXISTS services_is_active_idx;
DROP INDEX IF EXISTS services_name_idx;

-- Finally drop the table
DROP TABLE IF EXISTS public.services; 