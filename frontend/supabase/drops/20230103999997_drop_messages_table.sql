-- Drop messages table migration

-- Drop triggers first
DROP TRIGGER IF EXISTS update_messages_updated_at ON public.messages;
DROP TRIGGER IF EXISTS update_conversation_on_new_message ON public.messages;

-- Drop functions
DROP FUNCTION IF EXISTS public.update_messages_updated_at();
DROP FUNCTION IF EXISTS public.update_conversation_last_message();

-- Drop RLS policies
DROP POLICY IF EXISTS admin_staff_all ON public.messages;
DROP POLICY IF EXISTS user_view_conversation_messages ON public.messages;
DROP POLICY IF EXISTS user_insert_message ON public.messages;

-- Drop indexes
DROP INDEX IF EXISTS messages_conversation_id_idx;
DROP INDEX IF EXISTS messages_sender_id_idx;
DROP INDEX IF EXISTS messages_created_at_idx;
DROP INDEX IF EXISTS messages_conversation_created_idx;

-- Finally drop the table
DROP TABLE IF EXISTS public.messages; 