-- Drop conversations table migration

-- Drop triggers first
DROP TRIGGER IF EXISTS update_conversations_updated_at ON public.conversations;

-- Drop functions
DROP FUNCTION IF EXISTS public.update_conversations_updated_at();

-- Drop RLS policies
DROP POLICY IF EXISTS admin_staff_all ON public.conversations;
DROP POLICY IF EXISTS user_view_own ON public.conversations;
DROP POLICY IF EXISTS user_update_own ON public.conversations;

-- Drop indexes
DROP INDEX IF EXISTS conversations_user_id_idx;
DROP INDEX IF EXISTS conversations_user_role_idx;
DROP INDEX IF EXISTS conversations_last_message_at_idx;
DROP INDEX IF EXISTS unique_user_conversation_idx;

-- Finally drop the table
DROP TABLE IF EXISTS public.conversations; 