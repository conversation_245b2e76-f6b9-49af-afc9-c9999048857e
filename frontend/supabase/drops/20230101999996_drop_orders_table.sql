-- Drop orders table migration

-- First, drop all related policies
drop policy if exists "Users can view their own orders" on public.orders;
drop policy if exists "Authenticated users can create orders" on public.orders;
drop policy if exists "Admins, staff, and assigned technicians can update orders" on public.orders;
drop policy if exists "Only admins and staff can delete orders" on public.orders;

-- Drop indexes
drop index if exists idx_orders_customer_id;
drop index if exists idx_orders_technician_id;
drop index if exists idx_orders_service_id;
drop index if exists idx_orders_status;
drop index if exists idx_orders_scheduled_at;

-- Drop the table
drop table if exists public.orders; 