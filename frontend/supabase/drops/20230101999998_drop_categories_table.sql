-- Drop triggers first
DROP TRIGGER IF EXISTS update_categories_updated_at ON public.categories;

-- Drop functions
DROP FUNCTION IF EXISTS public.update_updated_at_column();

-- Drop RLS policies
DROP POLICY IF EXISTS admin_all ON public.categories;
DROP POLICY IF EXISTS staff_all ON public.categories;
DROP POLICY IF EXISTS technician_view ON public.categories;
DROP POLICY IF EXISTS customer_view_active ON public.categories;

-- Drop indexes
DROP INDEX IF EXISTS categories_slug_idx;
DROP INDEX IF EXISTS categories_is_active_idx;
DROP INDEX IF EXISTS categories_name_idx;
DROP INDEX IF EXISTS categories_sort_order_idx;

-- Finally drop the table
DROP TABLE IF EXISTS public.categories; 