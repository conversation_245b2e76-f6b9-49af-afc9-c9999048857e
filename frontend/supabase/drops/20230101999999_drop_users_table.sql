-- Drop triggers first
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Drop functions
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Drop RLS policies
DROP POLICY IF EXISTS admin_all ON public.users;
DROP POLICY IF EXISTS staff_view_all ON public.users;
DROP POLICY IF EXISTS staff_update_customers ON public.users;
DROP POLICY IF EXISTS user_view_own ON public.users;
DROP POLICY IF EXISTS user_update_own ON public.users;

-- Drop indexes
DROP INDEX IF EXISTS users_role_idx;
DROP INDEX IF EXISTS users_email_idx;
DROP INDEX IF EXISTS users_phone_idx;

-- Finally drop the table
DROP TABLE IF EXISTS public.users; 