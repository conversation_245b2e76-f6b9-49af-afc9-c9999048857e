-- Create orders table for service bookings/appointments
-- This table tracks service orders, connecting customers, technicians and services

create table if not exists public.orders (
    -- Primary key
    id uuid not null primary key default uuid_generate_v4(),
    
    -- Foreign keys
    customer_id uuid not null references public.users(id) on delete cascade,
    technician_id uuid references public.users(id) on delete set null,
    service_id uuid not null references public.services(id) on delete cascade,
    
    -- Order details
    status text not null check (status in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rejected')),
    amount decimal(10, 2) not null default 0, -- Total amount for the order
    scheduled_at timestamptz not null,
    completed_at timestamptz,
    note text,
    order_data jsonb,
    
    -- Timestamps
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

-- Add comments to the table and columns
comment on table public.orders is 'Table storing service booking/order information';
comment on column public.orders.id is 'Unique identifier for the order';
comment on column public.orders.customer_id is 'Reference to the customer who placed the order';
comment on column public.orders.technician_id is 'Reference to the technician assigned to the order';
comment on column public.orders.service_id is 'Reference to the service being ordered';
comment on column public.orders.status is 'Current status of the order (pending, confirmed, in_progress, completed, cancelled, rejected)';
comment on column public.orders.amount is 'Total amount for the order in currency units';
comment on column public.orders.scheduled_at is 'The date and time when the service is scheduled';
comment on column public.orders.completed_at is 'The date and time when the service was completed';
comment on column public.orders.note is 'Additional notes or comments about the order';
comment on column public.orders.order_data is 'JSON field for additional order-related data';
comment on column public.orders.created_at is 'Timestamp when the order was created';
comment on column public.orders.updated_at is 'Timestamp when the order was last updated';

-- Create indexes for foreign keys and common query patterns
create index if not exists idx_orders_customer_id on public.orders(customer_id);
create index if not exists idx_orders_technician_id on public.orders(technician_id);
create index if not exists idx_orders_service_id on public.orders(service_id);
create index if not exists idx_orders_status on public.orders(status);
create index if not exists idx_orders_scheduled_at on public.orders(scheduled_at);
create index if not exists idx_orders_amount on public.orders(amount);

-- Add RLS policies
alter table public.orders enable row level security;

-- Create policy for viewing orders (admins and staff can see all, customers and technicians can only see their own)
create policy "Users can view their own orders"
    on public.orders
    for select
    using (
        (auth.uid() = customer_id) or 
        (auth.uid() = technician_id) or 
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff')
    );

-- Create policy for inserting orders (only authenticated users)
create policy "Authenticated users can create orders"
    on public.orders
    for insert
    with check (auth.role() = 'authenticated');

-- Create policy for updating orders (admins, staff, and the assigned technician)
create policy "Admins, staff, and assigned technicians can update orders"
    on public.orders
    for update
    using (
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff') or 
        (auth.uid() = technician_id)
    );

-- Create policy for deleting orders (only admins and staff)
create policy "Only admins and staff can delete orders"
    on public.orders
    for delete
    using (
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff')
    );

-- Create function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_orders_updated_at
BEFORE UPDATE ON public.orders
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column(); 