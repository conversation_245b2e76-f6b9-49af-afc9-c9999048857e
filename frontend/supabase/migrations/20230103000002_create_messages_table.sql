-- Create messages table
CREATE TABLE IF NOT EXISTS public.messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_system BOOLEAN DEFAULT FALSE, -- Identifies if message was sent by system (admin/staff)
  read_at TIMESTAMP WITH TIME ZONE,
  message_data JSONB DEFAULT '{}'::jsonb, -- For future extensions (file uploads, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster searches and pagination
CREATE INDEX IF NOT EXISTS messages_conversation_id_idx ON public.messages(conversation_id);
CREATE INDEX IF NOT EXISTS messages_sender_id_idx ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS messages_created_at_idx ON public.messages(created_at);
CREATE INDEX IF NOT EXISTS messages_conversation_created_idx ON public.messages(conversation_id, created_at);

-- Set up Row Level Security (RLS)
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Create policies for different user roles
-- Admin and staff can do everything
CREATE POLICY admin_staff_all ON public.messages
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' IN ('admin', 'staff')
  );

-- Users can see messages in their conversations
CREATE POLICY user_view_conversation_messages ON public.messages
  FOR SELECT
  TO authenticated
  USING (
    conversation_id IN (
      SELECT id FROM public.conversations WHERE user_id = auth.uid()
    )
  );

-- Users can insert messages to their conversations
CREATE POLICY user_insert_message ON public.messages
  FOR INSERT
  TO authenticated
  WITH CHECK (
    (sender_id = auth.uid()) AND
    (conversation_id IN (
      SELECT id FROM public.conversations WHERE user_id = auth.uid()
    ))
  );

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_messages_updated_at
BEFORE UPDATE ON public.messages
FOR EACH ROW
EXECUTE FUNCTION update_messages_updated_at();

-- Create function to update conversation last_message when a new message is inserted
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the conversation with the latest message
  UPDATE public.conversations
  SET 
    last_message = NEW.content,
    last_message_at = NEW.created_at
  WHERE id = NEW.conversation_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the conversation last_message
CREATE TRIGGER update_conversation_on_new_message
AFTER INSERT ON public.messages
FOR EACH ROW
EXECUTE FUNCTION update_conversation_last_message();

COMMENT ON TABLE public.messages IS 'Stores individual chat messages';
COMMENT ON COLUMN public.messages.conversation_id IS 'Reference to the conversation this message belongs to';
COMMENT ON COLUMN public.messages.sender_id IS 'The user ID of the message sender';
COMMENT ON COLUMN public.messages.content IS 'The text content of the message';
COMMENT ON COLUMN public.messages.is_system IS 'Indicates if the message is from system (admin/staff)';
COMMENT ON COLUMN public.messages.read_at IS 'When the message was read by the recipient';
COMMENT ON COLUMN public.messages.message_data IS 'Additional message data for future extensions'; 