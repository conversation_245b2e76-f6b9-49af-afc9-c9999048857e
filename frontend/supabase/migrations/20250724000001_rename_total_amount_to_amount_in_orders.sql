-- Migration: Rename total_amount column to amount in orders table
-- Date: 2025-07-24
-- Description: Rename the total_amount column to amount for consistency with payment integration

-- Step 1: Add the new amount column
ALTER TABLE orders ADD COLUMN IF NOT EXISTS amount DECIMAL(10,2);

-- Step 2: Copy data from total_amount to amount
UPDATE orders SET amount = total_amount WHERE amount IS NULL;

-- Step 3: Make amount column NOT NULL with same constraints as total_amount
ALTER TABLE orders ALTER COLUMN amount SET NOT NULL;

-- Step 4: Add check constraint if it existed on total_amount
-- (Assuming there was a check constraint for positive amounts)
ALTER TABLE orders ADD CONSTRAINT orders_amount_positive CHECK (amount >= 0);

-- Step 5: Drop the old total_amount column
ALTER TABLE orders DROP COLUMN IF EXISTS total_amount;

-- Step 6: Add comment to document the change
COMMENT ON COLUMN orders.amount IS 'Order amount in USD (renamed from total_amount for payment integration consistency)';