-- Create services table
CREATE TABLE IF NOT EXISTS public.services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL,
  duration INTEGER NOT NULL, -- Duration in minutes
  category_id UUID NOT NULL REFERENCES public.categories(id),
  is_active BOOLEAN DEFAULT true,
  service_data JSONB DEFAULT '{}'::jsonb, -- Store additional service data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster searches
CREATE INDEX IF NOT EXISTS services_category_id_idx ON public.services(category_id);
CREATE INDEX IF NOT EXISTS services_is_active_idx ON public.services(is_active);
CREATE INDEX IF NOT EXISTS services_name_idx ON public.services(name);

-- Set up Row Level Security (RLS)
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;

-- Create policies for different user roles
-- <PERSON><PERSON> can do everything
CREATE POLICY admin_all ON public.services
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin'
  );

-- Staff can view all services and modify them
CREATE POLICY staff_all ON public.services
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'staff'
  );

-- Technicians can view all services
CREATE POLICY technician_view ON public.services
  FOR SELECT
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'technician'
  );

-- Customers can view active services
CREATE POLICY customer_view_active ON public.services
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'customer') AND
    is_active = true
  );

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_services_updated_at
BEFORE UPDATE ON public.services
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add default services
INSERT INTO public.services (name, description, price, duration, category_id)
VALUES 
  ('Oil Change', 'Standard oil change service with filter replacement', 49.99, 30, 
   (SELECT id FROM public.categories WHERE slug = 'engine')),
  ('Brake Inspection', 'Complete brake system inspection', 29.99, 45, 
   (SELECT id FROM public.categories WHERE slug = 'brakes')),
  ('Tire Rotation', 'Rotate tires to ensure even wear', 19.99, 20, 
   (SELECT id FROM public.categories WHERE slug = 'tires')),
  ('Engine Diagnostics', 'Computer diagnostics for engine issues', 79.99, 60, 
   (SELECT id FROM public.categories WHERE slug = 'engine')),
  ('Wheel Alignment', 'Adjust wheels to factory specifications', 89.99, 45, 
   (SELECT id FROM public.categories WHERE slug = 'tires'))
ON CONFLICT DO NOTHING;

COMMENT ON TABLE public.services IS 'Vehicle repair and maintenance services'; 