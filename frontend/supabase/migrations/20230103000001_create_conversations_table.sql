-- Create conversations table
CREATE TABLE IF NOT EXISTS public.conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  user_role TEXT NOT NULL CHECK (user_role IN ('customer', 'technician')),
  last_message TEXT,
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE,
  conversation_data JSONB DEFAULT '{}'::jsonb, -- Store additional conversation data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster searches
CREATE INDEX IF NOT EXISTS conversations_user_id_idx ON public.conversations(user_id);
CREATE INDEX IF NOT EXISTS conversations_user_role_idx ON public.conversations(user_role);
CREATE INDEX IF NOT EXISTS conversations_last_message_at_idx ON public.conversations(last_message_at);

-- Set up Row Level Security (RLS)
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- Create policies for different user roles
-- Admin and staff can do everything
CREATE POLICY admin_staff_all ON public.conversations
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' IN ('admin', 'staff')
  );

-- Users can view and update their own conversations
CREATE POLICY user_view_own ON public.conversations
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid()
  );

CREATE POLICY user_update_own ON public.conversations
  FOR UPDATE
  TO authenticated
  USING (
    user_id = auth.uid()
  );

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_conversations_updated_at
BEFORE UPDATE ON public.conversations
FOR EACH ROW
EXECUTE FUNCTION update_conversations_updated_at();

-- Add constraint to ensure one conversation per user
CREATE UNIQUE INDEX IF NOT EXISTS unique_user_conversation_idx 
ON public.conversations(user_id);

COMMENT ON TABLE public.conversations IS 'Stores chat conversations between users and system';
COMMENT ON COLUMN public.conversations.user_id IS 'The user ID of the customer or technician';
COMMENT ON COLUMN public.conversations.user_role IS 'The role of the user (customer or technician)';
COMMENT ON COLUMN public.conversations.last_message IS 'The text of the last message';
COMMENT ON COLUMN public.conversations.last_message_at IS 'When the last message was sent';
COMMENT ON COLUMN public.conversations.read_at IS 'When the conversation was last read by the user'; 