-- Migration file to add code column to orders table

-- Add the code column to the orders table
ALTER TABLE public.orders
ADD COLUMN IF NOT EXISTS code TEXT;

-- Make the code column NOT NULL for existing data
UPDATE public.orders
SET code = CONCAT(
  TO_CHAR(created_at, 'YYMMDD'),
  LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0')
)
WHERE code IS NULL;

-- Finally add NOT NULL constraint and create index
ALTER TABLE public.orders
ALTER COLUMN code SET NOT NULL;

-- Add unique constraint to ensure code is always unique
ALTER TABLE public.orders
ADD CONSTRAINT orders_code_unique UNIQUE (code);

-- Create index for code
CREATE INDEX IF NOT EXISTS idx_orders_code ON public.orders(code);

-- Add comment
COMMENT ON COLUMN public.orders.code IS 'Unique order code displayed to users (auto-generated)'; 