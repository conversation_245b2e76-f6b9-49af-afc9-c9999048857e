-- Create payments table for payment processing
-- This table tracks payments for orders using Stripe

create table if not exists public.payments (
    -- Primary key
    id text not null primary key, -- Stripe PaymentIntent ID
    
    -- Foreign keys
    order_id uuid not null references public.orders(id) on delete cascade,
    customer_id uuid not null references public.users(id) on delete cascade,
    
    -- Payment details
    stripe_payment_intent_id text not null unique,
    amount integer not null, -- Amount in smallest currency unit (cents for USD)
    currency text not null default 'usd',
    status text not null check (status in ('pending', 'requires_payment_method', 'requires_confirmation', 'requires_action', 'processing', 'requires_capture', 'cancelled', 'succeeded', 'failed')),
    
    -- Payment method information
    payment_method_type text, -- card, apple_pay, google_pay, etc.
    payment_method_details jsonb,
    
    -- Metadata and additional info
    metadata jsonb,
    failure_reason text,
    
    -- Timestamps
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

-- Add comments to the table and columns
comment on table public.payments is 'Table storing payment information using Stripe';
comment on column public.payments.id is 'Stripe PaymentIntent ID used as primary key';
comment on column public.payments.order_id is 'Reference to the order being paid for';
comment on column public.payments.customer_id is 'Reference to the customer making the payment';
comment on column public.payments.stripe_payment_intent_id is 'Stripe PaymentIntent ID for tracking';
comment on column public.payments.amount is 'Payment amount in smallest currency unit (cents for USD)';
comment on column public.payments.currency is 'Currency code (e.g., usd, eur)';
comment on column public.payments.status is 'Current status of the payment from Stripe';
comment on column public.payments.payment_method_type is 'Type of payment method used (card, apple_pay, google_pay, etc.)';
comment on column public.payments.payment_method_details is 'JSON field for payment method details';
comment on column public.payments.metadata is 'Additional metadata for the payment';
comment on column public.payments.failure_reason is 'Reason for payment failure if applicable';
comment on column public.payments.created_at is 'Timestamp when the payment was created';
comment on column public.payments.updated_at is 'Timestamp when the payment was last updated';

-- Create indexes for foreign keys and common query patterns
create index if not exists idx_payments_order_id on public.payments(order_id);
create index if not exists idx_payments_customer_id on public.payments(customer_id);
create index if not exists idx_payments_stripe_payment_intent_id on public.payments(stripe_payment_intent_id);
create index if not exists idx_payments_status on public.payments(status);
create index if not exists idx_payments_created_at on public.payments(created_at);

-- Add RLS policies
alter table public.payments enable row level security;

-- Create policy for viewing payments (admins, staff, and payment owner)
create policy "Users can view their own payments"
    on public.payments
    for select
    using (
        (auth.uid() = customer_id) or 
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff')
    );

-- Create policy for inserting payments (only authenticated users via backend service)
create policy "Service role can create payments"
    on public.payments
    for insert
    with check (true); -- Backend service will handle validation

-- Create policy for updating payments (only backend service)
create policy "Service role can update payments"
    on public.payments
    for update
    using (true); -- Backend service will handle validation

-- Create policy for deleting payments (only admins and staff)
create policy "Only admins and staff can delete payments"
    on public.payments
    for delete
    using (
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff')
    );

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON public.payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();