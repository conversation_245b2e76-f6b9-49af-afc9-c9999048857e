-- Update orders table to include payment-related fields and statuses
-- This migration adds payment status and updates the status enum

-- First, update the status check constraint to include 'paid'
alter table public.orders drop constraint if exists orders_status_check;
alter table public.orders add constraint orders_status_check 
    check (status in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rejected', 'paid'));

-- Add payment status column
alter table public.orders add column if not exists payment_status text 
    check (payment_status in ('unpaid', 'pending', 'paid', 'failed', 'refunded')) 
    default 'unpaid';

-- Rename amount to total_amount for clarity
alter table public.orders rename column amount to total_amount;

-- Add payment-related metadata
alter table public.orders add column if not exists payment_metadata jsonb;

-- Update comments
comment on column public.orders.status is 'Current status of the order (pending, confirmed, in_progress, completed, cancelled, rejected, paid)';
comment on column public.orders.payment_status is 'Payment status (unpaid, pending, paid, failed, refunded)';
comment on column public.orders.total_amount is 'Total amount for the order in currency units';
comment on column public.orders.payment_metadata is 'JSON field for payment-related metadata';

-- Create index for payment status
create index if not exists idx_orders_payment_status on public.orders(payment_status);

-- Update the update policy to allow customers to view payment status
drop policy if exists "Users can view their own orders" on public.orders;
create policy "Users can view their own orders"
    on public.orders
    for select
    using (
        (auth.uid() = customer_id) or 
        (auth.uid() = technician_id) or 
        (auth.jwt() ->> 'role' = 'admin') or 
        (auth.jwt() ->> 'role' = 'staff')
    );