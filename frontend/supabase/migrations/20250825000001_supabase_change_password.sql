-- Drop existing function with old signature if exists
DROP FUNCTION IF EXISTS public.change_user_password(VARCHAR, VARCHAR);
DROP FUNCTION IF EXISTS public.change_user_password(VARCHAR);
DROP FUNCTION IF EXISTS public.change_user_password(TEXT);

-- Simple function to change user password
CREATE OR REPLACE FUNCTION public.change_user_password(
  new_plain_password TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_encrypted TEXT;
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'User not authenticated');
  END IF;
  
  -- Get current encrypted password
  SELECT encrypted_password INTO current_encrypted
  FROM auth.users
  WHERE id = auth.uid();
  
  -- Check if new password is same as current password
  IF current_encrypted IS NOT NULL AND crypt(new_plain_password, current_encrypted) = current_encrypted THEN
    RETURN json_build_object('success', false, 'message', 'New password must be different from current password');
  END IF;
  
  -- Update password directly
  UPDATE auth.users
  SET 
    encrypted_password = crypt(new_plain_password, gen_salt('bf'))
  WHERE id = auth.uid();
  
  -- Check if update was successful
  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'message', 'Failed to update password');
  END IF;
  
  RETURN json_build_object('success', true, 'message', 'Password changed successfully');
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.change_user_password TO authenticated;