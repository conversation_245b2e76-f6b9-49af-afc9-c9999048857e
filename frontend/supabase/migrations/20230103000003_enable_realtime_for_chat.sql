-- Enable realtime for chat tables
-- This enables the realtime functionality for the conversations and messages tables
-- allowing clients to subscribe to changes via Supabase Realtime

BEGIN;

-- First, make sure the supabase_realtime publication exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
  ) THEN
    CREATE PUBLICATION supabase_realtime;
  END IF;
END
$$;

-- Add tables to the publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;

-- Make sure the realtime extension is enabled
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";

COMMIT;

COMMENT ON PUBLICATION supabase_realtime IS 'Publication for realtime functionality, including chat messages and conversations'; 