-- Create categories table
CREATE TABLE IF NOT EXISTS public.categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT NOT NULL UNIQUE,
  icon TEXT,
  sort_order INTEGER,
  is_active BOOLEAN DEFAULT true,
  category_data JSONB DEFAULT '{}'::jsonb, -- Store additional category data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster searches
CREATE INDEX IF NOT EXISTS categories_slug_idx ON public.categories(slug);
CREATE INDEX IF NOT EXISTS categories_is_active_idx ON public.categories(is_active);
CREATE INDEX IF NOT EXISTS categories_name_idx ON public.categories(name);
CREATE INDEX IF NOT EXISTS categories_sort_order_idx ON public.categories(sort_order);

-- Set up Row Level Security (RLS)
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- Create policies for different user roles
-- Ad<PERSON> can do everything
CREATE POLICY admin_all ON public.categories
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin'
  );

-- Staff can view all categories and modify them
CREATE POLICY staff_all ON public.categories
  FOR ALL
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'staff'
  );

-- Technicians can view all categories
CREATE POLICY technician_view ON public.categories
  FOR SELECT
  TO authenticated
  USING (
    (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'technician'
  );

-- Customers can view active categories
CREATE POLICY customer_view_active ON public.categories
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'customer') AND
    is_active = true
  );

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_categories_updated_at
BEFORE UPDATE ON public.categories
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add default categories
INSERT INTO public.categories (name, description, slug, icon, sort_order, is_active)
VALUES 
  ('Engine', 'Engine repair and maintenance services', 'engine', '🔧', 1, true),
  ('Brakes', 'Brake system services', 'brakes', '🛑', 2, true),
  ('Tires', 'Tire replacement and maintenance', 'tires', '🛞', 3, true),
  ('Electrical', 'Electrical systems diagnosis and repair', 'electrical', '⚡', 4, true),
  ('Transmission', 'Transmission repair and maintenance', 'transmission', '⚙️', 5, true)
ON CONFLICT DO NOTHING;

COMMENT ON TABLE public.categories IS 'Vehicle repair service categories'; 