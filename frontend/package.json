{"name": "vtech-admin", "version": "1.0.0", "private": true, "author": {"name": "LEDHCG", "url": "https://github.com/ledhcg"}, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "prepare": "husky", "server:start:production": "yarn next build && pm2 start node_modules/next/dist/bin/next --name vtech-admin -- start -p 24000", "server:stop:production": "pm2 stop vtech-admin", "server:restart:production": "yarn next build && pm2 restart node_modules/next/dist/bin/next --name vtech-admin -- start -p 24000", "server:delete:production": "pm2 delete vtech-admin"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.10.7", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "@types/node": "20.5.7", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "@types/react-input-mask": "^3.0.6", "autoprefixer": "10.4.15", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.0", "date-fns": "^2.30.0", "eslint": "8.48.0", "eslint-config-next": "^14.0.1", "framer-motion": "^12.3.1", "http-status-codes": "^2.3.0", "kbar": "^0.1.0-beta.45", "lucide-react": "^0.483.0", "match-sorter": "^6.3.4", "next": "^14.2.3", "next-themes": "^0.2.1", "nextjs-toploader": "^1.6.12", "nuqs": "^1.19.1", "postcss": "8.4.28", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "react-input-mask": "^2.0.4", "react-responsive": "^10.0.0", "recharts": "^2.12.7", "sharp": "^0.32.5", "sonner": "^1.5.0", "sort-by": "^1.2.0", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "use-debounce": "^10.0.4", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@faker-js/faker": "^9.0.3", "@types/sort-by": "^1.2.3", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "^0.5.14"}}