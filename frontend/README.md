This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Default Admin User

The application comes with a default admin user configuration. When setting up the application for the first time, the system will create this admin user automatically.

Default admin credentials are configured in the `.env`, `.env.development`, and `.env.production` files:

```
NEXT_PUBLIC_ADMIN_DEFAULT_EMAIL=<EMAIL>
NEXT_PUBLIC_ADMIN_DEFAULT_PASSWORD=Admin@123
NEXT_PUBLIC_ADMIN_DEFAULT_ROLE=admin
NEXT_PUBLIC_ADMIN_DEFAULT_NAME=System Admin
NEXT_PUBLIC_ADMIN_DEFAULT_PHONE=1234567890
NEXT_PUBLIC_ADMIN_SECRET=87ava
```

### Creating Default Admin User

To create the default admin user, access the following URL after starting the application:

```
http://localhost:3000/api/admin/init?secret=87ava
```

The `secret` parameter must match the `NEXT_PUBLIC_ADMIN_SECRET` value in your environment file.

After successful initialization, you can log in with:

- Email: <EMAIL>
- Password: Admin@123

For production, replace `localhost:3000` with your production domain.

You can use these credentials to log in or customize them in your environment files before starting the application.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
