import { NavItem } from "@/types";
import {
  Check,
  Clock,
  PackageCheck,
  PersonStanding,
  Truck,
} from "lucide-react";
import { X } from "lucide-react";

export const navItems: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard/overview",
    icon: "dashboard",
    isActive: false,
    shortcut: ["d", "d"],
    items: [], // Empty array as there are no child items for Dashboard
  },
  {
    title: "Order",
    url: "/dashboard/order",
    icon: "receiptText",
    shortcut: ["o", "o"],
    isActive: false,
    items: [], // No child items
  },
  {
    title: "Category",
    url: "/dashboard/category",
    icon: "bookText",
    shortcut: ["c", "c"],
    isActive: false,
    items: [], // No child items
  },
  {
    title: "Service",
    url: "/dashboard/service",
    icon: "cog",
    shortcut: ["s", "s"],
    isActive: false,
    items: [], // No child items
  },
  {
    title: "Conversation",
    url: "/dashboard/conversation",
    icon: "messageSquare",
    shortcut: ["c", "h"],
    isActive: false,
    items: [
      {
        title: "Customer Conversation",
        url: "/dashboard/customer-conversation",
        shortcut: ["c", "c"],
      },
      {
        title: "Technician Conversation",
        url: "/dashboard/technician-conversation",
        shortcut: ["c", "t"],
      },
    ],
  },
  // {
  //   title: "Product",
  //   url: "/dashboard/product",
  //   icon: "product",
  //   shortcut: ["p", "p"],
  //   isActive: false,
  //   items: [], // No child items
  // },
  {
    title: "User",
    url: "/dashboard/user",
    icon: "user",
    shortcut: ["u", "u"],
    isActive: false,
    items: [], // No child items
  },
  // {
  //   title: "Customer",
  //   url: "/dashboard/customer",
  //   icon: "users",
  //   shortcut: ["c", "u"],
  //   isActive: false,
  //   items: [], // No child items
  // },
  // {
  //   title: "Technician",
  //   url: "/dashboard/technician",
  //   icon: "userCog",
  //   shortcut: ["t", "e"],
  //   isActive: false,
  //   items: [], // No child items
  // },
  // {
  //   title: "Video",
  //   url: "/dashboard/video",
  //   icon: "video",
  //   shortcut: ["v", "v"],
  //   isActive: false,
  //   items: [], // No child items
  // },
  // {
  //   title: "Website",
  //   url: "/dashboard/website-info",
  //   icon: "panelsTopLeft",
  //   shortcut: ["w", "w"],
  //   isActive: false,
  //   items: [], // No child items
  // },
];

export const GENDER_OPTIONS = [
  { value: "male", label: "Male", color: "blue", icon: PersonStanding },
  {
    value: "female",
    label: "Female",
    color: "pink",
    icon: PersonStanding,
  },
  {
    value: "other",
    label: "Other",
    color: "gray",
    icon: PersonStanding,
  },
];

export const ORDER_STATUS_OPTIONS = [
  { value: "pending", label: "Pending", color: "yellow", icon: Clock },
  {
    value: "confirmed",
    label: "Confirmed",
    color: "pink",
    icon: PackageCheck,
  },
  { value: "shipping", label: "Shipping", color: "blue", icon: Truck },
  {
    value: "completed",
    label: "Completed",
    color: "green",
    icon: Check,
  },
];

export const PAYMENT_STATUS_OPTIONS = [
  { value: "paid", label: "Paid", color: "green", icon: Check },
  { value: "unpaid", label: "Unpaid", color: "yellow", icon: Clock },
  { value: "refunded", label: "Refunded", color: "red", icon: X },
];

export const STATUS_OPTIONS = [
  { value: "true", label: "Active", color: "green", icon: Check },
  { value: "false", label: "Inactive", color: "red", icon: X },
];
