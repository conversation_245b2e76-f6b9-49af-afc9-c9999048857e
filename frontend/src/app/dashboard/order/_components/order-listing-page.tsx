import PageContainer from "@/components/layout/page-container";
import { buttonVariants } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { searchParamsCache, serialize } from "@/lib/searchparams";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { DataTableSkeleton } from "@/components/ui/table/data-table-skeleton";
import OrderTable from "./order-tables";
import { getOrders } from "../actions";
import { OrderStatus } from "@/types/order.types";

type TOrderListingPage = object;

export default async function OrderListingPage({}: TOrderListingPage) {
  // Get filter values from search params
  const page = searchParamsCache.get("page");
  const search = searchParamsCache.get("q");
  const pageLimit = searchParamsCache.get("limit");
  const status = searchParamsCache.get("status");
  const startDate = searchParamsCache.get("start_date");
  const endDate = searchParamsCache.get("end_date");

  const filters = {
    page,
    limit: pageLimit,
    ...(search && { search }),
    ...(status && { status: status as OrderStatus }),
    ...(startDate && { start_date: startDate }),
    ...(endDate && { end_date: endDate }),
  };

  // Fetch data with filters
  const data = await getOrders(filters);
  const totalItems = data.total;
  const items = data.items;

  // Suspense key for re-fetching when filters change
  const key = serialize({ ...filters });

  return (
    <PageContainer scrollable>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <Heading
            title={`Orders (${totalItems})`}
            description="Manage service orders"
          />

          <Link
            href={"/dashboard/order/new"}
            className={cn(buttonVariants({ variant: "default" }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />
        <Suspense
          key={key}
          fallback={<DataTableSkeleton columnCount={7} rowCount={10} />}
        >
          <OrderTable data={items} totalData={totalItems} />
        </Suspense>
      </div>
    </PageContainer>
  );
}
