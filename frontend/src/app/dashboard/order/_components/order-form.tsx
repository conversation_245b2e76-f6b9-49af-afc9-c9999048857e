"use client";

import { OrderWithRelations, OrderStatus } from "@/types/order.types";
import { createOrder, updateOrder, getServices } from "../actions";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { format } from "date-fns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Heading } from "@/components/ui/heading";
import { Textarea } from "@/components/ui/textarea";
import { getUsersByRole } from "../../user/actions";
import { UserProfile } from "@/types/user.types";
import { DateTimePicker } from "@/components/app/date-time-picker";
import PayButton from "./pay-button";

// Định nghĩa interface Service cho rõ ràng
interface Service {
  id: string;
  name: string;
  price: number;
  category_id: string;
  duration?: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Define the form schema
const formSchema = z.object({
  customer_id: z.string({ required_error: "Customer is required" }),
  technician_id: z.string().optional(),
  service_id: z.string({ required_error: "Service is required" }),
  status: z.enum(
    [
      "pending",
      "confirmed",
      "in_progress",
      "completed",
      "cancelled",
      "rejected",
    ],
    {
      required_error: "Status is required",
    }
  ),
  amount: z.preprocess(
    (val) => (val === "" ? undefined : Number(val)),
    z
      .number({ required_error: "Amount is required" })
      .min(0, "Amount must be positive")
  ),
  scheduled_at: z.date({ required_error: "Scheduled date is required" }),
  completed_at: z.date().optional().nullable(),
  note: z.string().optional(),
  vehicle_info: z
    .object({
      brand: z.string().optional(),
      model: z.string().optional(),
      year: z.string().optional(),
      license_plate: z.string().optional(),
    })
    .optional(),
  location: z.string().optional(),
  special_requests: z.string().optional(),
});

type OrderFormValues = z.infer<typeof formSchema>;

interface OrderFormProps {
  order?: OrderWithRelations;
}

export default function OrderForm({ order }: OrderFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<UserProfile[]>([]);
  const [technicians, setTechnicians] = useState<UserProfile[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  // Initialize form with default values or order data
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: order
      ? {
          customer_id: order.customer_id,
          technician_id: order.technician_id || undefined,
          service_id: order.service_id,
          status: order.status,
          amount: order.amount,
          scheduled_at: order.scheduled_at
            ? new Date(order.scheduled_at)
            : new Date(),
          completed_at: order.completed_at
            ? new Date(order.completed_at)
            : null,
          note: order.note || "",
          vehicle_info: {
            brand: order.order_data?.vehicle_info?.brand || "",
            model: order.order_data?.vehicle_info?.model || "",
            year: order.order_data?.vehicle_info?.year?.toString() || "",
            license_plate: order.order_data?.vehicle_info?.license_plate || "",
          },
          location: order.order_data?.location || "",
          special_requests: order.order_data?.special_requests || "",
        }
      : {
          customer_id: "",
          technician_id: "",
          service_id: "",
          status: "pending" as OrderStatus,
          amount: 0,
          scheduled_at: new Date(),
          completed_at: null,
          note: "",
          vehicle_info: {
            brand: "",
            model: "",
            year: "",
            license_plate: "",
          },
          location: "",
          special_requests: "",
        },
  });

  // Fetch customers, technicians and services on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersData, techniciansData, servicesData] =
          await Promise.all([
            getUsersByRole("customer"),
            getUsersByRole("technician"),
            getServices(),
          ]);
        setCustomers(customersData);
        setTechnicians(techniciansData);
        setServices(servicesData as Service[]);

        // Set initial selected service if editing
        if (order && order.service_id) {
          const service = servicesData.find((s) => s.id === order.service_id);
          if (service) {
            setSelectedService(service as Service);
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load form data");
      }
    };
    fetchData();
  }, [order]);

  // Update amount when service changes
  const handleServiceChange = (serviceId: string) => {
    const service = services.find((s) => s.id === serviceId);
    if (service) {
      setSelectedService(service);
      form.setValue("amount", service.price);
    }
  };

  // Handle form submission
  const onSubmit = async (data: OrderFormValues) => {
    try {
      setLoading(true);

      // Format the data to match the expected structure
      const orderData = {
        customer_id: data.customer_id,
        technician_id: data.technician_id || undefined,
        service_id: data.service_id,
        status: data.status,
        amount: data.amount,
        scheduled_at: data.scheduled_at.toISOString(),
        completed_at: data.completed_at
          ? data.completed_at.toISOString()
          : undefined,
        note: data.note,
        order_data: {
          vehicle_info: {
            brand: data.vehicle_info?.brand,
            model: data.vehicle_info?.model,
            year: data.vehicle_info?.year,
            license_plate: data.vehicle_info?.license_plate,
          },
          location: data.location,
          special_requests: data.special_requests,
        },
      };

      if (order) {
        // Update existing order
        await updateOrder(order.id, orderData);
        toast.success("Order updated successfully");
      } else {
        // Create new order
        await createOrder(orderData);
        toast.success("Order created successfully");
      }

      router.push("/dashboard/order");
      router.refresh();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8 pb-10">
      <div className="flex items-center justify-between">
        <Heading
          title={order ? "Edit Order" : "Create Order"}
          description={
            order ? "Update an existing order" : "Create a new order"
          }
        />
        {order && order.code && (
          <div className="bg-muted p-2 rounded-md">
            <p className="text-sm font-medium">
              Order Code: <span className="text-primary">{order.code}</span>
            </p>
          </div>
        )}
      </div>
      <Separator />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-8 w-full"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Customer Selection */}
            <FormField
              control={form.control}
              name="customer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer *</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name} ({customer.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Technician Selection */}
            <FormField
              control={form.control}
              name="technician_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Technician</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a technician" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="">None</SelectItem>
                      {technicians.map((technician) => (
                        <SelectItem key={technician.id} value={technician.id}>
                          {technician.name} ({technician.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Service Selection */}
            <FormField
              control={form.control}
              name="service_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service *</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleServiceChange(value);
                    }}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a service" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          {service.name} (${service.price.toFixed(2)})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Amount Field */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount ($) *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      disabled={loading}
                      placeholder="0.00"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e.target.valueAsNumber || 0);
                      }}
                      value={field.value}
                    />
                  </FormControl>
                  {selectedService && (
                    <div className="text-xs text-muted-foreground">
                      Default service price: ${selectedService.price.toFixed(2)}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status Selection */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status *</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Scheduled Date */}
            <FormField
              control={form.control}
              name="scheduled_at"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Scheduled Date *</FormLabel>
                  <DateTimePicker
                    date={field.value}
                    setDate={field.onChange}
                    disabled={loading}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Completed Date (optional) */}
            <FormField
              control={form.control}
              name="completed_at"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Completed Date</FormLabel>
                  <DateTimePicker
                    date={field.value || null}
                    setDate={field.onChange}
                    disabled={loading}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Vehicle Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Vehicle Brand */}
              <FormField
                control={form.control}
                name="vehicle_info.brand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <FormControl>
                      <Input
                        disabled={loading}
                        placeholder="Brand"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Vehicle Model */}
              <FormField
                control={form.control}
                name="vehicle_info.model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model</FormLabel>
                    <FormControl>
                      <Input
                        disabled={loading}
                        placeholder="Model"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Vehicle Year */}
              <FormField
                control={form.control}
                name="vehicle_info.year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year</FormLabel>
                    <FormControl>
                      <Input disabled={loading} placeholder="Year" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* License Plate */}
              <FormField
                control={form.control}
                name="vehicle_info.license_plate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>License Plate</FormLabel>
                    <FormControl>
                      <Input
                        disabled={loading}
                        placeholder="License Plate"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Additional Information</h3>
            <div className="grid grid-cols-1 gap-4">
              {/* Location */}
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input
                        disabled={loading}
                        placeholder="Service location"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Special Requests */}
              <FormField
                control={form.control}
                name="special_requests"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Special Requests</FormLabel>
                    <FormControl>
                      <Textarea
                        disabled={loading}
                        placeholder="Any special requests or requirements"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes */}
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        disabled={loading}
                        placeholder="Additional notes for this order"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex items-center justify-end space-x-4">
            <Button
              disabled={loading}
              type="button"
              variant="outline"
              onClick={() => router.push("/dashboard/order")}
            >
              Cancel
            </Button>
            <Button disabled={loading} type="submit">
              {order ? "Update Order" : "Create Order"}
            </Button>
            {order && order.status === "completed" && (
              <PayButton orderId={order.id} amount={order.amount} />
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
