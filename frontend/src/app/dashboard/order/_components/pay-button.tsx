"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "sonner";
import { CreditCard } from "lucide-react";

interface PayButtonProps {
  orderId: string;
  amount: number;
}

export default function PayButton({ orderId, amount }: PayButtonProps) {
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    try {
      setLoading(true);
      
      // Create payment intent
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.vtechautonation.com';
      
      const response = await fetch(`${API_URL}/api/payment/create-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          amount: Math.round(amount * 100), // Convert to cents
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create payment intent');
      }

      const { clientSecret, paymentIntentId } = await response.json();
      
      // For web frontend, redirect to a payment confirmation page
      // or integrate with Stripe Elements for web payment
      window.open(
        `https://js.stripe.com/v3/elements/`, 
        '_blank',
        'width=600,height=800'
      );
      
      toast.success("Payment process initiated");
      
    } catch (error) {
      console.error('Payment error:', error);
      toast.error(error instanceof Error ? error.message : 'Payment failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={loading}
      className="bg-green-600 hover:bg-green-700 text-white"
    >
      <CreditCard className="w-4 h-4 mr-2" />
      {loading ? "Processing..." : `Pay $${amount.toFixed(2)}`}
    </Button>
  );
}