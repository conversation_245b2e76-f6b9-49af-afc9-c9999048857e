"use server";

import OrderForm from "./order-form";
import PageContainer from "@/components/layout/page-container";
import { notFound } from "next/navigation";
import { unstable_noStore as noStore } from "next/cache";
import { getOrderById } from "../actions";

interface OrderViewPageProps {
  orderId?: string;
}

async function getOrder(orderId: string) {
  noStore(); // Opt out of static rendering
  try {
    const result = await getOrderById(orderId);
    return result;
  } catch (error) {
    console.error("Error fetching order:", error);
    return null;
  }
}

export default async function OrderViewPage({ orderId }: OrderViewPageProps) {
  if (orderId) {
    const order = await getOrder(orderId);
    if (!order) {
      notFound();
    }

    return (
      <PageContainer>
        <OrderForm order={order} />
      </PageContainer>
    );
  } else {
    return (
      <PageContainer>
        <OrderForm />
      </PageContainer>
    );
  }
}
