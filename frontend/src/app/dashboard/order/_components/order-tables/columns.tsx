"use client";

import { ColumnDef } from "@tanstack/react-table";
import { CellAction } from "./cell-action";
import { OrderWithRelations } from "@/types/order.types";

export const columns: ColumnDef<
  OrderWithRelations & {
    customerName: string;
    technicianName: string;
    serviceName: string;
    servicePrice: string;
    formattedAmount: string;
    statusBadge: React.ReactNode;
    scheduledDate: React.ReactNode;
    completedDate: React.ReactNode;
  }
>[] = [
  // ID column with sorting
  // {
  //   accessorKey: "id",
  //   header: "ID",
  //   cell: ({ row }) => (
  //     <div className="w-[80px] truncate">
  //       {row.original.id.substring(0, 8)}...
  //     </div>
  //   ),
  // },
  // Code column
  {
    accessorKey: "code",
    header: "CODE",
    cell: ({ row }) => <div className="font-medium">{row.original.code}</div>,
  },
  // Customer name column with sorting
  {
    accessorKey: "customerName",
    header: "CUSTOMER",
    cell: ({ row }) => <div>{row.original.customerName}</div>,
  },
  // Service name column
  {
    accessorKey: "serviceName",
    header: "SERVICE",
    cell: ({ row }) => <div>{row.original.serviceName}</div>,
  },
  // Amount column
  {
    accessorKey: "amount",
    header: "AMOUNT",
    cell: ({ row }) => <div>{row.original.formattedAmount}</div>,
  },
  // Technician name column
  {
    accessorKey: "technicianName",
    header: "TECHNICIAN",
    cell: ({ row }) => <div>{row.original.technicianName}</div>,
  },
  // Status column
  {
    accessorKey: "status",
    header: "STATUS",
    cell: ({ row }) => (
      <div className="flex items-center">{row.original.statusBadge}</div>
    ),
  },
  // Scheduled date column with sorting
  {
    accessorKey: "scheduled_at",
    header: "SCHEDULED DATE",
    cell: ({ row }) => <div>{row.original.scheduledDate}</div>,
  },
  // Created at column with sorting
  {
    accessorKey: "created_at",
    header: "CREATED AT",
    cell: ({ row }) => (
      <div className="w-[150px]">
        {new Date(row.original.created_at).toLocaleDateString()}
      </div>
    ),
  },
  // Actions column
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original} />,
  },
];
