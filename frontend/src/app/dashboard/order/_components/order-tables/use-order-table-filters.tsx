"use client";

import { useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { OrderStatus } from "@/types/order.types";
import { parseAsArrayOf, parseAsString, useQueryState } from "nuqs";
import { searchParams } from "@/lib/searchparams";
import { useCallback, useMemo } from "react";

// Placeholder hooks cho đến khi tạo các hook thực tế
const usePagination = ({ totalItems }: { totalItems: number }) => {
  return {
    pagination: <div />,
    page: 1,
    pageSize: 10,
    filterForm: <div />,
  };
};

const useSearchFilter = () => {
  return {
    searchFilter: <div />,
    searchValue: "",
    setSearchValue: (value: string) => {},
  };
};

const useStatusFilter = ({
  value,
  placeholder,
  onChange,
  options,
}: {
  value: string;
  placeholder: string;
  onChange: (value: string) => void;
  options: { label: string; value: string }[];
}) => {
  return {
    statusFilter: <div />,
  };
};

const useDateFilter = ({
  startDate,
  endDate,
  onChangeStartDate,
  onChangeEndDate,
  startDatePlaceholder,
  endDatePlaceholder,
}: {
  startDate: string;
  endDate: string;
  onChangeStartDate: (value: string) => void;
  onChangeEndDate: (value: string) => void;
  startDatePlaceholder: string;
  endDatePlaceholder: string;
}) => {
  return {
    dateFilter: <div />,
  };
};

export function useOrderTableFilters() {
  const [searchQuery, setSearchQuery] = useQueryState(
    "q",
    searchParams.q
      .withOptions({ shallow: false, throttleMs: 1000 })
      .withDefault("")
  );

  const [statusFilter, setStatusFilter] = useQueryState(
    "status",
    searchParams.status.withOptions({ shallow: false }).withDefault("")
  );

  const [startDate, setStartDate] = useQueryState(
    "start_date",
    searchParams.start_date.withOptions({ shallow: false }).withDefault("")
  );

  const [endDate, setEndDate] = useQueryState(
    "end_date",
    searchParams.end_date.withOptions({ shallow: false }).withDefault("")
  );

  const [page, setPage] = useQueryState(
    "page",
    searchParams.page.withDefault(1)
  );

  const [pageSize, setPageSize] = useQueryState(
    "limit",
    searchParams.limit.withDefault(10)
  );

  // For date range, just store the values for now
  const dateRange = { startDate, endDate };
  const setDateRange = (range: {
    startDate: string | null;
    endDate: string | null;
  }) => {
    setStartDate(range.startDate);
    setEndDate(range.endDate);
  };

  const resetFilters = useCallback(() => {
    setSearchQuery(null);
    setStatusFilter(null);
    setStartDate(null);
    setEndDate(null);
    setPage(1);
  }, [setSearchQuery, setStatusFilter, setStartDate, setEndDate, setPage]);

  const isAnyFilterActive = useMemo(() => {
    return !!searchQuery || !!statusFilter || !!startDate || !!endDate;
  }, [searchQuery, statusFilter, startDate, endDate]);

  return {
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    dateRange,
    setDateRange,
    page,
    pageSize,
    setPage,
    setPageSize,
    isAnyFilterActive,
    resetFilters,
  };
}
