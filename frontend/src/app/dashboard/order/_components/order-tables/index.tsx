"use client";

import { DataTable } from "@/components/ui/table/data-table";
import { DataTableResetFilter } from "@/components/ui/table/data-table-reset-filter";
import { DataTableSearch } from "@/components/ui/table/data-table-search";
import { DataTableFilterBox } from "@/components/ui/table/data-table-filter-box";
import { DataTableColumnVisibility } from "@/components/ui/table/data-table-column-visibility";
import { Table } from "@tanstack/react-table";
import { columns } from "./columns";
import { OrderWithRelations } from "@/types/order.types";
import { useOrderTableFilters } from "./use-order-table-filters";
import { Badge } from "@/components/ui/badge";
import { ReactNode } from "react";

// Status filter options
const STATUS_OPTIONS = [
  { label: "Pending", value: "pending" },
  { label: "Confirmed", value: "confirmed" },
  { label: "In Progress", value: "in_progress" },
  { label: "Completed", value: "completed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Rejected", value: "rejected" },
];

// Placeholder component cho đến khi tạo component thực tế
const LocaleDatePlaceholder = ({
  date,
  format,
}: {
  date: string;
  format: string;
}) => <span>{new Date(date).toLocaleString()}</span>;

// Placeholder function cho đến khi tạo function thực tế
const formatPricePlaceholder = (price: number) => `$${price.toFixed(2)}`;

interface OrderTableProps {
  data: OrderWithRelations[];
  totalData: number;
}

export default function OrderTable({ data, totalData }: OrderTableProps) {
  const {
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    isAnyFilterActive,
    resetFilters,
    setPage,
  } = useOrderTableFilters();

  // Format the data to include computed values or formatted fields
  const formattedData = data.map((order) => {
    return {
      ...order,
      customerName: order.customer?.name || "Unknown",
      technicianName: order.technician?.name || "Unassigned",
      serviceName: order.service?.name || "Unknown",
      servicePrice: formatPricePlaceholder(order.service?.price || 0),
      formattedAmount: formatPricePlaceholder(order.amount || 0),
      statusBadge: (
        <Badge
          variant={
            order.status === "completed"
              ? "default"
              : order.status === "cancelled" || order.status === "rejected"
              ? "destructive"
              : order.status === "in_progress"
              ? "outline"
              : order.status === "confirmed"
              ? "default"
              : "secondary"
          }
          className={
            order.status === "completed"
              ? "bg-green-500"
              : order.status === "in_progress"
              ? "bg-orange-500"
              : ""
          }
        >
          {order.status.replace("_", " ")}
        </Badge>
      ),
      scheduledDate: (
        <LocaleDatePlaceholder date={order.scheduled_at} format="PPP p" />
      ),
      completedDate: order.completed_at ? (
        <LocaleDatePlaceholder date={order.completed_at} format="PPP p" />
      ) : (
        "Not completed"
      ),
    };
  });

  return (
    <div className="space-y-4">
      <DataTable columns={columns} data={formattedData} totalItems={totalData}>
        {(table) => (
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4 flex-1">
              <DataTableSearch
                searchKey="all"
                searchQuery={searchQuery || ""}
                setSearchQuery={setSearchQuery}
                setPage={setPage}
              />
              <DataTableFilterBox
                filterKey="status"
                title="Status"
                options={STATUS_OPTIONS}
                setFilterValue={setStatusFilter}
                filterValue={statusFilter || ""}
              />
              <DataTableResetFilter
                isFilterActive={isAnyFilterActive}
                onReset={resetFilters}
              />
            </div>
            <DataTableColumnVisibility
              table={table}
              storageKey="order-table-column-visibility"
            />
          </div>
        )}
      </DataTable>
    </div>
  );
}
