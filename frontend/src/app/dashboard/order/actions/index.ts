"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";
import {
  OrderStatus,
  Order,
  GetOrdersFilters,
  GetOrdersResponse,
  CreateOrderData,
  UpdateOrderData,
  OrderWithRelations,
} from "@/types/order.types";
import { stringToArray } from "@/utils/string";

/**
 * Tạo mã đơn hàng dựa trên timestamp và chuỗi ngẫu nhiên
 */
function generateOrderCode(): string {
  // Lấy timestamp hiện tại và convert sang base36
  const timestampBase36 = Date.now().toString(36);

  // Tạo 4 ký tự ngẫu nhiên base36
  const randomBase36 = Math.random().toString(36).slice(2, 6).padEnd(4, "0");

  // Ghép lại và viết hoa
  return (timestampBase36 + randomBase36).toUpperCase();
}

/**
 * Get services list for dropdown menu
 */
export async function getServices() {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("services")
      .select("id, name, price, category_id")
      .eq("is_active", true)
      .order("name", { ascending: true });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching services:", error);
    return [];
  }
}

/**
 * Get orders with pagination and filtering
 */
export async function getOrders(
  filters: GetOrdersFilters
): Promise<GetOrdersResponse> {
  try {
    const supabase = await createAdminClient();

    // Start building the query for count
    let countQuery = supabase.from("orders").select("*", { count: "exact" });

    // Start building the query for data with relationships
    let query = supabase.from("orders").select(`
        *,
        customer:customer_id(id, name, email, phone),
        technician:technician_id(id, name, email, phone),
        service:service_id(id, name, price, category_id)
      `);

    // Apply filters
    if (filters.search) {
      const searchPattern = `%${filters.search}%`;
      countQuery = countQuery.or(`note.ilike.${searchPattern}`);
      query = query.or(`note.ilike.${searchPattern}`);
    }

    if (filters.status) {
      const statuses = stringToArray(filters.status);
      countQuery = countQuery.in("status", statuses);
      query = query.in("status", statuses);
    }

    if (filters.customer_id) {
      countQuery = countQuery.eq("customer_id", filters.customer_id);
      query = query.eq("customer_id", filters.customer_id);
    }

    if (filters.technician_id) {
      countQuery = countQuery.eq("technician_id", filters.technician_id);
      query = query.eq("technician_id", filters.technician_id);
    }

    if (filters.service_id) {
      countQuery = countQuery.eq("service_id", filters.service_id);
      query = query.eq("service_id", filters.service_id);
    }

    if (filters.start_date) {
      countQuery = countQuery.gte("scheduled_at", filters.start_date);
      query = query.gte("scheduled_at", filters.start_date);
    }

    if (filters.end_date) {
      countQuery = countQuery.lte("scheduled_at", filters.end_date);
      query = query.lte("scheduled_at", filters.end_date);
    }

    if (filters.min_amount) {
      countQuery = countQuery.gte("amount", filters.min_amount);
      query = query.gte("amount", filters.min_amount);
    }

    if (filters.max_amount) {
      countQuery = countQuery.lte("amount", filters.max_amount);
      query = query.lte("amount", filters.max_amount);
    }

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to).order("created_at", { ascending: false });

    // Get the count
    const { count, error: countError } = await countQuery;

    if (countError) {
      throw new Error(countError.message);
    }

    // Get the data
    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      items: (data as unknown as OrderWithRelations[]) || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw error;
  }
}

/**
 * Get an order by ID
 */
export async function getOrderById(
  orderId: string
): Promise<OrderWithRelations> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("orders")
      .select(
        `
        *,
        customer:customer_id(id, name, email, phone),
        technician:technician_id(id, name, email, phone),
        service:service_id(id, name, price, category_id)
      `
      )
      .eq("id", orderId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Order not found");
    }

    return data as unknown as OrderWithRelations;
  } catch (error) {
    console.error("Error fetching order:", error);
    throw error;
  }
}

/**
 * Create a new order
 */
export async function createOrder(orderData: CreateOrderData): Promise<Order> {
  try {
    const supabase = await createAdminClient();

    // Fetch service price if not provided
    let amount = orderData.amount;
    if (!amount) {
      const { data: serviceData, error: serviceError } = await supabase
        .from("services")
        .select("price")
        .eq("id", orderData.service_id)
        .single();

      if (serviceError) {
        throw new Error(
          `Error fetching service price: ${serviceError.message}`
        );
      }

      amount = serviceData?.price || 0;
    }

    // Generate unique order code
    const orderCode = generateOrderCode();

    const { data, error } = await supabase
      .from("orders")
      .insert({
        code: orderCode,
        customer_id: orderData.customer_id,
        technician_id: orderData.technician_id,
        service_id: orderData.service_id,
        status: orderData.status,
        amount: amount,
        scheduled_at: orderData.scheduled_at,
        completed_at: orderData.completed_at,
        note: orderData.note,
        order_data: orderData.order_data || {},
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the orders page to update the UI
    revalidatePath("/dashboard/order");

    return data;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
}

/**
 * Update an order
 */
export async function updateOrder(
  orderId: string,
  orderData: UpdateOrderData
): Promise<Order> {
  try {
    const supabase = await createAdminClient();

    // If service_id is updated but amount is not, fetch new service price
    if (orderData.service_id && orderData.amount === undefined) {
      const { data: serviceData, error: serviceError } = await supabase
        .from("services")
        .select("price")
        .eq("id", orderData.service_id)
        .single();

      if (serviceError) {
        throw new Error(
          `Error fetching service price: ${serviceError.message}`
        );
      }

      if (serviceData) {
        orderData.amount = serviceData.price;
      }
    }

    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (orderData.technician_id !== undefined) {
      updateData.technician_id = orderData.technician_id;
    }

    if (orderData.service_id !== undefined) {
      updateData.service_id = orderData.service_id;
    }

    if (orderData.status !== undefined) {
      updateData.status = orderData.status;
    }

    if (orderData.amount !== undefined) {
      updateData.amount = orderData.amount;
    }

    if (orderData.scheduled_at !== undefined) {
      updateData.scheduled_at = orderData.scheduled_at;
    }

    if (orderData.completed_at !== undefined) {
      updateData.completed_at = orderData.completed_at;
    }

    if (orderData.note !== undefined) {
      updateData.note = orderData.note;
    }

    if (orderData.order_data !== undefined) {
      updateData.order_data = orderData.order_data;
    }

    const { data, error } = await supabase
      .from("orders")
      .update(updateData)
      .eq("id", orderId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the relevant pages to update the UI
    revalidatePath("/dashboard/order");
    revalidatePath(`/dashboard/order/${orderId}`);

    return data;
  } catch (error) {
    console.error("Error updating order:", error);
    throw error;
  }
}

/**
 * Delete an order
 */
export async function deleteOrder(orderId: string): Promise<void> {
  try {
    const supabase = await createAdminClient();

    const { error } = await supabase.from("orders").delete().eq("id", orderId);

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the orders page to update the UI
    revalidatePath("/dashboard/order");
  } catch (error) {
    console.error("Error deleting order:", error);
    throw error;
  }
}

/**
 * Get orders by status
 */
export async function getOrdersByStatus(
  status: OrderStatus
): Promise<OrderWithRelations[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("orders")
      .select(
        `
        *,
        customer:customer_id(id, name, email, phone),
        technician:technician_id(id, name, email, phone),
        service:service_id(id, name, price, category_id)
      `
      )
      .eq("status", status)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return (data as unknown as OrderWithRelations[]) || [];
  } catch (error) {
    console.error(`Error fetching ${status} orders:`, error);
    throw error;
  }
}

/**
 * Get orders for a specific customer
 */
export async function getCustomerOrders(
  customerId: string
): Promise<OrderWithRelations[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("orders")
      .select(
        `
        *,
        customer:customer_id(id, name, email, phone),
        technician:technician_id(id, name, email, phone),
        service:service_id(id, name, price, category_id)
      `
      )
      .eq("customer_id", customerId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return (data as unknown as OrderWithRelations[]) || [];
  } catch (error) {
    console.error("Error fetching customer orders:", error);
    throw error;
  }
}

/**
 * Get orders assigned to a specific technician
 */
export async function getTechnicianOrders(
  technicianId: string
): Promise<OrderWithRelations[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("orders")
      .select(
        `
        *,
        customer:customer_id(id, name, email, phone),
        technician:technician_id(id, name, email, phone),
        service:service_id(id, name, price, category_id)
      `
      )
      .eq("technician_id", technicianId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return (data as unknown as OrderWithRelations[]) || [];
  } catch (error) {
    console.error("Error fetching technician orders:", error);
    throw error;
  }
}
