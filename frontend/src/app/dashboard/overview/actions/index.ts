"use server";

import { createClient } from "@/utils/supabase/server";
import {
  OverviewStats,
  RevenueData,
  OrderDistribution,
  ServicePopularity,
  OrderStats,
  OrderTrend,
  UserStats,
  UserGrowth,
  TechnicianPerformance,
  TopCustomer,
  ReportFilters,
  DashboardMetric,
} from "@/types/report.types";
import {
  generateDashboardMetrics,
  generateRevenueData,
  generateOrderDistribution,
  generateTopServices,
  generateActivityHeatmap,
  generateTechnicianPerformance,
  generateTopCustomers,
} from "../utils/mock-data";

function getDateRange(filter?: ReportFilters) {
  const endDate = filter?.dateRange?.end || new Date();
  const startDate = filter?.dateRange?.start || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  
  return {
    start: startDate.toISOString(),
    end: endDate.toISOString(),
  };
}

export async function getOverviewStats(filters?: ReportFilters): Promise<OverviewStats> {
  const supabase = await createClient();
  const { start, end } = getDateRange(filters);

  const [
    { data: revenueData },
    { data: ordersData },
    { data: usersData },
    { data: activeOrdersData },
  ] = await Promise.all([
    supabase
      .from("orders")
      .select("amount")
      .gte("created_at", start)
      .lte("created_at", end)
      .in("status", ["completed"]),
    
    supabase
      .from("orders")
      .select("id, status")
      .gte("created_at", start)
      .lte("created_at", end),
    
    supabase
      .from("users")
      .select("id")
      .gte("created_at", start)
      .lte("created_at", end),
    
    supabase
      .from("orders")
      .select("id")
      .in("status", ["confirmed", "in_progress"]),
  ]);

  const totalRevenue = revenueData?.reduce((sum, order) => sum + (order.amount || 0), 0) || 0;
  const totalOrders = ordersData?.length || 0;
  const completedOrders = ordersData?.filter(o => o.status === "completed").length || 0;
  const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
  const totalUsers = usersData?.length || 0;
  const activeOrders = activeOrdersData?.length || 0;
  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  const previousStart = new Date(new Date(start).getTime() - (new Date(end).getTime() - new Date(start).getTime()));
  const { data: previousRevenueData } = await supabase
    .from("orders")
    .select("amount")
    .gte("created_at", previousStart.toISOString())
    .lt("created_at", start)
    .in("status", ["completed"]);

  const previousRevenue = previousRevenueData?.reduce((sum, order) => sum + (order.amount || 0), 0) || 0;
  const revenueChange = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;

  return {
    totalRevenue,
    revenueChange,
    totalOrders,
    ordersChange: 0,
    totalUsers,
    usersChange: 0,
    completionRate,
    completionRateChange: 0,
    activeOrders,
    activeOrdersChange: 0,
    averageOrderValue,
    averageOrderValueChange: 0,
  };
}

export async function getRevenueData(filters?: ReportFilters): Promise<RevenueData[]> {
  try {
    const supabase = await createClient();
    const { start, end } = getDateRange(filters);

    const { data, error } = await supabase
      .from("orders")
      .select("amount, created_at")
      .gte("created_at", start)
      .lte("created_at", end)
      .in("status", ["completed"])
      .order("created_at");

    if (error || !data || data.length === 0) {
      // Return mock data if no real data available
      return generateRevenueData(30);
    }

    const groupedData: Record<string, { revenue: number; orders: number }> = {};

    data.forEach(order => {
      const date = new Date(order.created_at).toISOString().split('T')[0];
      if (!groupedData[date]) {
        groupedData[date] = { revenue: 0, orders: 0 };
      }
      groupedData[date].revenue += order.amount || 0;
      groupedData[date].orders += 1;
    });

    return Object.entries(groupedData).map(([date, values]) => ({
      date,
      revenue: values.revenue,
      orders: values.orders,
    }));
  } catch (error) {
    console.error("Error fetching revenue data:", error);
    return generateRevenueData(30);
  }
}

export async function getOrderDistribution(filters?: ReportFilters): Promise<OrderDistribution[]> {
  try {
    const supabase = await createClient();
    const { start, end } = getDateRange(filters);

    const { data, error } = await supabase
      .from("orders")
      .select("status")
      .gte("created_at", start)
      .lte("created_at", end);

    if (error || !data || data.length === 0) {
      return generateOrderDistribution();
    }

    const statusCounts: Record<string, number> = {
      pending: 0,
      confirmed: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0,
      rejected: 0,
    };

    data.forEach(order => {
      if (order.status in statusCounts) {
        statusCounts[order.status]++;
      }
    });

    const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);

    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }));
  } catch (error) {
    console.error("Error fetching order distribution:", error);
    return generateOrderDistribution();
  }
}

export async function getTopServices(filters?: ReportFilters): Promise<ServicePopularity[]> {
  try {
    const supabase = await createClient();
    const { start, end } = getDateRange(filters);

    const { data, error } = await supabase
      .from("orders")
      .select(`
        service_id,
        amount,
        services (
          id,
          name,
          categories (
            name
          )
        )
      `)
      .gte("created_at", start)
      .lte("created_at", end)
      .in("status", ["completed"]);

    if (error || !data || data.length === 0) {
      return generateTopServices();
    }

    const serviceMap: Record<string, ServicePopularity> = {};

    data.forEach((order: any) => {
      const serviceId = order.service_id;
      if (!serviceMap[serviceId]) {
        serviceMap[serviceId] = {
          serviceId,
          serviceName: order.services?.name || "Unknown",
          categoryName: order.services?.categories?.name || "Uncategorized",
          orderCount: 0,
          revenue: 0,
        };
      }
      serviceMap[serviceId].orderCount += 1;
      serviceMap[serviceId].revenue += order.amount || 0;
    });

    return Object.values(serviceMap)
      .sort((a, b) => b.orderCount - a.orderCount)
      .slice(0, 10);
  } catch (error) {
    console.error("Error fetching top services:", error);
    return generateTopServices();
  }
}

export async function getUserStats(filters?: ReportFilters): Promise<UserStats> {
  const supabase = await createClient();
  const { start, end } = getDateRange(filters);

  const [
    { data: totalData },
    { data: newUsersData },
    { data: activeUsersData },
  ] = await Promise.all([
    supabase
      .from("users")
      .select("role"),
    
    supabase
      .from("users")
      .select("id")
      .gte("created_at", start)
      .lte("created_at", end),
    
    supabase
      .from("orders")
      .select("customer_id")
      .gte("created_at", start)
      .lte("created_at", end),
  ]);

  const roleCounts = {
    customer: 0,
    technician: 0,
    staff: 0,
    admin: 0,
  };

  totalData?.forEach(user => {
    if (user.role in roleCounts) {
      roleCounts[user.role as keyof typeof roleCounts]++;
    }
  });

  const uniqueActiveUsers = new Set(activeUsersData?.map(o => o.customer_id)).size;

  return {
    totalUsers: totalData?.length || 0,
    totalCustomers: roleCounts.customer,
    totalTechnicians: roleCounts.technician,
    totalStaff: roleCounts.staff,
    totalAdmin: roleCounts.admin,
    newUsersThisMonth: newUsersData?.length || 0,
    activeUsersThisMonth: uniqueActiveUsers,
    userGrowthRate: 0,
  };
}

export async function getTechnicianPerformance(filters?: ReportFilters): Promise<TechnicianPerformance[]> {
  const supabase = await createClient();
  const { start, end } = getDateRange(filters);

  const { data } = await supabase
    .from("users")
    .select(`
      id,
      name,
      profile_data,
      orders:orders!technician_id (
        id,
        status,
        amount,
        completed_at,
        scheduled_at
      )
    `)
    .eq("role", "technician");

  return (data || []).map((technician: any) => {
    const completedOrders = technician.orders?.filter((o: any) => o.status === "completed") || [];
    const totalRevenue = completedOrders.reduce((sum: number, o: any) => sum + (o.amount || 0), 0);
    
    const completionTimes = completedOrders
      .filter((o: any) => o.completed_at && o.scheduled_at)
      .map((o: any) => new Date(o.completed_at).getTime() - new Date(o.scheduled_at).getTime());
    
    const avgCompletionTime = completionTimes.length > 0
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length / (1000 * 60 * 60)
      : 0;

    return {
      technicianId: technician.id,
      technicianName: technician.name || "Unknown",
      rating: technician.profile_data?.rating || 0,
      experience: technician.profile_data?.exp || 0,
      completedOrders: completedOrders.length,
      totalRevenue,
      averageCompletionTime: avgCompletionTime,
      certifications: technician.profile_data?.certs || [],
      activeStatus: true,
    };
  });
}

export async function getTopCustomers(filters?: ReportFilters): Promise<TopCustomer[]> {
  const supabase = await createClient();
  const { start, end } = getDateRange(filters);

  const { data } = await supabase
    .from("users")
    .select(`
      id,
      name,
      email,
      orders:orders!customer_id (
        id,
        amount,
        created_at,
        status
      )
    `)
    .eq("role", "customer");

  return (data || [])
    .map((customer: any) => {
      const validOrders = customer.orders?.filter((o: any) => o.status === "completed") || [];
      const totalSpent = validOrders.reduce((sum: number, o: any) => sum + (o.amount || 0), 0);
      const lastOrder = validOrders.sort((a: any, b: any) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )[0];

      return {
        customerId: customer.id,
        customerName: customer.name || "Unknown",
        email: customer.email,
        totalOrders: validOrders.length,
        totalSpent,
        averageOrderValue: validOrders.length > 0 ? totalSpent / validOrders.length : 0,
        lastOrderDate: lastOrder?.created_at || "",
      };
    })
    .filter(c => c.totalOrders > 0)
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 10);
}

export async function getDashboardMetrics(filters?: ReportFilters): Promise<DashboardMetric[]> {
  try {
    const stats = await getOverviewStats(filters);
    
    // If we don't have real data, return mock data
    if (stats.totalRevenue === 0 && stats.totalOrders === 0) {
      return generateDashboardMetrics();
    }

    return [
      {
        label: "Total Revenue",
        value: stats.totalRevenue,
        change: stats.revenueChange,
        changeType: stats.revenueChange > 0 ? "increase" : stats.revenueChange < 0 ? "decrease" : "neutral",
        icon: "revenue",
      },
      {
        label: "Total Orders",
        value: stats.totalOrders,
        change: stats.ordersChange,
        changeType: stats.ordersChange > 0 ? "increase" : stats.ordersChange < 0 ? "decrease" : "neutral",
        icon: "orders",
      },
      {
        label: "New Users",
        value: stats.totalUsers,
        change: stats.usersChange,
        changeType: stats.usersChange > 0 ? "increase" : stats.usersChange < 0 ? "decrease" : "neutral",
        icon: "users",
      },
      {
        label: "Active Orders",
        value: stats.activeOrders,
        change: stats.activeOrdersChange,
        changeType: stats.activeOrdersChange > 0 ? "increase" : stats.activeOrdersChange < 0 ? "decrease" : "neutral",
        icon: "active",
      },
      {
        label: "Completion Rate",
        value: stats.completionRate,
        change: stats.completionRateChange,
        changeType: stats.completionRateChange > 0 ? "increase" : stats.completionRateChange < 0 ? "decrease" : "neutral",
        icon: "completion",
      },
      {
        label: "Average Order Value",
        value: stats.averageOrderValue,
        change: stats.averageOrderValueChange,
        changeType: stats.averageOrderValueChange > 0 ? "increase" : stats.averageOrderValueChange < 0 ? "decrease" : "neutral",
        icon: "average",
      },
    ];
  } catch (error) {
    console.error("Error fetching dashboard metrics:", error);
    return generateDashboardMetrics();
  }
}