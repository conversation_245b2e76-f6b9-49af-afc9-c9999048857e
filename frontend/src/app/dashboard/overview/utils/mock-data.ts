import {
  DashboardMetric,
  RevenueData,
  OrderDistribution,
  ServicePopularity,
  ActivityHeatmap,
  OrderTrend,
  TechnicianPerformance,
  TopCustomer,
  UserGrowth,
} from "@/types/report.types";

// Generate realistic revenue data for the last 30 days
export function generateRevenueData(days: number = 30): RevenueData[] {
  const data: RevenueData[] = [];
  const today = new Date();
  const baseRevenue = 15000;
  const baseOrders = 45;
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // Add some realistic variation
    const dayOfWeek = date.getDay();
    const weekendMultiplier = (dayOfWeek === 0 || dayOfWeek === 6) ? 1.3 : 1;
    const randomVariation = 0.7 + Math.random() * 0.6;
    const trend = 1 + (days - i) * 0.01; // Slight upward trend
    
    data.push({
      date: date.toISOString().split('T')[0],
      revenue: Math.round(baseRevenue * weekendMultiplier * randomVariation * trend),
      orders: Math.round(baseOrders * weekendMultiplier * randomVariation),
    });
  }
  
  return data;
}

// Generate dashboard metrics with realistic values
export function generateDashboardMetrics(): DashboardMetric[] {
  return [
    {
      label: "Total Revenue",
      value: 485231,
      change: 12.5,
      changeType: "increase",
      icon: "revenue",
    },
    {
      label: "Total Orders",
      value: 1352,
      change: 8.3,
      changeType: "increase",
      icon: "orders",
    },
    {
      label: "New Customers",
      value: 234,
      change: -2.4,
      changeType: "decrease",
      icon: "users",
    },
    {
      label: "Active Orders",
      value: 87,
      change: 15.7,
      changeType: "increase",
      icon: "active",
    },
    {
      label: "Completion Rate",
      value: 94.2,
      change: 3.1,
      changeType: "increase",
      icon: "completion",
    },
    {
      label: "Avg Order Value",
      value: 358.65,
      change: 4.2,
      changeType: "increase",
      icon: "average",
    },
  ];
}

// Generate order distribution data
export function generateOrderDistribution(): OrderDistribution[] {
  const total = 1352;
  const distribution = [
    { status: "completed", count: 892, percentage: 65.98 },
    { status: "in_progress", count: 176, percentage: 13.02 },
    { status: "confirmed", count: 108, percentage: 7.99 },
    { status: "pending", count: 94, percentage: 6.95 },
    { status: "cancelled", count: 54, percentage: 3.99 },
    { status: "rejected", count: 28, percentage: 2.07 },
  ];
  
  return distribution;
}

// Generate top services data
export function generateTopServices(): ServicePopularity[] {
  const services = [
    { name: "AC Repair & Maintenance", category: "Air Conditioning", orders: 342, revenue: 102600 },
    { name: "Full House Cleaning", category: "Cleaning Services", orders: 289, revenue: 57800 },
    { name: "Plumbing Installation", category: "Plumbing", orders: 234, revenue: 93600 },
    { name: "Electrical Wiring", category: "Electrical", orders: 198, revenue: 79200 },
    { name: "Garden Landscaping", category: "Gardening", orders: 176, revenue: 52800 },
    { name: "Appliance Repair", category: "Electronics", orders: 145, revenue: 36250 },
    { name: "Painting Services", category: "Home Improvement", orders: 132, revenue: 39600 },
    { name: "Pest Control", category: "Maintenance", orders: 98, revenue: 19600 },
    { name: "Roof Repair", category: "Construction", orders: 76, revenue: 45600 },
    { name: "Security System Setup", category: "Security", orders: 62, revenue: 31000 },
  ];
  
  return services.map((service, index) => ({
    serviceId: `service-${index + 1}`,
    serviceName: service.name,
    categoryName: service.category,
    orderCount: service.orders,
    revenue: service.revenue,
  }));
}

// Generate activity heatmap data
export function generateActivityHeatmap(): ActivityHeatmap[] {
  const data: ActivityHeatmap[] = [];
  
  // Generate realistic patterns (higher activity during business hours and weekends)
  for (let day = 0; day < 7; day++) {
    for (let hour = 0; hour < 24; hour++) {
      let count = 0;
      
      // Base activity
      const isWeekend = day === 0 || day === 6;
      const isBusinessHours = hour >= 9 && hour <= 18;
      const isLunchTime = hour >= 12 && hour <= 14;
      const isEveningPeak = hour >= 17 && hour <= 20;
      
      if (isBusinessHours) {
        count = 20 + Math.floor(Math.random() * 30);
        if (isLunchTime) count += 10;
        if (isEveningPeak) count += 15;
      } else if (hour >= 6 && hour <= 22) {
        count = 5 + Math.floor(Math.random() * 15);
      } else {
        count = Math.floor(Math.random() * 5);
      }
      
      if (isWeekend) {
        count = Math.floor(count * 1.3);
      }
      
      data.push({ day, hour, count });
    }
  }
  
  return data;
}

// Generate technician performance data
export function generateTechnicianPerformance(): TechnicianPerformance[] {
  const technicians = [
    { name: "John Smith", rating: 4.8, exp: 5, orders: 234, revenue: 93600, time: 2.5, certs: ["HVAC", "Electrical"] },
    { name: "Maria Garcia", rating: 4.9, exp: 7, orders: 198, revenue: 79200, time: 2.2, certs: ["Plumbing", "Gas Safety"] },
    { name: "David Chen", rating: 4.7, exp: 3, orders: 176, revenue: 70400, time: 2.8, certs: ["Electrical", "Solar"] },
    { name: "Sarah Johnson", rating: 4.9, exp: 6, orders: 165, revenue: 66000, time: 2.3, certs: ["HVAC", "Refrigeration"] },
    { name: "Mike Wilson", rating: 4.6, exp: 4, orders: 145, revenue: 58000, time: 3.1, certs: ["Plumbing"] },
    { name: "Lisa Anderson", rating: 4.8, exp: 8, orders: 134, revenue: 53600, time: 2.4, certs: ["Electrical", "HVAC", "Solar"] },
    { name: "Robert Taylor", rating: 4.5, exp: 2, orders: 123, revenue: 49200, time: 3.5, certs: ["General"] },
    { name: "Emily Brown", rating: 4.9, exp: 5, orders: 112, revenue: 44800, time: 2.1, certs: ["Plumbing", "HVAC"] },
  ];
  
  return technicians.map((tech, index) => ({
    technicianId: `tech-${index + 1}`,
    technicianName: tech.name,
    rating: tech.rating,
    experience: tech.exp,
    completedOrders: tech.orders,
    totalRevenue: tech.revenue,
    averageCompletionTime: tech.time,
    certifications: tech.certs,
    activeStatus: true,
  }));
}

// Generate top customers data
export function generateTopCustomers(): TopCustomer[] {
  const customers = [
    { name: "ABC Corporation", email: "<EMAIL>", orders: 45, spent: 67500, lastOrder: "2024-01-15" },
    { name: "XYZ Industries", email: "<EMAIL>", orders: 38, spent: 57000, lastOrder: "2024-01-14" },
    { name: "Global Tech Ltd", email: "<EMAIL>", orders: 32, spent: 48000, lastOrder: "2024-01-13" },
    { name: "Prime Services Inc", email: "<EMAIL>", orders: 28, spent: 42000, lastOrder: "2024-01-12" },
    { name: "Metro Holdings", email: "<EMAIL>", orders: 25, spent: 37500, lastOrder: "2024-01-11" },
    { name: "City Plaza Mall", email: "<EMAIL>", orders: 23, spent: 34500, lastOrder: "2024-01-10" },
    { name: "Sunshine Apartments", email: "<EMAIL>", orders: 21, spent: 31500, lastOrder: "2024-01-09" },
    { name: "Green Valley Resort", email: "<EMAIL>", orders: 19, spent: 28500, lastOrder: "2024-01-08" },
    { name: "Blue Ocean Hotel", email: "<EMAIL>", orders: 17, spent: 25500, lastOrder: "2024-01-07" },
    { name: "Mountain View Complex", email: "<EMAIL>", orders: 15, spent: 22500, lastOrder: "2024-01-06" },
  ];
  
  return customers.map((customer, index) => ({
    customerId: `customer-${index + 1}`,
    customerName: customer.name,
    email: customer.email,
    totalOrders: customer.orders,
    totalSpent: customer.spent,
    averageOrderValue: customer.spent / customer.orders,
    lastOrderDate: customer.lastOrder,
  }));
}

// Generate order trends data
export function generateOrderTrends(days: number = 7): OrderTrend[] {
  const trends: OrderTrend[] = [];
  const today = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    const baseCount = 50;
    const variation = 0.7 + Math.random() * 0.6;
    
    trends.push({
      date: date.toISOString().split('T')[0],
      pending: Math.round(baseCount * 0.15 * variation),
      confirmed: Math.round(baseCount * 0.10 * variation),
      in_progress: Math.round(baseCount * 0.20 * variation),
      completed: Math.round(baseCount * 0.45 * variation),
      cancelled: Math.round(baseCount * 0.07 * variation),
      rejected: Math.round(baseCount * 0.03 * variation),
      total: Math.round(baseCount * variation),
    });
  }
  
  return trends;
}

// Generate user growth data
export function generateUserGrowth(months: number = 6): UserGrowth[] {
  const growth: UserGrowth[] = [];
  const today = new Date();
  
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setMonth(date.getMonth() - i);
    
    const baseCustomers = 500 + (months - i) * 50;
    const baseTechnicians = 50 + (months - i) * 5;
    const baseStaff = 10 + Math.floor((months - i) / 2);
    
    growth.push({
      date: date.toISOString().split('T')[0],
      customers: baseCustomers + Math.floor(Math.random() * 100),
      technicians: baseTechnicians + Math.floor(Math.random() * 10),
      staff: baseStaff + Math.floor(Math.random() * 3),
      total: 0, // Will be calculated
    });
    
    growth[growth.length - 1].total = 
      growth[growth.length - 1].customers + 
      growth[growth.length - 1].technicians + 
      growth[growth.length - 1].staff;
  }
  
  return growth;
}