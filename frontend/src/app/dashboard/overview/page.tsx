import { Suspense } from "react";
import PageContainer from "@/components/layout/page-container";
import { CalendarDateRangePicker } from "@/components/date-range-picker";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatsCards } from "./_components/stats-cards";
import { RevenueChart } from "./_components/revenue-chart";
import { OrderDistribution } from "./_components/order-distribution";
import { ActivityHeatmap } from "./_components/activity-heatmap";
import { TopServices } from "./_components/top-services";
import { ExportButtons } from "./_components/export-buttons";
import { RecentActivities } from "./_components/recent-activities";
import { PerformanceMetrics } from "./_components/performance-metrics";
import {
  getDashboardMetrics,
  getRevenueData,
  getOrderDistribution,
  getTopServices,
} from "./actions";
import { generateActivityHeatmap } from "./utils/mock-data";

export const metadata = {
  title: "Dashboard",
  description: "Comprehensive overview of business metrics and analytics",
};

async function OverviewContent() {
  const [metrics, revenueData, orderDistribution, topServices] = await Promise.all([
    getDashboardMetrics(),
    getRevenueData(),
    getOrderDistribution(),
    getTopServices(),
  ]);

  const activityData = generateActivityHeatmap();

  return (
    <div className="space-y-4">
      <StatsCards metrics={metrics} />
      
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
        <div className="lg:col-span-4">
          <RevenueChart data={revenueData} />
        </div>
        <div className="lg:col-span-3">
          <OrderDistribution data={orderDistribution} />
        </div>
      </div>

      <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
        <div className="lg:col-span-4">
          <ActivityHeatmap data={activityData} />
        </div>
        <div className="lg:col-span-3">
          <TopServices data={topServices} />
        </div>
      </div>

      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
        <RecentActivities />
        <PerformanceMetrics />
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <PageContainer scrollable>
      <div className="space-y-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl lg:text-3xl font-bold tracking-tight">Dashboard</h2>
            <p className="text-sm lg:text-base text-muted-foreground">
              Comprehensive overview of your business metrics and performance
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <CalendarDateRangePicker />
            <ExportButtons />
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Suspense
              fallback={
                <div className="space-y-4">
                  <StatsCards metrics={[]} loading />
                  <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
                    <div className="lg:col-span-4">
                      <RevenueChart data={[]} loading />
                    </div>
                    <div className="lg:col-span-3">
                      <OrderDistribution data={[]} loading />
                    </div>
                  </div>
                </div>
              }
            >
              <OverviewContent />
            </Suspense>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-4">
            <div className="rounded-lg border bg-card p-8 text-center">
              <h3 className="text-lg font-medium">Period Comparison</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Compare metrics across different time periods
              </p>
              <p className="text-xs text-muted-foreground mt-4">Coming soon...</p>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <div className="rounded-lg border bg-card p-8 text-center">
              <h3 className="text-lg font-medium">Trend Analysis</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Analyze long-term trends and patterns
              </p>
              <p className="text-xs text-muted-foreground mt-4">Coming soon...</p>
            </div>
          </TabsContent>

          <TabsContent value="forecasts" className="space-y-4">
            <div className="rounded-lg border bg-card p-8 text-center">
              <h3 className="text-lg font-medium">Forecasting</h3>
              <p className="text-sm text-muted-foreground mt-2">
                AI-powered predictions and forecasts
              </p>
              <p className="text-xs text-muted-foreground mt-4">Coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}