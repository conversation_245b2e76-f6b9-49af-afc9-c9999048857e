"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ActivityHeatmap as ActivityHeatmapType } from "@/types/report.types";

interface ActivityHeatmapProps {
  data: ActivityHeatmapType[];
  loading?: boolean;
  title?: string;
  description?: string;
}

const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const HOURS = Array.from({ length: 24 }, (_, i) => i);

function getColor(value: number, max: number): string {
  if (value === 0) return 'bg-muted';
  const intensity = value / max;
  if (intensity < 0.25) return 'bg-blue-200 dark:bg-blue-900';
  if (intensity < 0.5) return 'bg-blue-400 dark:bg-blue-700';
  if (intensity < 0.75) return 'bg-blue-600 dark:bg-blue-500';
  return 'bg-blue-800 dark:bg-blue-300';
}

export function ActivityHeatmap({ 
  data, 
  loading = false,
  title = "Activity Heatmap",
  description = "Order activity by day and hour"
}: ActivityHeatmapProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[280px] w-full bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  const heatmapData: Record<string, number> = {};
  let maxValue = 0;

  data.forEach(item => {
    const key = `${item.day}-${item.hour}`;
    heatmapData[key] = item.count;
    if (item.count > maxValue) maxValue = item.count;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <div className="min-w-[600px]">
            <div className="grid grid-cols-[auto_repeat(24,_1fr)] gap-1 text-xs">
              <div className="h-6" />
              {HOURS.map(hour => (
                <div key={hour} className="text-center text-muted-foreground">
                  {hour}
                </div>
              ))}
              
              {DAYS.map((day, dayIndex) => (
                <>
                  <div key={`label-${day}`} className="flex items-center justify-end pr-2 h-6 text-muted-foreground">
                    {day}
                  </div>
                  {HOURS.map(hour => {
                    const value = heatmapData[`${dayIndex}-${hour}`] || 0;
                    return (
                      <div
                        key={`${dayIndex}-${hour}`}
                        className={`h-6 rounded-sm transition-colors hover:ring-2 hover:ring-primary cursor-pointer ${getColor(value, maxValue)}`}
                        title={`${day} ${hour}:00 - ${value} orders`}
                      />
                    );
                  })}
                </>
              ))}
            </div>
            
            <div className="flex items-center justify-center gap-2 mt-4 text-xs text-muted-foreground">
              <span>Less</span>
              <div className="flex gap-1">
                <div className="w-3 h-3 bg-muted rounded-sm" />
                <div className="w-3 h-3 bg-blue-200 dark:bg-blue-900 rounded-sm" />
                <div className="w-3 h-3 bg-blue-400 dark:bg-blue-700 rounded-sm" />
                <div className="w-3 h-3 bg-blue-600 dark:bg-blue-500 rounded-sm" />
                <div className="w-3 h-3 bg-blue-800 dark:bg-blue-300 rounded-sm" />
              </div>
              <span>More</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}