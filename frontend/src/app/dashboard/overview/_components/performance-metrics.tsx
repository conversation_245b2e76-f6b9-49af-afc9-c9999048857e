"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";
import { cn } from "@/lib/utils";

interface Metric {
  name: string;
  value: number;
  target: number;
  unit?: string;
  trend?: "up" | "down" | "stable";
  trendValue?: number;
}

interface PerformanceMetricsProps {
  metrics?: Metric[];
  loading?: boolean;
}

const defaultMetrics: Metric[] = [
  {
    name: "Customer Satisfaction",
    value: 92,
    target: 95,
    unit: "%",
    trend: "up",
    trendValue: 3.2,
  },
  {
    name: "Response Time",
    value: 15,
    target: 10,
    unit: "min",
    trend: "down",
    trendValue: -2.5,
  },
  {
    name: "Service Completion",
    value: 94,
    target: 90,
    unit: "%",
    trend: "up",
    trendValue: 1.8,
  },
  {
    name: "First Call Resolution",
    value: 87,
    target: 85,
    unit: "%",
    trend: "stable",
    trendValue: 0.1,
  },
  {
    name: "Technician Utilization",
    value: 78,
    target: 80,
    unit: "%",
    trend: "up",
    trendValue: 5.3,
  },
  {
    name: "Order Fulfillment",
    value: 96,
    target: 95,
    unit: "%",
    trend: "up",
    trendValue: 2.1,
  },
];

export function PerformanceMetrics({ metrics = defaultMetrics, loading = false }: PerformanceMetricsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>Key performance indicators</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 w-1/3 bg-muted animate-pulse rounded" />
                <div className="h-2 w-full bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Metrics</CardTitle>
        <CardDescription>Key performance indicators vs targets</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {metrics.map((metric, index) => {
            const percentage = Math.min((metric.value / metric.target) * 100, 100);
            const isAboveTarget = metric.value >= metric.target;
            
            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {metric.name}
                    </p>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold">
                        {metric.value}{metric.unit}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        / {metric.target}{metric.unit}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <Badge 
                      variant={isAboveTarget ? "default" : "secondary"}
                      className={cn(
                        isAboveTarget ? "bg-green-500" : "bg-orange-500"
                      )}
                    >
                      {isAboveTarget ? "On Target" : "Below Target"}
                    </Badge>
                    {metric.trend && (
                      <div className={cn(
                        "flex items-center gap-1 text-xs",
                        metric.trend === "up" && "text-green-600",
                        metric.trend === "down" && "text-red-600",
                        metric.trend === "stable" && "text-gray-600"
                      )}>
                        {metric.trend === "up" && <TrendingUp className="h-3 w-3" />}
                        {metric.trend === "down" && <TrendingDown className="h-3 w-3" />}
                        {metric.trend === "stable" && <Minus className="h-3 w-3" />}
                        {metric.trendValue && (
                          <span>
                            {metric.trend === "up" ? "+" : ""}{metric.trendValue}%
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <Progress 
                  value={percentage} 
                  className={cn(
                    "h-2",
                    isAboveTarget && "[&>*]:bg-green-500",
                    !isAboveTarget && "[&>*]:bg-orange-500"
                  )}
                />
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}