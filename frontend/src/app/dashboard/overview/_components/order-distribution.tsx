"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts";
import { OrderDistribution as OrderDistributionType } from "@/types/report.types";

interface OrderDistributionProps {
  data: OrderDistributionType[];
  loading?: boolean;
  title?: string;
  description?: string;
}

const COLORS = {
  pending: "hsl(var(--chart-1))",
  confirmed: "hsl(var(--chart-2))",
  in_progress: "hsl(var(--chart-3))",
  completed: "hsl(var(--chart-4))",
  cancelled: "hsl(var(--chart-5))",
  rejected: "hsl(var(--destructive))",
};

const chartConfig = {
  pending: {
    label: "Pending",
    color: COLORS.pending,
  },
  confirmed: {
    label: "Confirmed",
    color: COLORS.confirmed,
  },
  in_progress: {
    label: "In Progress",
    color: COLORS.in_progress,
  },
  completed: {
    label: "Completed",
    color: COLORS.completed,
  },
  cancelled: {
    label: "Cancelled",
    color: COLORS.cancelled,
  },
  rejected: {
    label: "Rejected",
    color: COLORS.rejected,
  },
} satisfies ChartConfig;

const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  if (percent < 0.05) return null;

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      className="text-xs font-medium"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export function OrderDistribution({ 
  data, 
  loading = false,
  title = "Order Distribution",
  description = "Orders by status"
}: OrderDistributionProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[350px] w-full bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  const formattedData = data.map(item => ({
    ...item,
    name: item.status.charAt(0).toUpperCase() + item.status.slice(1).replace('_', ' '),
    fill: COLORS[item.status as keyof typeof COLORS] || "hsl(var(--muted))",
  }));

  const totalOrders = data.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          {description} - Total: {totalOrders.toLocaleString()} orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[350px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={formattedData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={CustomLabel}
                outerRadius={120}
                fill="#8884d8"
                dataKey="count"
              >
                {formattedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0];
                    return (
                      <div className="rounded-lg border bg-background p-2 shadow-sm">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex flex-col">
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              Status
                            </span>
                            <span className="font-bold text-muted-foreground">
                              {data.name}
                            </span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              Count
                            </span>
                            <span className="font-bold">
                              {data.value?.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span className="text-sm">
                    {value}: {entry.payload.count.toLocaleString()} ({entry.payload.percentage.toFixed(1)}%)
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}