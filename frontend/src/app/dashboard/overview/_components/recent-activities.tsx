"use client";

import { <PERSON>, <PERSON>Content, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CheckCircle, Clock, XCircle, AlertCircle, UserPlus, ShoppingCart, DollarSign } from "lucide-react";

interface Activity {
  id: string;
  type: "order" | "user" | "payment" | "system";
  title: string;
  description: string;
  timestamp: string;
  status?: "success" | "pending" | "failed" | "warning";
  user?: {
    name: string;
    avatar?: string;
  };
}

interface RecentActivitiesProps {
  activities?: Activity[];
  loading?: boolean;
}

const mockActivities: Activity[] = [
  {
    id: "1",
    type: "order",
    title: "New order #1234",
    description: "AC Repair service booked by <PERSON>",
    timestamp: "2 minutes ago",
    status: "success",
    user: { name: "<PERSON>" },
  },
  {
    id: "2",
    type: "payment",
    title: "Payment received",
    description: "$450 from ABC Corporation",
    timestamp: "15 minutes ago",
    status: "success",
  },
  {
    id: "3",
    type: "user",
    title: "New technician registered",
    description: "<PERSON> <PERSON> joined as technician",
    timestamp: "1 hour ago",
    status: "pending",
    user: { name: "Mike Wilson" },
  },
  {
    id: "4",
    type: "order",
    title: "Order cancelled",
    description: "Order #1232 cancelled by customer",
    timestamp: "2 hours ago",
    status: "failed",
  },
  {
    id: "5",
    type: "system",
    title: "System maintenance",
    description: "Scheduled maintenance completed",
    timestamp: "3 hours ago",
    status: "warning",
  },
  {
    id: "6",
    type: "order",
    title: "Order completed",
    description: "Plumbing service completed successfully",
    timestamp: "4 hours ago",
    status: "success",
  },
  {
    id: "7",
    type: "user",
    title: "Customer feedback",
    description: "5-star rating from Global Tech Ltd",
    timestamp: "5 hours ago",
    status: "success",
  },
  {
    id: "8",
    type: "payment",
    title: "Refund processed",
    description: "$120 refunded to customer",
    timestamp: "6 hours ago",
    status: "warning",
  },
];

const statusIcons = {
  success: <CheckCircle className="h-4 w-4 text-green-500" />,
  pending: <Clock className="h-4 w-4 text-yellow-500" />,
  failed: <XCircle className="h-4 w-4 text-red-500" />,
  warning: <AlertCircle className="h-4 w-4 text-orange-500" />,
};

const typeIcons = {
  order: <ShoppingCart className="h-4 w-4" />,
  user: <UserPlus className="h-4 w-4" />,
  payment: <DollarSign className="h-4 w-4" />,
  system: <AlertCircle className="h-4 w-4" />,
};

export function RecentActivities({ activities = mockActivities, loading = false }: RecentActivitiesProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>Latest system activities and updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-1/2 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
        <CardDescription>Latest system activities and updates</CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4">
                <Avatar className="h-9 w-9">
                  {activity.user?.avatar ? (
                    <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                  ) : (
                    <AvatarFallback>
                      {typeIcons[activity.type]}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {activity.title}
                    </p>
                    {activity.status && statusIcons[activity.status]}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {activity.description}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {activity.timestamp}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}