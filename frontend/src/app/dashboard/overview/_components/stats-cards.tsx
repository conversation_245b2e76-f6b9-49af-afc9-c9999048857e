"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Activity,
  CheckCircle,
  Package
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DashboardMetric } from "@/types/report.types";

interface StatsCardsProps {
  metrics: DashboardMetric[];
  loading?: boolean;
}

const iconMap: Record<string, any> = {
  revenue: DollarSign,
  orders: ShoppingCart,
  users: Users,
  active: Activity,
  completion: CheckCircle,
  average: Package,
};

function formatValue(value: number | string, type?: string): string {
  if (typeof value === 'number') {
    if (type === 'currency') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    }
    if (type === 'percentage') {
      return `${value.toFixed(1)}%`;
    }
    return new Intl.NumberFormat('en-US').format(value);
  }
  return value;
}

export function StatsCards({ metrics, loading = false }: StatsCardsProps) {
  if (loading) {
    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-20 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {metrics.map((metric, index) => {
        const Icon = iconMap[metric.icon || 'revenue'];
        const isPositive = metric.changeType === 'increase';
        const isNegative = metric.changeType === 'decrease';

        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.label}
              </CardTitle>
              <Icon className={cn(
                "h-4 w-4",
                metric.color ? `text-${metric.color}-500` : "text-muted-foreground"
              )} />
            </CardHeader>
            <CardContent>
              <div className="text-xl lg:text-2xl font-bold">
                {formatValue(
                  metric.value, 
                  metric.label.toLowerCase().includes('revenue') || 
                  metric.label.toLowerCase().includes('value') ? 'currency' : 
                  metric.label.toLowerCase().includes('rate') ? 'percentage' : undefined
                )}
              </div>
              {metric.change !== undefined && (
                <p className={cn(
                  "text-xs flex items-center gap-1 mt-1",
                  isPositive && "text-green-600",
                  isNegative && "text-red-600",
                  !isPositive && !isNegative && "text-muted-foreground"
                )}>
                  {isPositive && <TrendingUp className="h-3 w-3" />}
                  {isNegative && <TrendingDown className="h-3 w-3" />}
                  {isPositive ? '+' : ''}{metric.change.toFixed(1)}% from last period
                </p>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}