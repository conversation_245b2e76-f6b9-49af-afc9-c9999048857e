"use client";

import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ServicePopularity } from "@/types/report.types";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface TopServicesProps {
  data: ServicePopularity[];
  loading?: boolean;
  title?: string;
  description?: string;
}

export function TopServices({ 
  data, 
  loading = false,
  title = "Top Services",
  description = "Most popular services by order count"
}: TopServicesProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                <div className="h-2 w-full bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const maxOrders = Math.max(...data.map(s => s.orderCount), 1);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((service, index) => (
            <div key={service.serviceId} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {index + 1}. {service.serviceName}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {service.categoryName}
                  </Badge>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {service.orderCount.toLocaleString()} orders
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(service.revenue)}
                  </div>
                </div>
              </div>
              <Progress 
                value={(service.orderCount / maxOrders) * 100} 
                className="h-2"
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}