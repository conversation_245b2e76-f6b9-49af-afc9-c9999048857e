import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { ConversationList, ChatInterface } from "../conversation/_components";

interface Recipient {
  name: string;
  phone: string;
}

export default async function TechnicianConversationPage({
  searchParams,
}: {
  searchParams: { id?: string };
}) {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  // Get user role
  const { role } = user.user_metadata || {};
  const isSystemUser = role === "admin" || role === "staff";

  // Get conversation id from query params
  const conversationId = searchParams.id;

  // Fetch recipient name if conversation id exists
  let recipient: { name: string; phone: string } | null = null;
  if (conversationId) {
    const { data: conversation } = await supabase
      .from("conversations")
      .select("user_id")
      .eq("id", conversationId)
      .single();

    if (conversation) {
      // If current user is admin/staff, get user's name, otherwise get admin's name
      const { data: profile } = await supabase
        .from("users")
        .select("name, phone")
        .eq("id", conversation.user_id)
        .single();

      recipient = {
        name: profile?.name || "Unknown",
        phone: profile?.phone || "",
      };
    }
  }

  return (
    <div className="flex flex-1">
      <div className="hidden border-r md:block md:w-80">
        <ConversationList 
          currentUserId={user.id} 
          userRole={role}
          filterRole="technician"
        />
      </div>
      <div className="flex flex-1 flex-col">
        {conversationId ? (
          <ChatInterface
            conversationId={conversationId}
            currentUserId={user.id}
            recipient={recipient as Recipient}
          />
        ) : (
          <div className="flex h-full flex-col items-center justify-center p-4 text-center">
            <h1 className="text-2xl font-semibold">Technician Conversations</h1>
            <p className="text-muted-foreground">
              {isSystemUser
                ? "Select a technician conversation or start a new one."
                : "Select a conversation to chat with our support team."}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}