"use client";

import { useState, useRef, useEffect } from "react";
import { useRealtimeMessages } from "../hooks/use-realtime-messages";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, SendIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  ChatBubble,
  ChatBubbleAvatar,
  ChatBubbleMessage,
  ChatBubbleTimestamp,
} from "@/components/ui/chat/chat-bubble";
import { ChatInput } from "@/components/ui/chat/chat-input";
import { ChatMessageList } from "@/components/ui/chat/chat-message-list";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Recipient {
  name: string;
  phone: string;
}

interface ChatInterfaceProps {
  conversationId: string;
  currentUserId: string;
  recipient: Recipient;
}

// Format timestamp
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

export function ChatInterface({
  conversationId,
  currentUserId,
  recipient,
}: ChatInterfaceProps) {
  const [isSending, setIsSending] = useState(false);
  const [messageContent, setMessageContent] = useState("");
  
  // Handle input change to track message content for button state
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageContent(e.target.value);
  };
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [shouldFocus, setShouldFocus] = useState(false);

  // Use our realtime messages hook
  const {
    messages,
    loading,
    loadingEarlier,
    hasMore,
    messagesEndRef,
    loadEarlierMessages,
    sendMessage,
  } = useRealtimeMessages(conversationId);

  useEffect(() => {
    if (shouldFocus && inputRef.current) {
      inputRef.current.focus();
      setShouldFocus(false);
    }
  }, [shouldFocus]);

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Use state value instead of ref for consistency
    const trimmedContent = messageContent.trim();

    if (!trimmedContent || isSending) return;

    try {
      // Clear input immediately for better UX
      setMessageContent("");
      if (inputRef.current) {
        inputRef.current.value = "";
      }
      setIsSending(true);

      await sendMessage({
        conversation_id: conversationId,
        content: trimmedContent,
        sender_id: currentUserId,
      });

      // Clear input trực tiếp
      if (inputRef.current) {
        setShouldFocus(true);
      }
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setIsSending(false);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Shift+Enter for new line (default behavior)
    if (e.key === "Enter" && e.shiftKey) {
      return; // Let default behavior handle new line
    }

    // Enter to send message
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Helper function to render message content
  const renderMessage = (msg: any, index: number) => {
    const isOwnMessage = "sender_id" in msg && msg.sender_id === currentUserId;
    const isPending = "isPending" in msg && msg.isPending;
    const isSystem = "is_system" in msg && msg.is_system;

    const messageId =
      "id" in msg ? msg.id : "temp_id" in msg ? msg.temp_id : `msg-${index}`;

    const senderName = isSystem
      ? "System"
      : isOwnMessage
      ? "You"
      : "sender" in msg && msg.sender?.name
      ? msg.sender.name
      : "User";

    const timestamp =
      "created_at" in msg && msg.created_at ? formatTime(msg.created_at) : "";

    return (
      <ChatBubble
        key={messageId}
        variant={isOwnMessage ? "sent" : "received"}
        className="max-w-full"
      >
        <ChatBubbleAvatar fallback={senderName.charAt(0)} />
        <div className="flex flex-col gap-1">
          <ChatBubbleMessage variant={isOwnMessage ? "sent" : "received"}>
            <p>{msg.content}</p>
            <ChatBubbleTimestamp
              className={cn(
                isOwnMessage ? "text-right" : "text-left",
                "text-xs"
              )}
              timestamp={isPending ? "Sending..." : timestamp}
            />
          </ChatBubbleMessage>
        </div>
      </ChatBubble>
    );
  };

  return (
    <div className="flex h-full flex-col">
      {/* Chat header */}
      <div className="border-b px-4 py-3 flex-shrink-0 flex items-center">
        <Avatar className="h-10 w-10">
          <AvatarImage src="" alt={recipient.name || ""} />
          <AvatarFallback className={cn("bg-primary text-primary-foreground")}>
            {recipient.name.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="ml-2">
          <h2 className="text-lg font-semibold">{recipient.name}</h2>
          <p className="text-sm text-muted-foreground">{recipient.phone}</p>
        </div>
      </div>

      {/* Chat messages */}
      <ChatMessageList className="h-[calc(100dvh-250px)]">
        {/* Load more button */}
        {hasMore && (
          <div className="mb-4 flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={loadEarlierMessages}
              disabled={loadingEarlier}
            >
              {loadingEarlier ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                "Load earlier messages"
              )}
            </Button>
          </div>
        )}

        {/* Loading state */}
        {loading && (
          <div className="flex h-full items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        )}

        {/* No messages state */}
        {!loading && messages.length === 0 && (
          <div className="flex h-full flex-col items-center justify-center text-center text-muted-foreground">
            <p>No messages yet</p>
            <p className="text-sm">Send a message to start the conversation</p>
          </div>
        )}

        {/* Messages */}
        {!loading && messages.length > 0 && (
          <div className="space-y-4">{messages.map(renderMessage)}</div>
        )}

        {/* This div is used to scroll to the bottom */}
        <div ref={messagesEndRef} className="h-1" />
      </ChatMessageList>

      {/* Chat input */}
      <div className="border-t p-4 flex-shrink-0">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <ChatInput
            ref={inputRef}
            value={messageContent}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            disabled={loading || isSending}
          />
          <Button
            type="submit"
            disabled={!messageContent.trim() || isSending}
            className={cn(
              "h-10 w-10 rounded-full p-0",
              !messageContent.trim() && "opacity-50"
            )}
          >
            {isSending ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <SendIcon className="h-5 w-5" />
            )}
            <span className="sr-only">Send</span>
          </Button>
        </form>
        <p className="mt-2 text-xs text-muted-foreground">
          Press <kbd className="rounded border px-1 py-0.5 text-xs">Enter</kbd>{" "}
          to send,{" "}
          <kbd className="rounded border px-1 py-0.5 text-xs ml-1">
            Shift+Enter
          </kbd>{" "}
          for new line
        </p>
      </div>
    </div>
  );
}
