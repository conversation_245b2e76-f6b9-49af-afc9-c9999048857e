"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
// import { UserRole } from "@/types/user.types";
import { createConversation, getAvailableUsers } from "../actions";
import { toast } from "sonner";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

type UserRoleForConversation = "customer" | "technician";

interface NewConversationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NewConversationDialog({
  open,
  onOpenChange,
}: NewConversationDialogProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedUserRole, setSelectedUserRole] =
    useState<UserRoleForConversation>("customer");
  const [initialMessage, setInitialMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch available users when the dialog opens
  useEffect(() => {
    if (open) {
      fetchUsers(selectedUserRole);
    }
  }, [open, selectedUserRole]);

  // Filter users by search query
  const filteredUsers = users.filter((user) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      user.name?.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.phone?.toLowerCase().includes(searchLower)
    );
  });

  // Fetch users who don&apos;t have conversations yet
  const fetchUsers = async (role: UserRoleForConversation) => {
    try {
      setLoadingUsers(true);
      const result = await getAvailableUsers(role);
      setUsers(result || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to load available users");
    } finally {
      setLoadingUsers(false);
    }
  };

  // Handle role change
  const handleRoleChange = (role: UserRoleForConversation) => {
    setSelectedUserRole(role);
    setSelectedUserId("");
  };

  // Handle create conversation
  const handleCreateConversation = async () => {
    if (!selectedUserId) {
      toast.error("Please select a user");
      return;
    }

    try {
      setLoading(true);

      const result = await createConversation({
        user_id: selectedUserId,
        user_role: selectedUserRole,
        initial_message: initialMessage,
      });

      // Close dialog
      onOpenChange(false);

      // Reset form
      setSelectedUserId("");
      setInitialMessage("");

      // Navigate to the appropriate conversation page based on user role
      if (result.conversation) {
        const params = new URLSearchParams();
        params.set("id", result.conversation.id);
        
        // Redirect to the appropriate conversation page based on user role
        const basePath = selectedUserRole === "customer" 
          ? "/dashboard/customer-conversation" 
          : "/dashboard/technician-conversation";
        
        router.push(`${basePath}?${params.toString()}`);
      }

      // Show success message
      toast.success(
        result.isNew
          ? "New conversation created successfully"
          : "Existing conversation opened"
      );
    } catch (error) {
      console.error("Error creating conversation:", error);
      toast.error("Failed to create conversation");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>New Conversation</DialogTitle>
          <DialogDescription>
            Start a new conversation with a user who doesn&apos;t have an active
            chat yet.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Role selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="role" className="text-right text-sm font-medium">
              User type
            </label>
            <div className="col-span-3">
              <Select
                value={selectedUserRole}
                onValueChange={(value) =>
                  handleRoleChange(value as UserRoleForConversation)
                }
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer">Customer</SelectItem>
                  <SelectItem value="technician">Technician</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search users */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="search" className="text-right text-sm font-medium">
              Search
            </label>
            <div className="col-span-3">
              <Input
                id="search"
                placeholder="Search by name or email"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={loadingUsers}
              />
            </div>
          </div>

          {/* User selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="user" className="text-right text-sm font-medium">
              User
            </label>
            <div className="col-span-3">
              <Select
                value={selectedUserId}
                onValueChange={setSelectedUserId}
                disabled={loadingUsers || users.length === 0}
              >
                <SelectTrigger id="user">
                  <SelectValue
                    placeholder={
                      loadingUsers
                        ? "Loading users..."
                        : users.length === 0
                        ? "No available users"
                        : "Select a user"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {filteredUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name || user.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Initial message */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="message" className="text-right text-sm font-medium">
              First message
            </label>
            <div className="col-span-3">
              <Textarea
                id="message"
                placeholder="Enter the first message (optional)"
                value={initialMessage}
                onChange={(e) => setInitialMessage(e.target.value)}
                rows={3}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" type="button" disabled={loading}>
              Cancel
            </Button>
          </DialogClose>
          <Button
            type="button"
            onClick={handleCreateConversation}
            disabled={loading || !selectedUserId}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Conversation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
