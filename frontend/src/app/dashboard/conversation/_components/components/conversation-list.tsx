"use client";

import { useState, useCallback, memo, useEffect, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ConversationWithUser } from "@/types/conversation.types";
import { UserRole } from "@/types/user.types";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PlusIcon, SearchIcon, X } from "lucide-react";
import { useRealtimeConversations } from "../hooks/use-realtime-conversations";
import { NewConversationDialog } from "./new-conversation-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Format timestamp - moved outside component to avoid re-creation on each render
const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  
  // Reset time to midnight for accurate day comparison
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));

  // If today, show time
  if (daysDiff === 0) {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  }

  // If yesterday, show "Yesterday"
  if (daysDiff === 1) {
    return "Yesterday";
  }

  // If within a week, show day of week
  if (daysDiff < 7) {
    return date.toLocaleDateString("en-US", { weekday: "short" });
  }

  // If within current year, show month and day
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  }

  // Otherwise show full date with year
  return date.toLocaleDateString("en-US", { 
    month: "short", 
    day: "numeric", 
    year: "numeric" 
  });
};

interface ConversationItemProps {
  conversation: ConversationWithUser;
  isActive: boolean;
  onSelect: (conversation: ConversationWithUser) => void;
}

// Memoized conversation item component
const ConversationItem = memo(
  ({ conversation, isActive, onSelect }: ConversationItemProps) => {
    const hasUnread =
      !conversation.read_at ||
      new Date(conversation.last_message_at) > new Date(conversation.read_at);

    // Get first letter of name for avatar fallback
    const nameInitial = (
      conversation.user.name ||
      conversation.user.email ||
      "U"
    )
      .charAt(0)
      .toUpperCase();

    return (
      <div
        className={cn(
          "flex cursor-pointer items-center gap-3 p-3 hover:bg-muted/50 transition-colors rounded-lg",
          isActive && "bg-muted"
        )}
        onClick={() => onSelect(conversation)}
      >
        <Avatar className="h-10 w-10">
          <AvatarImage src="" alt={conversation.user.name || ""} />
          <AvatarFallback
            className={cn(
              "bg-muted text-muted-foreground",
              isActive && "bg-primary text-primary-foreground"
            )}
          >
            {nameInitial}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 space-y-1 overflow-hidden">
          <div className="flex items-center justify-between">
            <p
              className={cn(
                "truncate font-medium",
                hasUnread && "font-semibold"
              )}
            >
              {conversation.user.name || conversation.user.email}
            </p>
            <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
              {formatTimestamp(conversation.last_message_at)}
            </span>
          </div>
          <p
            className={cn(
              "truncate text-sm text-muted-foreground",
              hasUnread && "font-medium text-foreground"
            )}
          >
            {conversation.last_message || "No messages yet"}
          </p>
        </div>
        {hasUnread && (
          <div className="h-2 w-2 flex-shrink-0 rounded-full bg-primary"></div>
        )}
      </div>
    );
  }
);

ConversationItem.displayName = "ConversationItem";

interface ConversationListProps {
  currentUserId: string;
  userRole: UserRole;
  filterRole?: "customer" | "technician";
}

export function ConversationList({
  userRole,
  filterRole,
}: ConversationListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showNewDialog, setShowNewDialog] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Get conversations with realtime updates
  const { conversations, loading, error, filters, updateFilters } =
    useRealtimeConversations({
      user_role:
        userRole === "admin" || userRole === "staff" 
          ? filterRole || "customer" 
          : undefined,
    });

  // Update initialLoading state when data first loads
  useEffect(() => {
    if (!loading && initialLoading) {
      setInitialLoading(false);
    }
  }, [loading, initialLoading]);

  // Memoize current conversation ID to prevent unnecessary re-renders
  const currentConversationId = useMemo(() => searchParams.get("id"), [searchParams]);
  
  // Handle conversation selection - optimize dependencies
  const handleSelectConversation = useCallback(
    (conversation: ConversationWithUser) => {
      // Create a new URLSearchParams object based on the current params
      const params = new URLSearchParams(searchParams.toString());
      // Update the 'id' parameter
      params.set("id", conversation.id);
      // Determine the correct base path
      const basePath = filterRole === "customer" 
        ? "/dashboard/customer-conversation"
        : filterRole === "technician" 
        ? "/dashboard/technician-conversation" 
        : "/dashboard/conversation";
      // Navigate to the appropriate page with updated query parameters
      router.push(`${basePath}?${params.toString()}`);
    },
    [router, searchParams, filterRole] // Removed router from deps as it's stable
  );

  return (
    <div className="flex h-full flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">
            {filterRole === "customer" 
              ? "Customer Conversations" 
              : filterRole === "technician" 
              ? "Technician Conversations" 
              : "Conversations"}
          </h2>
          {(userRole === "admin" || userRole === "staff") && (
            <Button
              size="sm"
              onClick={() => setShowNewDialog(true)}
              className="h-8 gap-1"
            >
              <PlusIcon className="h-4 w-4" />
              <span>New</span>
            </Button>
          )}
        </div>

        {/* Filters - only show for admin/staff and when no filterRole is set */}
        {(userRole === "admin" || userRole === "staff") && (
          <>
            <div className="flex gap-2 mb-2">
              <div className="relative flex-1">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search..."
                  className="pl-8"
                  value={filters.search || ""}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                />
                {filters.search && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-9 w-9 p-0"
                    onClick={() => updateFilters({ search: "" })}
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Clear</span>
                  </Button>
                )}
              </div>
            </div>
            {!filterRole && (
              <Select
                value={filters.user_role || "customer"}
                onValueChange={(value) =>
                  updateFilters({
                    user_role: value as "customer" | "technician" | undefined,
                  })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer">Customers</SelectItem>
                  <SelectItem value="technician">Technicians</SelectItem>
                </SelectContent>
              </Select>
            )}
          </>
        )}
      </div>

      {/* Conversation list */}
      <div className="flex-1 overflow-auto">
        {initialLoading ? (
          <div className="p-4 text-center text-muted-foreground">
            Loading conversations...
          </div>
        ) : error ? (
          <div className="p-4 text-center text-destructive">
            Error loading conversations
          </div>
        ) : conversations.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No conversations found
          </div>
        ) : (
          <div className="flex flex-1 flex-col gap-2 overflow-y-auto overflow-x-hidden h-[calc(100dvh-220px)] p-4">
            {conversations.map((conversation) => (
              <ConversationItem
                key={conversation.id}
                conversation={conversation}
                isActive={currentConversationId === conversation.id}
                onSelect={handleSelectConversation}
              />
            ))}
          </div>
        )}
      </div>

      {/* New conversation dialog */}
      <NewConversationDialog
        open={showNewDialog}
        onOpenChange={setShowNewDialog}
      />
    </div>
  );
}
