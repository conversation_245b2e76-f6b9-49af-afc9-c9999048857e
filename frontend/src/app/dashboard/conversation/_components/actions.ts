"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { createAdminClient } from "@/utils/supabase/admin";
import { createClient } from "@/utils/supabase";
import {
  CreateConversationData,
  CreateMessageData,
  GetConversationsFilters,
  GetMessagesFilters,
  MarkAsReadData,
} from "@/types/conversation.types";
import { UserRole } from "@/types/user.types";

/**
 * Get conversations for the current user
 */
export async function getConversations(filters: GetConversationsFilters = {}) {
  try {
    const supabase = await createClient();
    const authUser = await supabase.auth.getUser();

    if (!authUser.data.user) {
      throw new Error("Unauthorized");
    }

    const { role } = authUser.data.user.user_metadata || {};

    // For admin/staff, get all conversations
    // For customers/technicians, get their own conversations
    let query = supabase.from("conversations").select(
      `
        *,
        user:users(id, name, email, phone, role, profile_data)
      `,
      { count: "exact" }
    );

    // Apply filters based on role
    if (role === "admin" || role === "staff") {
      // Admin/staff can see all conversations
      if (filters.user_role) {
        query = query.eq("user_role", filters.user_role);
      }
    } else {
      // Regular users can only see their own conversations
      query = query.eq("user_id", authUser.data.user.id);
    }

    // Search filter
    if (filters.search) {
      query = query.or(
        `user.name.ilike.%${filters.search}%,user.email.ilike.%${filters.search}%`
      );
    }

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const start = (page - 1) * limit;

    // Order by last message time (most recent first)
    query = query
      .order("last_message_at", { ascending: false })
      .range(start, start + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching conversations:", error);
    throw error;
  }
}

/**
 * Get messages for a specific conversation
 */
export async function getMessages(filters: GetMessagesFilters) {
  try {
    if (!filters.conversation_id) {
      throw new Error("Conversation ID is required");
    }

    const supabase = await createClient();

    // Build query
    let query = supabase
      .from("messages")
      .select(
        `
        *,
        sender:users(id, name, email, role)
      `,
        { count: "exact" }
      )
      .eq("conversation_id", filters.conversation_id);

    // For pagination from a specific point (for loading earlier messages)
    if (filters.created_at_lt) {
      query = query.lt("created_at", filters.created_at_lt);
    }

    // Pagination
    const limit = filters.limit || 50;

    // Get messages in chronological order (oldest to newest)
    query = query.order("created_at", { ascending: false }).limit(limit);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    // Return messages in reverse order so newest are at the bottom
    return {
      items: (data || []).reverse(),
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching messages:", error);
    throw error;
  }
}

/**
 * Create a new conversation
 */
export async function createConversation(data: CreateConversationData) {
  try {
    const { user_id, user_role, initial_message } = data;

    if (!user_id || !user_role) {
      throw new Error("User ID and role are required");
    }

    // Sử dụng createClient thông thường thay vì adminClient để xử lý qua RLS
    const supabase = await createClient();
    const {
      data: { user: authUser },
    } = await supabase.auth.getUser();

    if (!authUser) {
      throw new Error("Unauthorized");
    }

    // Kiểm tra xem người dùng có phải là admin hoặc staff
    const { role } = authUser.user_metadata || {};
    if (role !== "admin" && role !== "staff") {
      throw new Error("Only admin and staff can create conversations");
    }

    // Check if the user already has a conversation
    const { data: existingConversation, error: checkError } = await supabase
      .from("conversations")
      .select("id")
      .eq("user_id", user_id)
      .maybeSingle();

    if (checkError) {
      console.error("Error checking existing conversation:", checkError);
      throw new Error(
        `Failed to check existing conversations: ${checkError.message}`
      );
    }

    // If conversation exists, return it
    if (existingConversation) {
      return { conversation: existingConversation, isNew: false };
    }

    // Chỉ sử dụng adminClient khi thực sự cần thiết để tạo mới hội thoại
    const adminClient = await createAdminClient();

    // Create a new conversation
    const { data: newConversation, error: createError } = await adminClient
      .from("conversations")
      .insert({
        user_id,
        user_role,
      })
      .select()
      .single();

    if (createError) {
      console.error("Error creating conversation:", createError);
      throw new Error(`Failed to create conversation: ${createError.message}`);
    }

    // If initial message is provided, create it
    if (initial_message && newConversation) {
      const is_system = role === "admin" || role === "staff";

      const { error: messageError } = await adminClient
        .from("messages")
        .insert({
          conversation_id: newConversation.id,
          sender_id: authUser.id,
          content: initial_message,
          is_system,
        });

      if (messageError) {
        console.error("Error creating initial message:", messageError);
        // Không throw error vì đã tạo được conversation
      }
    }

    revalidatePath("/dashboard/conversation");

    return { conversation: newConversation, isNew: true };
  } catch (error: any) {
    console.error("Error creating conversation:", error);
    throw new Error(error.message || "Failed to create conversation");
  }
}

/**
 * Send a message in a conversation
 */
export async function sendMessage(data: CreateMessageData) {
  try {
    const { conversation_id, content, is_system } = data;

    if (!conversation_id || !content) {
      throw new Error("Conversation ID and content are required");
    }

    const supabase = await createClient();
    const authUser = await supabase.auth.getUser();

    if (!authUser.data.user) {
      throw new Error("Unauthorized");
    }

    // Check if the conversation exists and user has access
    const { data: conversation, error: checkError } = await supabase
      .from("conversations")
      .select("id")
      .eq("id", conversation_id)
      .maybeSingle();

    if (checkError || !conversation) {
      throw new Error("Conversation not found or access denied");
    }

    // Determine if message is from system based on user role
    const { role } = authUser.data.user.user_metadata || {};
    const isSystemUser = role === "admin" || role === "staff";
    const messageIsSystem = is_system !== undefined ? is_system : isSystemUser;

    // Create the message
    const { data: newMessage, error: createError } = await supabase
      .from("messages")
      .insert({
        conversation_id,
        sender_id: authUser.data.user.id,
        content,
        is_system: messageIsSystem,
      })
      .select()
      .single();

    if (createError) {
      throw createError;
    }

    return newMessage;
  } catch (error) {
    console.error("Error sending message:", error);
    throw error;
  }
}

/**
 * Mark a conversation as read
 */
export async function markAsRead(data: MarkAsReadData) {
  try {
    const { conversation_id } = data;

    if (!conversation_id) {
      throw new Error("Conversation ID is required");
    }

    const supabase = await createClient();
    const authUser = await supabase.auth.getUser();

    if (!authUser.data.user) {
      throw new Error("Unauthorized");
    }

    // Update the conversation read timestamp
    const { error } = await supabase
      .from("conversations")
      .update({ read_at: new Date().toISOString() })
      .eq("id", conversation_id);

    if (error) {
      throw error;
    }

    // Also mark messages as read
    await supabase
      .from("messages")
      .update({ read_at: new Date().toISOString() })
      .eq("conversation_id", conversation_id)
      .is("read_at", null);

    return { success: true };
  } catch (error) {
    console.error("Error marking conversation as read:", error);
    throw error;
  }
}

/**
 * Get available users for creating new conversations
 * This returns users who don't already have a conversation
 */
export async function getAvailableUsers(role?: UserRole) {
  try {
    const supabase = await createAdminClient();

    // Get users who don't have a conversation yet
    let query = supabase.from("users").select(`
      id,
      name,
      email,
      role,
      phone
    `);

    // Filter by role if specified
    if (role === "customer" || role === "technician") {
      query = query.eq("role", role);
    } else {
      // Only get customers and technicians
      query = query.in("role", ["customer", "technician"]);
    }

    const { data: allUsers, error: usersError } = await query;

    if (usersError) {
      throw usersError;
    }

    // Get all existing conversations
    const { data: conversations, error: convError } = await supabase
      .from("conversations")
      .select("user_id");

    if (convError) {
      throw convError;
    }

    // Create a set of user IDs that already have conversations
    const existingUserIds = new Set(conversations.map((c) => c.user_id));

    // Filter out users who already have conversations
    const availableUsers = allUsers.filter(
      (user) => !existingUserIds.has(user.id)
    );

    return availableUsers;
  } catch (error) {
    console.error("Error getting available users:", error);
    throw error;
  }
}