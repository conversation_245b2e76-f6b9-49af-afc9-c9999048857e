"use client";

import { useState, useEffect, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { getConversations } from "../actions";
import {
  ConversationWithUser,
  GetConversationsFilters,
} from "@/types/conversation.types";
import { toast } from "sonner";

export function useRealtimeConversations(
  initialFilters: GetConversationsFilters = {}
) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [conversations, setConversations] = useState<ConversationWithUser[]>(
    []
  );
  const [total, setTotal] = useState(0);
  const [filters, setFilters] =
    useState<GetConversationsFilters>(initialFilters);

  // Function to fetch conversations
  const fetchConversations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getConversations(filters);
      setConversations(response.items);
      setTotal(response.total);
    } catch (err: any) {
      setError(err.message || "Error loading conversations");
      toast.error("Unable to load conversations");
    } finally {
      setLoading(false);
    }
  };

  // Add ref to track current channel to prevent memory leaks
  const currentChannel = useRef<any>(null);
  
  // Setup Supabase realtime subscription
  useEffect(() => {
    const supabase = createClient();

    // Clean up previous channel if exists
    if (currentChannel.current) {
      supabase.removeChannel(currentChannel.current);
      currentChannel.current = null;
    }

    // Create unique channel name to prevent conflicts
    const channelName = `realtime-conversations-${Date.now()}-${Math.random()}`;

    // Subscribe to conversation changes
    const channel = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "conversations",
        },
        async (payload) => {
          // Optimize: Only refetch if the change affects current filters
          const changedConversation = payload.new as any || payload.old as any;
          
          // Smart update instead of full refetch
          if (payload.eventType === 'INSERT') {
            // Add new conversation if it matches current filters
            await fetchConversations();
          } else if (payload.eventType === 'UPDATE' && changedConversation?.id) {
            // Update specific conversation in state
            setConversations(prev => 
              prev.map(conv => 
                conv.id === changedConversation.id 
                  ? { ...conv, ...changedConversation }
                  : conv
              )
            );
          } else if (payload.eventType === 'DELETE' && changedConversation?.id) {
            // Remove conversation from state
            setConversations(prev => 
              prev.filter(conv => conv.id !== changedConversation.id)
            );
          }
        }
      )
      .subscribe();

    // Store reference to current channel
    currentChannel.current = channel;

    // Initial fetch
    fetchConversations();

    // Cleanup subscription
    return () => {
      if (currentChannel.current) {
        supabase.removeChannel(currentChannel.current);
        currentChannel.current = null;
      }
    };
  }, [filters]);

  // Update filters and trigger refetch
  const updateFilters = (newFilters: Partial<GetConversationsFilters>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 if anything other than page changes
      page:
        newFilters.page !== undefined
          ? newFilters.page
          : Object.keys(newFilters).some((key) => key !== "page")
          ? 1
          : prev.page,
    }));
  };

  // Manually refresh conversations
  const refresh = () => {
    fetchConversations();
  };

  return {
    loading,
    error,
    conversations,
    total,
    filters,
    updateFilters,
    refresh,
  };
}
