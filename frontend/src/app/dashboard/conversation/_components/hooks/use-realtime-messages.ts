"use client";

import { useState, useEffect, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { getMessages, markAsRead, sendMessage } from "../actions";
import {
  CreateMessageData,
  GetMessagesFilters,
  MessageWithSender,
  OptimisticMessage,
} from "@/types/conversation.types";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

export function useRealtimeMessages(conversationId: string) {
  const [loading, setLoading] = useState(true);
  const [loadingEarlier, setLoadingEarlier] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<
    (MessageWithSender | OptimisticMessage)[]
  >([]);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [limit, setLimit] = useState(50);
  const [oldestMessageTime, setOldestMessageTime] = useState<string | null>(
    null
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Track if we need to scroll to bottom on new messages
  const shouldScrollToBottom = useRef(true);

  // For optimistic updates
  const optimisticMessages = useRef<Map<string, OptimisticMessage>>(new Map());
  
  // Cache for user data to prevent N+1 queries
  const userCache = useRef<Map<string, any>>(new Map());
  
  // Track processed message IDs to prevent duplicates
  const processedMessageIds = useRef<Set<string>>(new Set());

  // Function to fetch messages
  const fetchMessages = async (loadEarlier = false) => {
    if (!conversationId) return;

    try {
      if (loadEarlier) {
        setLoadingEarlier(true);
      } else {
        setLoading(true);
      }

      setError(null);

      const filters: GetMessagesFilters = {
        conversation_id: conversationId,
        limit,
      };

      // For pagination, use the oldest message time
      if (loadEarlier && oldestMessageTime) {
        filters.created_at_lt = oldestMessageTime;
      }

      const response = await getMessages(filters);

      if (loadEarlier) {
        // Prepend earlier messages
        setMessages((prev) => [...response.items, ...prev]);
      } else {
        // Replace messages
        setMessages(response.items);

        // Note: Removed automatic mark as read - should be triggered by explicit user action
      }

      // Update total
      setTotal(response.total);

      // Update hasMore flag
      setHasMore(response.items.length === limit);

      // Update oldest message time for pagination
      if (response.items.length > 0) {
        const sortedMessages = [...response.items].sort(
          (a, b) =>
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
        setOldestMessageTime(sortedMessages[0].created_at);
      }
    } catch (err: any) {
      setError(err.message || "Error loading messages");
      toast.error("Unable to load messages");
    } finally {
      setLoading(false);
      setLoadingEarlier(false);
    }
  };

  // Function to load earlier messages
  const loadEarlierMessages = async () => {
    if (loadingEarlier || !hasMore) return;
    await fetchMessages(true);
  };

  // Function to scroll to bottom
  const scrollToBottom = (force = false) => {
    if ((shouldScrollToBottom.current || force) && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Function to handle scroll to determine if we should auto-scroll on new messages
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    shouldScrollToBottom.current = isNearBottom;
  };

  // Send a message with optimistic update
  const sendMessageWithOptimistic = async (data: CreateMessageData) => {
    try {
      // Create a temporary ID for optimistic update
      const tempId = uuidv4();

      // Get current time for display
      const now = new Date().toISOString();

      // Create optimistic message
      const optimisticMessage: OptimisticMessage = {
        temp_id: tempId,
        conversation_id: data.conversation_id,
        sender_id: data.sender_id || "", // Will be filled by the server
        content: data.content,
        is_system: data.is_system || false,
        isPending: true,
        created_at: now,
        updated_at: now,
      };

      // Add to our optimistic message map
      optimisticMessages.current.set(tempId, optimisticMessage);

      // Update UI immediately
      setMessages((prev) => [...prev, optimisticMessage]);

      // Ensure scroll to bottom
      setTimeout(() => scrollToBottom(true), 50);

      // Send the actual message
      const result = await sendMessage(data);

      // Remove optimistic message and add real one
      optimisticMessages.current.delete(tempId);

      // Replace optimistic message with real one - check for race condition
      setMessages((prev) => {
        // Check if real message already exists
        const realMessageExists = prev.some(msg => 
          "id" in msg && "id" in result && msg.id === result.id
        );
        
        if (realMessageExists) {
          // Just remove optimistic message
          return prev.filter(msg => 
            !("temp_id" in msg) || msg.temp_id !== tempId
          );
        }
        
        // Replace optimistic with real message
        return prev.map((msg) => {
          if ("temp_id" in msg && msg.temp_id === tempId) {
            // Mark this message ID as processed
            if ("id" in result) {
              processedMessageIds.current.add(result.id);
            }
            return result;
          }
          return msg;
        });
      });

      return result;
    } catch (err: any) {
      toast.error("Unable to send message");

      // Remove the optimistic message on error
      setMessages((prev) =>
        prev.filter((msg) => {
          return (
            !("temp_id" in msg) || !optimisticMessages.current.has(msg.temp_id!)
          );
        })
      );

      throw err;
    }
  };

  // Setup Supabase realtime subscription
  useEffect(() => {
    if (!conversationId) return;

    const supabase = createClient();

    // Subscribe to message changes for this conversation
    const channel = supabase
      .channel(`realtime-messages-${conversationId}`)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "messages",
          filter: `conversation_id=eq.${conversationId}`,
        },
        async (payload) => {
          const newMessage = payload.new as MessageWithSender;

          // Skip if we've already processed this message ID
          if (newMessage.id && processedMessageIds.current.has(newMessage.id)) {
            return;
          }

          // Check if message already exists in current state (proper deduplication)
          const messageExists = messages.some((msg) => {
            return "id" in msg && "id" in newMessage && msg.id === newMessage.id;
          });

          if (messageExists) {
            return;
          }

          // Get sender details from cache or fetch
          let sender = userCache.current.get(newMessage.sender_id);
          
          if (!sender) {
            try {
              const supabase = createClient();
              const { data: senderData } = await supabase
                .from("users")
                .select("id, name, email, role")
                .eq("id", newMessage.sender_id)
                .single();
              
              sender = senderData || {
                id: newMessage.sender_id,
                name: "Unknown",
                email: "",
                role: "customer",
              };
              
              // Cache the user data
              userCache.current.set(newMessage.sender_id, sender);
            } catch (error) {
              console.error("Error fetching sender:", error);
              sender = {
                id: newMessage.sender_id,
                name: "Unknown",
                email: "",
                role: "customer",
              };
            }
          }

          // Create message with sender
          const messageWithSender = {
            ...newMessage,
            sender,
          };

          // Mark as processed
          if (newMessage.id) {
            processedMessageIds.current.add(newMessage.id);
          }

          // Add to messages with final check
          setMessages((prev) => {
            // Final duplicate check in setter
            if (prev.some(msg => "id" in msg && "id" in messageWithSender && msg.id === messageWithSender.id)) {
              return prev;
            }
            return [...prev, messageWithSender];
          });

          // Scroll to bottom if we're already at the bottom
          setTimeout(() => scrollToBottom(), 50);
        }
      )
      .subscribe();

    // Initial fetch
    fetchMessages();

    // Cleanup subscription when component unmounts or conversation changes
    return () => {
      supabase.removeChannel(channel);
      optimisticMessages.current.clear();
      userCache.current.clear();
      processedMessageIds.current.clear();
    };
  }, [conversationId]);

  // Scroll to bottom on initial load and when messages change
  useEffect(() => {
    if (!loading) {
      setTimeout(() => scrollToBottom(), 100);
    }
  }, [loading]);

  // Manual mark as read function (to be called explicitly)
  const markConversationAsRead = async () => {
    try {
      await markAsRead({ conversation_id: conversationId });
    } catch (error) {
      console.error("Error marking conversation as read:", error);
    }
  };

  return {
    loading,
    loadingEarlier,
    error,
    messages,
    total,
    hasMore,
    messagesEndRef,
    handleScroll,
    loadEarlierMessages,
    sendMessage: sendMessageWithOptimistic,
    scrollToBottom,
    markAsRead: markConversationAsRead,
  };
}
