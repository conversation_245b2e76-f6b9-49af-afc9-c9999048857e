import PageContainer from "@/components/layout/page-container";
import { buttonVariants } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { searchParamsCache, serialize } from "@/lib/searchparams";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import Link from "next/link";
import UserTable from "./user-tables";
import { UserRole } from "@/types/user.types";
import { Suspense } from "react";
import { DataTableSkeleton } from "@/components/ui/table/data-table-skeleton";
import { getUsers } from "../actions";

type TUserListingPage = object;

export default async function UserListingPage({}: TUserListingPage) {
  // Showcasing the use of search params cache in nested RSCs
  const page = searchParamsCache.get("page");
  const search = searchParamsCache.get("q");
  const role = searchParamsCache.get("role") as UserRole | undefined;
  const pageLimit = searchParamsCache.get("limit");

  const filters = {
    page,
    limit: pageLimit,
    ...(search && { search }),
    ...(role && { role }),
  };

  const data = await getUsers(filters);
  const totalUsers = data.total;
  const users = data.items;

  // This key is used for invoke suspense if any of the search params changed (used for filters).
  const key = serialize({ ...filters });

  return (
    <PageContainer scrollable>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <Heading title={`Users (${totalUsers})`} description="Manage users" />

          <Link
            href={"/dashboard/user/new"}
            className={cn(buttonVariants({ variant: "default" }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />
        <Suspense
          key={key}
          fallback={<DataTableSkeleton columnCount={7} rowCount={10} />}
        >
          <UserTable data={users} totalData={totalUsers} />
        </Suspense>
      </div>
    </PageContainer>
  );
}
