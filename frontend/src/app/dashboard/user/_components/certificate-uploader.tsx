"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, X, ExternalLink, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { uploadCertificate, deleteCertificate } from "../actions/certificate-upload";

interface CertificateUploaderProps {
  value: string[];
  onChange: (urls: string[]) => void;
  error?: { message?: string };
  label?: string;
  helperText?: string;
}

export default function CertificateUploader({
  value = [],
  onChange,
  error,
  label = "Certificates",
  helperText = "Upload PDF files only (max 5MB each)",
}: CertificateUploaderProps) {
  const [isUploading, setIsUploading] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (const file of Array.from(files)) {
        // Validate file type - PDF only
        if (file.type !== 'application/pdf') {
          toast.error(`${file.name} is not a PDF file. Only PDF files are allowed.`);
          continue;
        }

        // Create FormData and append file
        const formData = new FormData();
        formData.append('file', file);

        // Use server action to upload
        const result = await uploadCertificate(formData);

        if (result.error) {
          toast.error(result.error);
        } else if (result.success && result.url) {
          uploadedUrls.push(result.url);
        }
      }

      // Update form with new URLs
      if (uploadedUrls.length > 0) {
        onChange([...value, ...uploadedUrls]);
        toast.success(`Successfully uploaded ${uploadedUrls.length} certificate(s)`);
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload certificates');
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeCertificate = async (index: number) => {
    try {
      const newUrls = [...value];
      const removedUrl = newUrls.splice(index, 1)[0];

      // If it's a Supabase URL, delete from storage
      if (removedUrl && removedUrl.includes('supabase')) {
        const urlParts = removedUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];

        // Use server action to delete
        const result = await deleteCertificate(fileName);

        if (result.error) {
          toast.error('Failed to remove certificate from storage');
        }
      }

      onChange(newUrls);
      toast.success('Certificate removed');
    } catch (error) {
      console.error('Error removing certificate:', error);
      toast.error('Failed to remove certificate');
    }
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      
      <div className="flex gap-2">
        <Input
          ref={fileInputRef}
          type="file"
          accept=".pdf,application/pdf"
          multiple
          onChange={handleFileSelect}
          disabled={isUploading}
          className="hidden"
          id="certificate-upload"
        />
        
        <Button
          type="button"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload PDF Certificates
            </>
          )}
        </Button>
      </div>

      {helperText && (
        <p className="text-xs text-muted-foreground">{helperText}</p>
      )}

      {/* Display uploaded certificates */}
      {value.length > 0 && (
        <div className="space-y-2">
          {value.map((url, index) => (
            <div
              key={index}
              className="flex items-center justify-between rounded-lg border bg-muted/50 p-2"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <FileText className="h-4 w-4 text-muted-foreground shrink-0" />
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 hover:underline text-sm truncate"
                >
                  <span className="truncate">
                    Certificate {index + 1}.pdf
                  </span>
                  <ExternalLink className="h-3 w-3 shrink-0" />
                </a>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 shrink-0"
                onClick={() => removeCertificate(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {error?.message && (
        <p className="text-sm text-destructive">{error.message}</p>
      )}
    </div>
  );
}