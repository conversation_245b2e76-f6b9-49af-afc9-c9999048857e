"use server";

import UserForm from "./user-form";
import PageContainer from "@/components/layout/page-container";
import { notFound } from "next/navigation";
import { unstable_noStore as noStore } from "next/cache";
import { UserProfile } from "@/types/user.types";
import { getUserById } from "../actions";

interface UserViewPageProps {
  userId?: string;
}

async function getUser(userId: string): Promise<UserProfile | null> {
  noStore(); // Opt out of static rendering
  try {
    const result = await getUserById(userId);
    console.log("Fetched user:", result);
    return result;
  } catch (error) {
    console.error("Error fetching user:", error);
    return null;
  }
}

export default async function UserViewPage({ userId }: UserViewPageProps) {
  if (userId) {
    const user = userId ? await getUser(userId) : undefined;
    if (!user) {
      notFound();
    }

    return (
      <PageContainer>
        <UserForm user={user} />
      </PageContainer>
    );
  } else {
    return (
      <PageContainer>
        <UserForm />
      </PageContainer>
    );
  }
}
