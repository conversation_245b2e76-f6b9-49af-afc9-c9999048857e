"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { CellAction } from "./cell-action";
import {
  BadgeCheck,
  Circle,
  CircleUser,
  PersonStanding,
  ShieldUser,
  User,
  User2,
  UserRoundCog,
} from "lucide-react";
import { getColorStyle } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { UserProfile, UserRole } from "@/types/user.types";

// Map roles to display properties
const ROLE_OPTIONS = [
  { label: "Customer", value: "customer", icon: CircleUser, color: "blue" },
  {
    label: "Technician",
    value: "technician",
    icon: UserRoundCog,
    color: "green",
  },
  { label: "Staff", value: "staff", icon: User2, color: "sky" },
  { label: "Admin", value: "admin", icon: ShieldUser, color: "red" },
];

export const columns: ColumnDef<UserProfile>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "NAME",
    enableHiding: true,
  },
  {
    accessorKey: "email",
    header: "EMAIL",
    enableHiding: true,
  },
  {
    accessorKey: "phone",
    header: "PHONE NUMBER",
    enableHiding: true,
  },
  {
    accessorKey: "address",
    header: "ADDRESS",
    enableHiding: true,
  },
  {
    accessorKey: "role",
    header: "ROLE",
    enableHiding: true,
    cell: ({ row }) => {
      const role = row.getValue("role") as UserRole;
      const roleOption = ROLE_OPTIONS.find(
        (option) => option.value === role
      ) || { label: role, icon: User2, color: "gray" };

      const Icon = roleOption.icon;

      return (
        <div
          className={`
            inline-flex items-center pl-2.5 pr-3 py-2 rounded-full text-xs font-semibold
            transition-colors duration-200
            border 
          `}
        >
          <Icon className="w-4 h-4 mr-1.5" />
          {roleOption.label}
        </div>
      );
    },
  },
  {
    accessorKey: "profile_data",
    id: "rating",
    header: "RATING",
    enableHiding: true,
    cell: ({ row }) => {
      const user = row.original as UserProfile;
      const rating = user.profile_data?.rating;
      
      // Only show for technicians
      if (user.role !== 'technician' || rating === undefined) return null;

      return (
        <div className="flex items-center">
          <span className="mr-1">{rating.toFixed(1)}</span>
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className={`w-4 h-4 ${
                  star <= rating ? "text-yellow-400" : "text-gray-300"
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "profile_data",
    id: "exp",
    header: "EXP",
    enableHiding: true,
    cell: ({ row }) => {
      const user = row.original as UserProfile;
      const exp = user.profile_data?.exp;
      
      // Only show for technicians
      if (user.role !== 'technician' || exp === undefined) return null;

      return <span>{exp} years</span>;
    },
  },
  {
    accessorKey: "profile_data",
    id: "certs",
    header: "CERTIFICATIONS",
    enableHiding: true,
    cell: ({ row }) => {
      const user = row.original as UserProfile;
      const certs = user.profile_data?.certs;
      
      // Only show for technicians
      if (user.role !== 'technician' || !certs || certs.length === 0) return null;

      return (
        <div className="flex flex-wrap gap-1">
          {certs.map((cert, index) => (
            <a
              key={index}
              href={cert}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-1"
            >
              <Badge variant="outline" className="text-xs hover:bg-accent cursor-pointer">
                Certificate {index + 1}
                <svg 
                  className="ml-1 h-3 w-3" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
                  />
                </svg>
              </Badge>
            </a>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: "CREATED AT",
    enableHiding: true,
    cell: ({ row }) => {
      const created_at = row.getValue("created_at") as string;
      return format(new Date(created_at), "PPP");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original} />,
    enableHiding: true,
  },
];
