"use client";

import { DataTable } from "@/components/ui/table/data-table";
import { DataTableResetFilter } from "@/components/ui/table/data-table-reset-filter";
import { DataTableSearch } from "@/components/ui/table/data-table-search";
import { UserProfile } from "@/types/user.types";
import { columns } from "./columns";
import { useUserTableFilters } from "./use-user-table-filters";
import { DataTableFilterBox } from "@/components/ui/table/data-table-filter-box";
import { DataTableColumnVisibility } from "@/components/ui/table/data-table-column-visibility";
import { Table } from "@tanstack/react-table";

// Map roles to display options
const ROLE_OPTIONS = [
  { label: "Customer", value: "customer" },
  { label: "Technician", value: "technician" },
  { label: "Staff", value: "staff" },
  { label: "Admin", value: "admin" },
];

export default function UserTable({
  data,
  totalData,
}: {
  data: UserProfile[];
  totalData: number;
}) {
  const {
    roleFilter,
    setRoleFilter,
    isAnyFilterActive,
    resetFilters,
    searchQuery,
    setPage,
    setSearchQuery,
  } = useUserTableFilters();

  return (
    <div className="space-y-4">
      <DataTable columns={columns} data={data} totalItems={totalData}>
        {(table: Table<UserProfile>) => (
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4 flex-1">
              <DataTableSearch
                searchKey="name"
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                setPage={setPage}
              />
              <DataTableFilterBox
                filterKey="role"
                title="Role"
                options={ROLE_OPTIONS}
                setFilterValue={setRoleFilter}
                filterValue={roleFilter}
              />
              <DataTableResetFilter
                isFilterActive={isAnyFilterActive}
                onReset={resetFilters}
              />
            </div>
            <DataTableColumnVisibility
              table={table}
              storageKey="user-table-column-visibility"
            />
          </div>
        )}
      </DataTable>
    </div>
  );
}
