"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";
import {
  UserRole,
  UserProfile,
  GetUsersFilters,
  GetUsersResponse,
  CreateUserData,
  UpdateUserData,
} from "@/types/user.types";
import { stringToArray } from "@/utils/string";

/**
 * Get users with pagination and filtering
 */
export async function getUsers(
  filters: GetUsersFilters
): Promise<GetUsersResponse> {
  try {
    const supabase = await createAdminClient();

    // Start building the query
    let query = supabase.from("users").select("*", { count: "exact" });

    // Apply filters
    if (filters.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`
      );
    }

    if (filters.role) {
      query = query.in("role", stringToArray(filters.role));
    }

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to).order("created_at", { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
}

/**
 * Get a user by ID
 */
export async function getUserById(userId: string): Promise<UserProfile> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("User not found");
    }

    return data;
  } catch (error) {
    console.error("Error fetching user:", error);
    throw error;
  }
}

/**
 * Create a new user with Supabase Auth and profile
 */
export async function createUser(
  userData: CreateUserData
): Promise<UserProfile> {
  try {
    const supabase = await createAdminClient();

    // 1. Create the auth user
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          role: userData.role,
          name: userData.name,
        },
      });

    if (authError) {
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error("Failed to create user");
    }

    // 2. Create or update the user profile (in case trigger didn't work)
    const { data: profileData, error: profileError } = await supabase
      .from("users")
      .upsert({
        id: authData.user.id,
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        address: userData.address,
        role: userData.role,
        profile_data: userData.profile_data || {},
      })
      .select()
      .single();

    if (profileError) {
      // If profile creation fails, clean up the auth user
      await supabase.auth.admin.deleteUser(authData.user.id);
      throw new Error(profileError.message);
    }

    // Revalidate the users page to update the UI
    revalidatePath("/dashboard/user");

    return profileData;
  } catch (error) {
    console.error("Error creating user:", error);
    throw error;
  }
}

/**
 * Creates a default admin user if one doesn't already exist.
 */
export async function createDefaultAdminUser() {
  try {
    const supabase = await createAdminClient();
    const adminEmail = process.env.NEXT_PUBLIC_ADMIN_DEFAULT_EMAIL;
    const adminPassword = process.env.NEXT_PUBLIC_ADMIN_DEFAULT_PASSWORD;

    // Check if admin already exists
    const { data: existingUsers } = await supabase
      .from("users")
      .select("id, role")
      .eq("email", adminEmail)
      .eq("role", "admin");

    if (existingUsers && existingUsers.length > 0) {
      console.log("Default admin user already exists");
      return;
    }

    // Create the admin user in auth
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email: adminEmail,
        password: adminPassword,
        email_confirm: true,
        user_metadata: {
          role: process.env.NEXT_PUBLIC_ADMIN_DEFAULT_ROLE,
          name: process.env.NEXT_PUBLIC_ADMIN_DEFAULT_NAME,
          phone: process.env.NEXT_PUBLIC_ADMIN_DEFAULT_PHONE,
        },
      });

    if (authError) {
      console.error("Error creating default admin user:", authError);
      return;
    }

    console.log("Default admin user created successfully:", authData.user.id);
    return authData.user.id;
  } catch (error) {
    console.error("Failed to create default admin user:", error);
  }
}

/**
 * Update a user profile
 */
export async function updateUser(
  userId: string,
  userData: UpdateUserData
): Promise<UserProfile> {
  try {
    const supabase = await createAdminClient();

    // 1. Update the user profile
    const { data, error } = await supabase
      .from("users")
      .update({
        name: userData.name,
        phone: userData.phone,
        address: userData.address,
        role: userData.role,
        profile_data: userData.profile_data,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // 2. Update the auth user metadata if role or name changed
    if (userData.role || userData.name) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        userId,
        {
          user_metadata: {
            role: userData.role,
            name: userData.name,
          },
        }
      );

      if (authError) {
        console.error("Error updating auth user metadata:", authError);
      }
    }

    // Revalidate the users page to update the UI
    revalidatePath("/dashboard/user");
    revalidatePath(`/dashboard/user/${userId}`);

    return data;
  } catch (error) {
    console.error("Error updating user:", error);
    throw error;
  }
}

/**
 * Delete a user (both auth and profile)
 */
export async function deleteUser(userId: string): Promise<void> {
  try {
    const supabase = await createAdminClient();

    // 1. Delete the user profile
    const { error: profileError } = await supabase
      .from("users")
      .delete()
      .eq("id", userId);

    if (profileError) {
      throw new Error(profileError.message);
    }

    // 2. Delete the auth user
    const { error: authError } = await supabase.auth.admin.deleteUser(userId);

    if (authError) {
      console.error("Error deleting auth user:", authError);
    }

    // Revalidate the users page to update the UI
    revalidatePath("/dashboard/user");
  } catch (error) {
    console.error("Error deleting user:", error);
    throw error;
  }
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: UserRole): Promise<UserProfile[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("role", role)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error(`Error fetching ${role} users:`, error);
    throw error;
  }
}

/**
 * Get technicians with additional filters on profile_data
 */
export async function getTechnicians(filters: {
  minRating?: number;
  experience?: number;
  certifications?: string[];
}): Promise<UserProfile[]> {
  try {
    const supabase = await createAdminClient();
    let query = supabase.from("users").select("*").eq("role", "technician");

    // Filter by minimum rating if provided
    if (filters.minRating) {
      query = query.gte("profile_data->rating", filters.minRating);
    }

    // Filter by minimum experience if provided
    if (filters.experience) {
      query = query.gte("profile_data->exp", filters.experience);
    }

    // Filter by certifications if provided
    if (filters.certifications && filters.certifications.length > 0) {
      // This assumes profile_data->certs is a JSON array
      filters.certifications.forEach((cert) => {
        query = query.contains("profile_data->certs", [cert]);
      });
    }

    const { data, error } = await query.order("created_at", {
      ascending: false,
    });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching technicians:", error);
    throw error;
  }
}
