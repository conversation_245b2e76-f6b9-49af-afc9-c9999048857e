"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";

export async function uploadCertificate(formData: FormData) {
  try {
    const file = formData.get('file') as File;
    
    if (!file) {
      return { error: 'No file provided' };
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return { error: 'File size exceeds 5MB limit' };
    }

    // Validate file type - PDF only
    if (file.type !== 'application/pdf') {
      return { error: 'Only PDF files are allowed. Please upload a PDF certificate.' };
    }

    // Get file extension
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random().toString(36).substring(2, 15)}_${Date.now()}.${fileExt}`;

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Use admin client for upload to bypass RLS
    const supabase = await createAdminClient();
    
    // Upload to Supabase
    const { error: uploadError, data } = await supabase.storage
      .from('certificates')
      .upload(fileName, buffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      
      // Check if bucket doesn't exist
      if (uploadError.message?.includes('Bucket not found')) {
        return { error: 'Storage bucket not configured. Please run setup first.' };
      }
      
      return { error: uploadError.message };
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from('certificates')
      .getPublicUrl(fileName);

    if (publicUrlData?.publicUrl) {
      return { 
        success: true, 
        url: publicUrlData.publicUrl,
        fileName: fileName 
      };
    }

    return { error: 'Failed to generate public URL' };
    
  } catch (error) {
    console.error('Server upload error:', error);
    return { error: 'An unexpected error occurred during upload' };
  }
}

export async function deleteCertificate(fileName: string) {
  try {
    const supabase = await createAdminClient();
    
    const { error } = await supabase.storage
      .from('certificates')
      .remove([fileName]);

    if (error) {
      console.error('Delete error:', error);
      return { error: error.message };
    }

    return { success: true };
    
  } catch (error) {
    console.error('Server delete error:', error);
    return { error: 'An unexpected error occurred during deletion' };
  }
}