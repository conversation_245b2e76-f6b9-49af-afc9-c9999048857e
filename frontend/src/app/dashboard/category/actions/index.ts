"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";
import {
  CategoryProfile,
  GetCategoriesFilters,
  GetCategoriesResponse,
  CreateCategoryData,
  UpdateCategoryData,
} from "@/types/category.types";
import { stringToArray } from "@/utils/string";

/**
 * Get categories with pagination and filtering
 */
export async function getCategories(
  filters: GetCategoriesFilters
): Promise<GetCategoriesResponse> {
  try {
    const supabase = await createAdminClient();

    // Start building the query
    let query = supabase.from("categories").select("*", { count: "exact" });

    // Apply filters
    if (filters.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,slug.ilike.%${filters.search}%`
      );
    }

    if (filters.is_active !== undefined) {
      query = query.eq("is_active", filters.is_active);
    }

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to).order("sort_order", { ascending: true });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
}

/**
 * Get a category by ID
 */
export async function getCategoryById(
  categoryId: string
): Promise<CategoryProfile> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("id", categoryId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Category not found");
    }

    return data;
  } catch (error) {
    console.error("Error fetching category:", error);
    throw error;
  }
}

/**
 * Create a new category
 */
export async function createCategory(
  categoryData: CreateCategoryData
): Promise<CategoryProfile> {
  try {
    const supabase = await createAdminClient();

    const { data, error } = await supabase
      .from("categories")
      .insert({
        name: categoryData.name,
        description: categoryData.description || null,
        slug: categoryData.slug,
        icon: categoryData.icon || null,
        sort_order: categoryData.sort_order || null,
        is_active: categoryData.is_active,
        category_data: categoryData.category_data || {},
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the categories page to update the UI
    revalidatePath("/dashboard/category");

    return data;
  } catch (error) {
    console.error("Error creating category:", error);
    throw error;
  }
}

/**
 * Update a category
 */
export async function updateCategory(
  categoryId: string,
  categoryData: UpdateCategoryData
): Promise<CategoryProfile> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("categories")
      .update({
        ...(categoryData.name && { name: categoryData.name }),
        ...(categoryData.description !== undefined && {
          description: categoryData.description,
        }),
        ...(categoryData.slug && { slug: categoryData.slug }),
        ...(categoryData.icon !== undefined && { icon: categoryData.icon }),
        ...(categoryData.sort_order !== undefined && {
          sort_order: categoryData.sort_order,
        }),
        ...(categoryData.is_active !== undefined && {
          is_active: categoryData.is_active,
        }),
        ...(categoryData.category_data && {
          category_data: categoryData.category_data,
        }),
      })
      .eq("id", categoryId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the categories page to update the UI
    revalidatePath("/dashboard/category");
    revalidatePath(`/dashboard/category/${categoryId}`);

    return data;
  } catch (error) {
    console.error("Error updating category:", error);
    throw error;
  }
}

/**
 * Delete a category
 */
export async function deleteCategory(categoryId: string): Promise<void> {
  try {
    const supabase = await createAdminClient();
    const { error } = await supabase
      .from("categories")
      .delete()
      .eq("id", categoryId);

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the categories page to update the UI
    revalidatePath("/dashboard/category");
  } catch (error) {
    console.error("Error deleting category:", error);
    throw error;
  }
}
