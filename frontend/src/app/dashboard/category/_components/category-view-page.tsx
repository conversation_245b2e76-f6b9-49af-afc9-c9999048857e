"use server";

import CategoryForm from "./category-form";
import PageContainer from "@/components/layout/page-container";
import { notFound } from "next/navigation";
import { unstable_noStore as noStore } from "next/cache";
import { CategoryProfile } from "@/types/category.types";
import { getCategoryById } from "../actions";

interface CategoryViewPageProps {
  categoryId?: string;
}

async function getCategory(
  categoryId: string
): Promise<CategoryProfile | null> {
  noStore(); // Opt out of static rendering
  try {
    const result = await getCategoryById(categoryId);
    console.log("Fetched category:", result);
    return result;
  } catch (error) {
    console.error("Error fetching category:", error);
    return null;
  }
}

export default async function CategoryViewPage({
  categoryId,
}: CategoryViewPageProps) {
  if (categoryId) {
    const category = categoryId ? await getCategory(categoryId) : undefined;
    if (!category) {
      notFound();
    }

    return (
      <PageContainer>
        <CategoryForm category={category} />
      </PageContainer>
    );
  } else {
    return (
      <PageContainer>
        <CategoryForm />
      </PageContainer>
    );
  }
}
