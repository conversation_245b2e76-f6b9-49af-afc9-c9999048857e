"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { CellAction } from "./cell-action";
import { BadgeCheck, Tag, Circle, Folder, FolderArchive } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { CategoryProfile } from "@/types/category.types";

export const columns: ColumnDef<CategoryProfile>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "NAME",
    enableHiding: true,
    cell: ({ row }) => {
      const icon = row.original.icon;
      return (
        <div className="flex items-center gap-2">
          {icon ? (
            <span className="text-primary">{icon}</span>
          ) : (
            <Folder className="h-4 w-4 text-muted-foreground" />
          )}
          <span className="font-medium">{row.getValue("name")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "slug",
    header: "SLUG",
    enableHiding: true,
    cell: ({ row }) => {
      return (
        <Badge variant="outline" className="font-mono">
          {row.getValue("slug")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "description",
    header: "DESCRIPTION",
    enableHiding: true,
    cell: ({ row }) => {
      const description = row.getValue("description") as string | null;
      return description ? (
        <span className="line-clamp-2 max-w-[300px] text-muted-foreground">
          {description}
        </span>
      ) : (
        <span className="text-muted-foreground italic">No description</span>
      );
    },
  },
  {
    accessorKey: "sort_order",
    header: "DISPLAY ORDER",
    enableHiding: true,
    cell: ({ row }) => {
      const sort_order = row.getValue("sort_order") as number | null;
      return sort_order !== null ? (
        <span className="font-medium">{sort_order}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    accessorKey: "is_active",
    header: "STATUS",
    enableHiding: true,
    cell: ({ row }) => {
      const isActive = row.getValue("is_active") as boolean;

      return (
        <div className="flex items-center">
          <Badge
            variant={isActive ? "default" : "secondary"}
            className="gap-1 items-center flex"
          >
            {isActive ? (
              <>
                <Circle className="h-2 w-2 fill-current text-emerald-500" />{" "}
                Active
              </>
            ) : (
              <>
                <Circle className="h-2 w-2 fill-current text-slate-500" />{" "}
                Inactive
              </>
            )}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: "CREATED AT",
    enableHiding: true,
    cell: ({ row }) => {
      const created_at = row.getValue("created_at") as string;
      return format(new Date(created_at), "PPP");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original} />,
    enableHiding: true,
  },
];
