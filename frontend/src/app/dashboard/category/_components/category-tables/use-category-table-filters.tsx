"use client";

import { searchParams } from "@/lib/searchparams";
import { useQueryState } from "nuqs";
import { useCallback, useMemo } from "react";

export function useCategoryTableFilters() {
  const [searchQuery, setSearchQuery] = useQueryState(
    "q",
    searchParams.q
      .withOptions({ shallow: false, throttleMs: 1000 })
      .withDefault("")
  );

  const [activeFilter, setActiveFilter] = useQueryState(
    "is_active",
    searchParams.is_active.withOptions({ shallow: false }).withDefault("")
  );

  const [page, setPage] = useQueryState(
    "page",
    searchParams.page.withDefault(1)
  );

  const resetFilters = useCallback(() => {
    setSearchQuery(null);
    setActiveFilter(null);
    setPage(1);
  }, [setSearchQuery, setActiveFilter, setPage]);

  const isAnyFilterActive = useMemo(() => {
    return !!searchQuery || !!activeFilter;
  }, [searchQuery, activeFilter]);

  return {
    searchQuery,
    setSearchQuery,
    activeFilter,
    setActiveFilter,
    page,
    setPage,
    resetFilters,
    isAnyFilterActive,
  };
}
