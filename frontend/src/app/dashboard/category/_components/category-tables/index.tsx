"use client";

import { DataTable } from "@/components/ui/table/data-table";
import { DataTableResetFilter } from "@/components/ui/table/data-table-reset-filter";
import { DataTableSearch } from "@/components/ui/table/data-table-search";
import { CategoryProfile } from "@/types/category.types";
import { columns } from "./columns";
import { useCategoryTableFilters } from "./use-category-table-filters";
import { DataTableFilterBox } from "@/components/ui/table/data-table-filter-box";
import { DataTableColumnVisibility } from "@/components/ui/table/data-table-column-visibility";
import { Table } from "@tanstack/react-table";

// Map status to display options
const STATUS_OPTIONS = [
  { label: "Active", value: "true" },
  { label: "Inactive", value: "false" },
];

export default function CategoryTable({
  data,
  totalData,
}: {
  data: CategoryProfile[];
  totalData: number;
}) {
  const {
    activeFilter,
    setActiveFilter,
    isAnyFilterActive,
    resetFilters,
    searchQuery,
    setPage,
    setSearchQuery,
  } = useCategoryTableFilters();

  return (
    <div className="space-y-4">
      <DataTable columns={columns} data={data} totalItems={totalData}>
        {(table: Table<CategoryProfile>) => (
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4 flex-1">
              <DataTableSearch
                searchKey="name"
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                setPage={setPage}
              />
              <DataTableFilterBox
                filterKey="is_active"
                title="Status"
                options={STATUS_OPTIONS}
                setFilterValue={setActiveFilter}
                filterValue={activeFilter}
              />
              <DataTableResetFilter
                isFilterActive={isAnyFilterActive}
                onReset={resetFilters}
              />
            </div>
            <DataTableColumnVisibility
              table={table}
              storageKey="category-table-column-visibility"
            />
          </div>
        )}
      </DataTable>
    </div>
  );
}
