"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/utils/supabase";
import {
  Service,
  GetServicesFilters,
  GetServicesResponse,
  CreateServiceData,
  UpdateServiceData,
} from "@/types/service.types";
import { stringToArray } from "@/utils/string";

/**
 * Get services with pagination and filtering
 */
export async function getServices(
  filters: GetServicesFilters
): Promise<GetServicesResponse> {
  try {
    const supabase = await createAdminClient();

    // Start building the query
    let query = supabase.from("services").select(
      `
        *,
        category:categories(*)
      `,
      { count: "exact" }
    );

    // Apply filters
    if (filters.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`
      );
    }

    if (filters.category_id) {
      query = query.in("category_id", stringToArray(filters.category_id));
    }

    if (filters.is_active !== undefined) {
      query = query.eq("is_active", filters.is_active);
    }

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to).order("created_at", { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error("Error fetching services:", error);
    throw error;
  }
}

/**
 * Get a service by ID
 */
export async function getServiceById(serviceId: string): Promise<Service> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("services")
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq("id", serviceId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Service not found");
    }

    return data;
  } catch (error) {
    console.error("Error fetching service:", error);
    throw error;
  }
}

/**
 * Create a new service
 */
export async function createService(
  serviceData: CreateServiceData
): Promise<Service> {
  try {
    const supabase = await createAdminClient();

    const { data, error } = await supabase
      .from("services")
      .insert({
        name: serviceData.name,
        description: serviceData.description,
        price: serviceData.price,
        duration: serviceData.duration,
        category_id: serviceData.category_id,
        is_active: serviceData.is_active ?? true,
        service_data: serviceData.service_data || {},
      })
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the services page to update the UI
    revalidatePath("/dashboard/service");

    return data;
  } catch (error) {
    console.error("Error creating service:", error);
    throw error;
  }
}

/**
 * Update a service
 */
export async function updateService(
  serviceId: string,
  serviceData: UpdateServiceData
): Promise<Service> {
  try {
    const supabase = await createAdminClient();

    // Update the service
    const { data, error } = await supabase
      .from("services")
      .update({
        name: serviceData.name,
        description: serviceData.description,
        price: serviceData.price,
        duration: serviceData.duration,
        category_id: serviceData.category_id,
        is_active: serviceData.is_active,
        service_data: serviceData.service_data,
        updated_at: new Date().toISOString(),
      })
      .eq("id", serviceId)
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the services page to update the UI
    revalidatePath("/dashboard/service");
    revalidatePath(`/dashboard/service/${serviceId}`);

    return data;
  } catch (error) {
    console.error("Error updating service:", error);
    throw error;
  }
}

/**
 * Delete a service
 */
export async function deleteService(serviceId: string): Promise<void> {
  try {
    const supabase = await createAdminClient();

    const { error } = await supabase
      .from("services")
      .delete()
      .eq("id", serviceId);

    if (error) {
      throw new Error(error.message);
    }

    // Revalidate the services page to update the UI
    revalidatePath("/dashboard/service");
  } catch (error) {
    console.error("Error deleting service:", error);
    throw error;
  }
}

/**
 * Get services by category
 */
export async function getServicesByCategory(
  categoryId: string
): Promise<Service[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("services")
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq("category_id", categoryId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error(`Error fetching services by category:`, error);
    throw error;
  }
}

/**
 * Get active services
 */
export async function getActiveServices(): Promise<Service[]> {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("services")
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq("is_active", true)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching active services:", error);
    throw error;
  }
}

/**
 * Get all active categories
 * This is a local implementation to make the service module independent
 */
export async function getActiveCategories() {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("is_active", true)
      .order("sort_order", { ascending: true });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching active categories:", error);
    return [];
  }
}
