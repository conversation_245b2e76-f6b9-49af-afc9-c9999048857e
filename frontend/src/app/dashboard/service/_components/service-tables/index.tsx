"use client";

import { DataTable } from "@/components/ui/table/data-table";
import { DataTableResetFilter } from "@/components/ui/table/data-table-reset-filter";
import { DataTableSearch } from "@/components/ui/table/data-table-search";
import { Service } from "@/types/service.types";
import { columns } from "./columns";
import { useServiceTableFilters } from "./use-service-table-filters";
import { DataTableFilterBox } from "@/components/ui/table/data-table-filter-box";
import { DataTableColumnVisibility } from "@/components/ui/table/data-table-column-visibility";
import { Table } from "@tanstack/react-table";
import { CategoryProfile } from "@/types/category.types";

// Status filter options
const STATUS_OPTIONS = [
  { label: "Active", value: "true" },
  { label: "Inactive", value: "false" },
];

export default function ServiceTable({
  data,
  totalData,
  categories,
}: {
  data: Service[];
  totalData: number;
  categories: CategoryProfile[];
}) {
  const {
    categoryFilter,
    setCategoryFilter,
    isActiveFilter,
    setIsActiveFilter,
    isAnyFilterActive,
    resetFilters,
    searchQuery,
    setPage,
    setSearchQuery,
  } = useServiceTableFilters();

  // Chuyển đổi danh sách categories để phù hợp với format của DataTableFilterBox
  const categoryOptions = categories.map((category) => ({
    label: category.name,
    value: category.id,
  }));

  return (
    <div className="space-y-4">
      <DataTable columns={columns} data={data} totalItems={totalData}>
        {(table: Table<Service>) => (
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4 flex-1">
              <DataTableSearch
                searchKey="name"
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                setPage={setPage}
              />
              <DataTableFilterBox
                filterKey="category_id"
                title="Category"
                options={categoryOptions}
                setFilterValue={setCategoryFilter}
                filterValue={categoryFilter}
              />
              <DataTableFilterBox
                filterKey="is_active"
                title="Status"
                options={STATUS_OPTIONS}
                setFilterValue={setIsActiveFilter}
                filterValue={isActiveFilter}
              />
              <DataTableResetFilter
                isFilterActive={isAnyFilterActive}
                onReset={resetFilters}
              />
            </div>
            <DataTableColumnVisibility
              table={table}
              storageKey="service-table-column-visibility"
            />
          </div>
        )}
      </DataTable>
    </div>
  );
}
