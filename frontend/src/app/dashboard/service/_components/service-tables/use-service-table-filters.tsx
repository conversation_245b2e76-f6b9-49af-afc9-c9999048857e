"use client";

import { searchParams } from "@/lib/searchparams";
import { useQueryState } from "nuqs";
import { useCallback, useMemo } from "react";
import { parseAsString } from "nuqs/parsers";

export function useServiceTableFilters() {
  const [searchQuery, setSearchQuery] = useQueryState(
    "q",
    searchParams.q
      .withOptions({ shallow: false, throttleMs: 1000 })
      .withDefault("")
  );

  const [categoryFilter, setCategoryFilter] = useQueryState(
    "category_id",
    parseAsString.withOptions({ shallow: false }).withDefault("")
  );

  const [isActiveFilter, setIsActiveFilter] = useQueryState(
    "is_active",
    parseAsString.withOptions({ shallow: false }).withDefault("")
  );

  const [page, setPage] = useQueryState(
    "page",
    searchParams.page.withDefault(1)
  );

  const resetFilters = useCallback(() => {
    setSearchQuery(null);
    setCategoryFilter(null);
    setIsActiveFilter(null);
    setPage(1);
  }, [setSearchQuery, setCategoryFilter, setIsActiveFilter, setPage]);

  const isAnyFilterActive = useMemo(() => {
    return !!searchQuery || !!categoryFilter || !!isActiveFilter;
  }, [searchQuery, categoryFilter, isActiveFilter]);

  return {
    searchQuery,
    setSearchQuery,
    categoryFilter,
    setCategoryFilter,
    isActiveFilter,
    setIsActiveFilter,
    page,
    setPage,
    resetFilters,
    isAnyFilterActive,
  };
}
