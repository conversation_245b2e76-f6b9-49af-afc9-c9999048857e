"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { CellAction } from "./cell-action";
import {
  CheckCircle2,
  Circle,
  ClipboardCheck,
  Settings,
  Wrench,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Service } from "@/types/service.types";
import { CategoryProfile } from "@/types/category.types";

// Format currency to display in USD
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

// Mapping icon cho category dựa vào icon của category
const getCategoryIcon = (icon: string | null) => {
  // Mặc định dùng Wrench nếu không có icon
  if (!icon) return Wrench;

  // Kiểm tra icon và trả về component tương ứng
  switch (icon) {
    case "🔧":
      return Wrench;
    case "🛑":
      return Circle;
    case "⚙️":
      return Settings;
    case "🛞":
      return ClipboardCheck;
    case "⚡":
      return ClipboardCheck;
    default:
      return Wrench;
  }
};

export const columns: ColumnDef<Service>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "NAME",
    enableHiding: true,
  },
  {
    accessorKey: "price",
    header: "PRICE",
    enableHiding: true,
    cell: ({ row }) => {
      const price = row.getValue("price") as number;
      return formatCurrency(price);
    },
  },
  {
    accessorKey: "duration",
    header: "DURATION",
    enableHiding: true,
    cell: ({ row }) => {
      const duration = row.getValue("duration") as number;
      return `${duration} min`;
    },
  },
  {
    accessorKey: "category",
    header: "CATEGORY",
    enableHiding: true,
    cell: ({ row }) => {
      const category = row.getValue("category") as CategoryProfile;

      // Xử lý trường hợp category là null hoặc undefined
      if (!category) {
        return (
          <div className="inline-flex items-center pl-2.5 pr-3 py-2 rounded-full text-xs font-semibold border">
            <Circle className="w-4 h-4 mr-1.5" />
            Unknown
          </div>
        );
      }

      const Icon = getCategoryIcon(category.icon);

      return (
        <div
          className={`
            inline-flex items-center pl-2.5 pr-3 py-2 rounded-full text-xs font-semibold
            transition-colors duration-200
            border 
          `}
        >
          <Icon className="w-4 h-4 mr-1.5" />
          {category.name}
        </div>
      );
    },
  },
  {
    accessorKey: "is_active",
    header: "STATUS",
    enableHiding: true,
    cell: ({ row }) => {
      const isActive = row.getValue("is_active") as boolean;
      return (
        <Badge
          variant={isActive ? "default" : "destructive"}
          className="flex items-center gap-1"
        >
          {isActive ? (
            <>
              <CheckCircle2 className="h-3 w-3" /> Active
            </>
          ) : (
            <>
              <Circle className="h-3 w-3" /> Inactive
            </>
          )}
        </Badge>
      );
    },
  },
  {
    accessorKey: "description",
    header: "DESCRIPTION",
    enableHiding: true,
    cell: ({ row }) => {
      const description = row.getValue("description") as string | undefined;
      if (!description) return null;

      // Truncate description if too long
      return description.length > 50
        ? `${description.substring(0, 50)}...`
        : description;
    },
  },
  {
    accessorKey: "created_at",
    header: "CREATED AT",
    enableHiding: true,
    cell: ({ row }) => {
      const created_at = row.getValue("created_at") as string;
      return format(new Date(created_at), "PPP");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original} />,
  },
];
