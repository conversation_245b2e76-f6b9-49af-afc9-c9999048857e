"use server";

import ServiceForm from "./service-form";
import PageContainer from "@/components/layout/page-container";
import { notFound } from "next/navigation";
import { unstable_noStore as noStore } from "next/cache";
import { getServiceById, getActiveCategories } from "../actions";

interface ServiceViewPageProps {
  serviceId?: string;
}

// Get a service by ID
async function getService(serviceId: string) {
  noStore(); // Opt out of static rendering
  try {
    return await getServiceById(serviceId);
  } catch (error) {
    console.error("Error fetching service:", error);
    return null;
  }
}

// Get all active categories
async function getCategories() {
  noStore(); // Opt out of static rendering
  try {
    return await getActiveCategories();
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

export default async function ServiceViewPage({
  serviceId,
}: ServiceViewPageProps) {
  // Fetch categories for the form to use
  const categories = await getCategories();

  if (serviceId) {
    const service = await getService(serviceId);
    if (!service) {
      notFound();
    }

    return (
      <PageContainer>
        <ServiceForm service={service} categories={categories} />
      </PageContainer>
    );
  } else {
    return (
      <PageContainer>
        <ServiceForm categories={categories} />
      </PageContainer>
    );
  }
}
