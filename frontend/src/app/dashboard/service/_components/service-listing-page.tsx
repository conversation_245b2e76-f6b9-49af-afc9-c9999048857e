import PageContainer from "@/components/layout/page-container";
import { buttonVariants } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { searchParamsCache, serialize } from "@/lib/searchparams";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import Link from "next/link";
import ServiceTable from "./service-tables";
import { Suspense } from "react";
import { DataTableSkeleton } from "@/components/ui/table/data-table-skeleton";
import { getServices, getActiveCategories } from "../actions";

type TServiceListingPage = object;

export default async function ServiceListingPage({}: TServiceListingPage) {
  // Get all filters from searchParamsCache
  const page = searchParamsCache.get("page");
  const search = searchParamsCache.get("q");
  const categoryId = searchParamsCache.get("category_id");
  const isActiveStr = searchParamsCache.get("is_active") as unknown as string;
  const pageLimit = searchParamsCache.get("limit");

  // Convert string representation to boolean for the API
  let isActive: boolean | undefined;
  if (isActiveStr === "true") isActive = true;
  if (isActiveStr === "false") isActive = false;

  const filters = {
    page,
    limit: pageLimit,
    ...(search && { search }),
    ...(categoryId && { category_id: categoryId }),
    ...(isActive !== undefined && { is_active: isActive }),
  };

  // Fetch services and categories in parallel
  const [data, categories] = await Promise.all([
    getServices(filters),
    getActiveCategories(),
  ]);

  const totalServices = data.total;
  const services = data.items;

  // Create a key for Suspense by serializing all filters
  const suspenseKey = serialize({
    page,
    limit: pageLimit,
    ...(search && { q: search }),
    ...(categoryId && { category: categoryId }),
    ...(isActive !== undefined && { is_active: isActiveStr }),
  });

  return (
    <PageContainer scrollable>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <Heading
            title={`Services (${totalServices})`}
            description="Manage vehicle repair services"
          />

          <Link
            href={"/dashboard/service/new"}
            className={cn(buttonVariants({ variant: "default" }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />
        <Suspense
          key={suspenseKey}
          fallback={<DataTableSkeleton columnCount={7} rowCount={10} />}
        >
          <ServiceTable
            data={services}
            totalData={totalServices}
            categories={categories}
          />
        </Suspense>
      </div>
    </PageContainer>
  );
}
