import { Metadata } from "next";
import Link from "next/link";
import UserAuthForm from "./user-auth-form";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { Sparkles } from "@/components/animations/sparkles";
import LogoSVG from "@/components/Logo";
export const metadata: Metadata = {
  title: "Sign In",
  description: "Sign in to your account using the form below.",
};

export default function SignInViewPage() {
  return (
    <div className="relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <Link
        href="/examples/authentication"
        className={cn(
          buttonVariants({ variant: "ghost" }),
          "absolute right-4 top-4 hidden md:right-8 md:top-8"
        )}
      >
        Login
      </Link>
      <div className="relative hidden h-full flex-col bg-muted text-white lg:flex dark:border-r">
        <section className="relative mx-auto h-full w-full overflow-hidden ">
          <Image
            src="https://images.unsplash.com/photo-*************-ea67b9b108de?q=80&w=2333&auto=format&fit=crop&ixlib=rb-4.0.3"
            width={1000}
            height={1000}
            className="absolute left-0 top-0 h-full w-full object-cover"
            alt="bg-image"
          />
          <div className="absolute inset-0 bg-black/80 z-[1]"></div>
          <article className="relative z-10 grid h-full items-center justify-center gap-4 text-center">
            <LogoSVG width={200} height={200} className="mx-auto" />
            <h1 className="bg-gradient-to-br from-white to-yellow-500 bg-clip-text text-5xl font-semibold leading-[100%] tracking-tighter text-transparent xl:text-5xl 2xl:text-6xl">
              Become Your Trusted <br /> Business Partner
            </h1>
            <Link
              href="https://vtech.us"
              className="mx-auto w-fit p-2 px-4 text-yellow-200/80"
            >
              www.vtech.us
            </Link>
          </article>

          <div className="absolute bottom-0 z-[2] h-[50vh] w-screen overflow-hidden [mask-image:radial-gradient(100%_50%,white,transparent)] before:absolute before:inset-0 before:bg-[radial-gradient(circle_at_bottom_center,#3273ff,transparent_90%)] before:opacity-40 after:absolute">
            <Sparkles
              density={2000}
              speed={1.2}
              color="#ffffff"
              direction="top"
              className="absolute inset-x-0 bottom-0 h-full w-full "
            />
          </div>
        </section>
      </div>
      <div className="flex h-full items-center p-4 lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Welcome back
            </h1>
            <p className="text-sm text-muted-foreground">
              Enter your email to sign in to your account
            </p>
          </div>
          <UserAuthForm />
          {/* <p className="px-8 text-center text-sm text-muted-foreground">
            By clicking continue, you agree to our{" "}
            <Link
              href="/terms"
              className="underline underline-offset-4 hover:text-primary"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              href="/privacy"
              className="underline underline-offset-4 hover:text-primary"
            >
              Privacy Policy
            </Link>
            .
          </p> */}
        </div>
      </div>
    </div>
  );
}
