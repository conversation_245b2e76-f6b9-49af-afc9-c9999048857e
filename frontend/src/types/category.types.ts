export type CategoryProfile = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  icon: string | null;
  sort_order: number | null;
  is_active: boolean;
  category_data: Record<string, any>;
  created_at: string;
  updated_at: string;
};

export type GetCategoriesFilters = {
  page?: number;
  limit?: number;
  search?: string;
  is_active?: boolean;
};

export type GetCategoriesResponse = {
  items: CategoryProfile[];
  total: number;
};

export type CreateCategoryData = {
  name: string;
  description?: string;
  slug: string;
  icon?: string;
  sort_order?: number | null;
  is_active: boolean;
  category_data?: Record<string, any>;
};

export type UpdateCategoryData = {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  sort_order?: number | null;
  is_active?: boolean;
  category_data?: Record<string, any>;
};
