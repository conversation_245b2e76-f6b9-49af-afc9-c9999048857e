// Define user roles
export type UserRole = "customer" | "technician" | "staff" | "admin";

export interface UserProfileData {
  rating?: number;
  exp?: number;
  certs?: string[];
}

// Define the user profile interface
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  address?: string;
  role: UserRole;
  profile_data?: UserProfileData;
  created_at: string;
  updated_at: string;
}

// Define the filters interface
export interface GetUsersFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
}

// Define the response interface
export interface GetUsersResponse {
  items: UserProfile[];
  total: number;
}

// Define create user data interface
export interface CreateUserData {
  email: string;
  password: string;
  name?: string;
  phone?: string;
  address?: string;
  role: UserRole;
  profile_data?: UserProfileData;
}

// Define update user data interface
export interface UpdateUserData {
  name?: string;
  phone?: string;
  address?: string;
  role?: UserRole;
  profile_data?: UserProfileData;
}
