// Import CategoryProfile from category types
import { CategoryProfile } from "./category.types";

export interface ServiceData {
  tools_required?: string[];
  parts_required?: string[];
  technician_notes?: string;
}

// Define the service interface
export interface Service {
  id: string;
  name: string;
  description?: string;
  price: number;
  duration: number; // in minutes
  category_id: string;
  category?: CategoryProfile; // Thông tin category liên kết (optional vì không phải lúc nào cũng cần load)
  is_active: boolean;
  service_data?: ServiceData;
  created_at: string;
  updated_at: string;
}

// Define the filters interface
export interface GetServicesFilters {
  page?: number;
  limit?: number;
  search?: string;
  category_id?: string;
  is_active?: boolean;
}

// Define the response interface
export interface GetServicesResponse {
  items: Service[];
  total: number;
}

// Define create service data interface
export interface CreateServiceData {
  name: string;
  description?: string;
  price: number;
  duration: number;
  category_id: string;
  is_active?: boolean;
  service_data?: ServiceData;
}

// Define update service data interface
export interface UpdateServiceData {
  name?: string;
  description?: string;
  price?: number;
  duration?: number;
  category_id?: string;
  is_active?: boolean;
  service_data?: ServiceData;
}
