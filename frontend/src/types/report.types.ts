export interface DateRange {
  start: Date;
  end: Date;
}

export interface ComparisonPeriod {
  current: DateRange;
  previous: DateRange;
}

export interface OverviewStats {
  totalRevenue: number;
  revenueChange: number;
  totalOrders: number;
  ordersChange: number;
  totalUsers: number;
  usersChange: number;
  completionRate: number;
  completionRateChange: number;
  activeOrders: number;
  activeOrdersChange: number;
  averageOrderValue: number;
  averageOrderValueChange: number;
}

export interface RevenueData {
  date: string;
  revenue: number;
  orders: number;
}

export interface OrderDistribution {
  status: string;
  count: number;
  percentage: number;
}

export interface ServicePopularity {
  serviceId: string;
  serviceName: string;
  categoryName: string;
  orderCount: number;
  revenue: number;
}

export interface ActivityHeatmap {
  hour: number;
  day: number;
  count: number;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  confirmedOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  rejectedOrders: number;
  averageProcessingTime: number;
  cancellationRate: number;
  averageOrderValue: number;
}

export interface OrderTrend {
  date: string;
  pending: number;
  confirmed: number;
  in_progress: number;
  completed: number;
  cancelled: number;
  rejected: number;
  total: number;
}

export interface OrderByService {
  serviceId: string;
  serviceName: string;
  categoryId: string;
  categoryName: string;
  orderCount: number;
  revenue: number;
  averageRating?: number;
}

export interface OrderByTechnician {
  technicianId: string;
  technicianName: string;
  orderCount: number;
  completedCount: number;
  cancelledCount: number;
  revenue: number;
  averageRating: number;
  completionRate: number;
}

export interface OrderByLocation {
  location: string;
  orderCount: number;
  revenue: number;
}

export interface UserStats {
  totalUsers: number;
  totalCustomers: number;
  totalTechnicians: number;
  totalStaff: number;
  totalAdmin: number;
  newUsersThisMonth: number;
  activeUsersThisMonth: number;
  userGrowthRate: number;
}

export interface UserGrowth {
  date: string;
  customers: number;
  technicians: number;
  staff: number;
  total: number;
}

export interface TechnicianPerformance {
  technicianId: string;
  technicianName: string;
  rating: number;
  experience: number;
  completedOrders: number;
  totalRevenue: number;
  averageCompletionTime: number;
  certifications: string[];
  activeStatus: boolean;
}

export interface TopCustomer {
  customerId: string;
  customerName: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: string;
}

export interface ServiceStats {
  totalServices: number;
  activeServices: number;
  totalCategories: number;
  averageServicePrice: number;
  mostExpensiveService: ServicePopularity;
  mostPopularService: ServicePopularity;
}

export interface ServiceByCategory {
  categoryId: string;
  categoryName: string;
  serviceCount: number;
  orderCount: number;
  revenue: number;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalPending: number;
  totalCompleted: number;
  totalRefunded: number;
  averageTransactionValue: number;
  paymentMethods: PaymentMethodBreakdown[];
}

export interface PaymentMethodBreakdown {
  method: string;
  count: number;
  amount: number;
  percentage: number;
}

export interface RevenueByPeriod {
  period: string;
  revenue: number;
  orderCount: number;
  growth: number;
}

export interface SupportStats {
  totalConversations: number;
  activeConversations: number;
  averageResponseTime: number;
  resolutionRate: number;
  satisfactionRate: number;
  totalMessages: number;
}

export interface ConversationMetrics {
  date: string;
  newConversations: number;
  closedConversations: number;
  averageMessages: number;
}

export interface ReportFilters {
  dateRange?: DateRange;
  comparison?: boolean;
  groupBy?: 'day' | 'week' | 'month' | 'year';
  categoryId?: string;
  serviceId?: string;
  technicianId?: string;
  customerId?: string;
  status?: string[];
  location?: string;
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  reportType: string;
  filters: ReportFilters;
  includeCharts?: boolean;
  includeRawData?: boolean;
}

export interface DashboardMetric {
  label: string;
  value: number | string;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: string;
  color?: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'heatmap';
  data: any;
  options?: any;
  responsive?: boolean;
  height?: number;
}