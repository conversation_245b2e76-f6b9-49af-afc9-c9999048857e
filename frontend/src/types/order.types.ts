// Define order status type
export type OrderStatus =
  | "pending"
  | "confirmed"
  | "in_progress"
  | "completed"
  | "cancelled"
  | "rejected";

export interface OrderData {
  location?: string;
  vehicle_info?: {
    brand?: string;
    model?: string;
    year?: string;
    license_plate?: string;
  };
  additional_services?: string[];
  special_requests?: string;
}

// Define the order interface
export interface Order {
  id: string;
  code: string; // Mã đơn hàng hiển thị cho người dùng
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: OrderStatus;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: OrderData;
  created_at: string;
  updated_at: string;
}

// Define the expanded order interface with related data
export interface OrderWithRelations extends Order {
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
    category_id: string;
  };
}

// Define the filters interface
export interface GetOrdersFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: OrderStatus;
  customer_id?: string;
  technician_id?: string;
  service_id?: string;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

// Define the response interface
export interface GetOrdersResponse {
  items: OrderWithRelations[];
  total: number;
}

// Define create order data interface
export interface CreateOrderData {
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: OrderStatus;
  amount?: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: OrderData;
}

// Define update order data interface
export interface UpdateOrderData {
  technician_id?: string;
  service_id?: string;
  status?: OrderStatus;
  amount?: number;
  scheduled_at?: string;
  completed_at?: string;
  note?: string;
  order_data?: OrderData;
}
