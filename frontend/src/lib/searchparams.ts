import {
  createSearchParamsCache,
  createSerializer,
  parseAsInteger,
  parseAsString,
} from "nuqs/server";

export const searchParams = {
  page: parseAsInteger.withDefault(1),
  limit: parseAsInteger.withDefault(10),
  q: parseAsString,
  gender: parseAsString,
  categories: parseAsString,
  status: parseAsString,
  role: parseAsString,
  orderStatus: parseAsString,
  paymentStatus: parseAsString,
  category: parseAsString,
  category_id: parseAsString,
  is_active: parseAsString,
  start_date: parseAsString,
  end_date: parseAsString,
};

export const searchParamsCache = createSearchParamsCache(searchParams);
export const serialize = createSerializer(searchParams);
