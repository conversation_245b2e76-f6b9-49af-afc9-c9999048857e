import {
  Tag,
  Users,
  Settings,
  Bookmark,
  SquarePen,
  LayoutGrid,
  LucideIcon,
  FileText,
  ShoppingCart,
  UserCheck,
  MessageSquare,
  Layers,
  BarChart3,
  PieChart,
  TrendingUp,
  FileBarChart
} from 'lucide-react';

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
};

type Menu = {
  href: string;
  label: string;
  active: boolean;
  icon: LucideIcon;
  submenus?: Submenu[];
};

type Group = {
  groupLabel: string;
  menus: Menu[];
};

export function getMenuList(pathname: string): Group[] {
  return [
    {
      groupLabel: '',
      menus: [
        {
          href: '/dashboard/overview',
          label: 'Dashboard',
          active: pathname === '/dashboard/overview' || pathname === '/dashboard',
          icon: LayoutGrid,
          submenus: []
        }
      ]
    },
    {
      groupLabel: 'Management',
      menus: [
        {
          href: '/dashboard/order',
          label: 'Orders',
          active: pathname.includes('/dashboard/order'),
          icon: ShoppingCart,
          submenus: []
        },
        {
          href: '/dashboard/service',
          label: 'Services',
          active: pathname.includes('/dashboard/service'),
          icon: Layers,
          submenus: []
        },
        {
          href: '/dashboard/category',
          label: 'Categories',
          active: pathname.includes('/dashboard/category'),
          icon: Bookmark,
          submenus: []
        },
        {
          href: '/dashboard/user',
          label: 'Users',
          active: pathname.includes('/dashboard/user'),
          icon: Users,
          submenus: []
        }
      ]
    },
    {
      groupLabel: 'Communication',
      menus: [
        {
          href: '/dashboard/conversation',
          label: 'All Conversations',
          active: pathname === '/dashboard/conversation',
          icon: MessageSquare,
          submenus: []
        },
        {
          href: '/dashboard/customer-conversation',
          label: 'Customer Support',
          active: pathname.includes('/dashboard/customer-conversation'),
          icon: UserCheck,
          submenus: []
        },
        {
          href: '/dashboard/technician-conversation',
          label: 'Technician Chat',
          active: pathname.includes('/dashboard/technician-conversation'),
          icon: MessageSquare,
          submenus: []
        }
      ]
    },
    {
      groupLabel: 'Analytics & Reports',
      menus: [
        {
          href: '/dashboard/reports/overview',
          label: 'Overview Reports',
          active: pathname === '/dashboard/reports/overview',
          icon: BarChart3,
          submenus: []
        },
        {
          href: '/dashboard/reports/orders',
          label: 'Orders Analytics',
          active: pathname === '/dashboard/reports/orders',
          icon: TrendingUp,
          submenus: []
        },
        {
          href: '/dashboard/reports/users',
          label: 'Users Analytics',
          active: pathname === '/dashboard/reports/users',
          icon: PieChart,
          submenus: []
        },
        {
          href: '/dashboard/reports/services',
          label: 'Services Analytics',
          active: pathname === '/dashboard/reports/services',
          icon: FileBarChart,
          submenus: []
        },
        {
          href: '/dashboard/reports/financial',
          label: 'Financial Reports',
          active: pathname === '/dashboard/reports/financial',
          icon: FileText,
          submenus: []
        }
      ]
    },
    {
      groupLabel: 'Settings',
      menus: [
        {
          href: '/dashboard/settings',
          label: 'Settings',
          active: pathname.includes('/dashboard/settings'),
          icon: Settings,
          submenus: []
        }
      ]
    }
  ];
}
