'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

type BreadcrumbItem = {
  title: string;
  link: string;
};

// Function to convert slug to readable text
const slugToText = (slug: string): string => {
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// This allows to add custom title as well
const routeMapping: Record<string, BreadcrumbItem[]> = {
  '/dashboard': [{ title: 'Dashboard', link: '/dashboard' }],
  '/dashboard/employee': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Employee', link: '/dashboard/employee' }
  ],
  '/dashboard/product': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Product', link: '/dashboard/product' }
  ],
  '/dashboard/conversation': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Conversation', link: '/dashboard/conversation' }
  ],
  '/dashboard/customer-conversation': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Customer Conversation', link: '/dashboard/customer-conversation' }
  ],
  '/dashboard/technician-conversation': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Technician Conversation', link: '/dashboard/technician-conversation' }
  ]
  // Add more custom mappings as needed
};

export function useBreadcrumbs() {
  const pathname = usePathname();

  const breadcrumbs = useMemo(() => {
    // Check if we have a custom mapping for this exact path
    if (routeMapping[pathname]) {
      return routeMapping[pathname];
    }

    // If no exact match, fall back to generating breadcrumbs from the path
    const segments = pathname.split('/').filter(Boolean);
    return segments.map((segment, index) => {
      const path = `/${segments.slice(0, index + 1).join('/')}`;
      return {
        title: slugToText(segment),
        link: path
      };
    });
  }, [pathname]);

  return breadcrumbs;
}
