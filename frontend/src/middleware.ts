import { type NextRequest } from "next/server";
import { updateSession } from "@/utils/supabase/middleware";
import { createServerClient } from "@supabase/ssr";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Update the session first, which refreshes tokens if needed
  const response = await updateSession(request);

  // After session update, check auth status
  const redirectToLogin = () => {
    const url = request.nextUrl.clone();
    url.pathname = "/";
    url.searchParams.set("redirect", pathname);
    return Response.redirect(url);
  };

  // Check if this is a protected route
  if (pathname.startsWith("/dashboard")) {
    // Create a server client to validate the session properly
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set() {}, // We don't need to set cookies here
          remove() {}, // We don't need to remove cookies here
        },
      }
    );

    // Use getUser() for secure validation
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // If no authenticated user is found, redirect to login
    if (!user) {
      return redirectToLogin();
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
