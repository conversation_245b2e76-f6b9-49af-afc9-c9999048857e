"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import {
  Bold,
  Italic,
  List,
  Heading1,
  Heading2,
  Heading3,
  Type,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { useState, forwardRef, useEffect } from "react";

interface EditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const Editor = forwardRef<HTMLDivElement, EditorProps>(
  ({ value, onChange, className }, ref) => {
    const editor = useEditor({
      extensions: [
        StarterKit,
      ],
      content: value,
      editorProps: {
        attributes: {
          class: "prose max-w-none focus:outline-none",
        },
      },
      onUpdate: ({ editor }) => {
        onChange(editor.getHTML());
      },
      immediatelyRender: false,
    });

    useEffect(() => {
      if (editor && value !== editor.getHTML()) {
        editor.commands.setContent(value);
      }
    }, [value, editor]);

    const [isPreviewOpen, setIsPreviewOpen] = useState(false);

    if (!editor) {
      return null;
    }

    return (
      <div ref={ref} className={className}>
        <div className="border-b p-2 flex gap-2 flex-wrap">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="outline" className="gap-2">
                <Type className="h-4 w-4" />
                {editor.isActive("heading", { level: 1 }) && "Heading 1"}
                {editor.isActive("heading", { level: 2 }) && "Heading 2"}
                {editor.isActive("heading", { level: 3 }) && "Heading 3"}
                {!editor.isActive("heading") && "Normal"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().setParagraph().run()}
                className="flex items-center gap-2"
              >
                <Type className="h-4 w-4" />
                Normal
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 1 }).run()
                }
                className="flex items-center gap-2"
              >
                <Heading1 className="h-4 w-4" />
                Heading 1
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 2 }).run()
                }
                className="flex items-center gap-2"
              >
                <Heading2 className="h-4 w-4" />
                Heading 2
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 3 }).run()
                }
                className="flex items-center gap-2"
              >
                <Heading3 className="h-4 w-4" />
                Heading 3
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            size="sm"
            type="button"
            variant={editor.isActive("bold") ? "default" : "outline"}
            onClick={() => editor.chain().focus().toggleBold().run()}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            type="button"
            variant={editor.isActive("italic") ? "default" : "outline"}
            onClick={() => editor.chain().focus().toggleItalic().run()}
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            type="button"
            variant={editor.isActive("bulletList") ? "default" : "outline"}
            onClick={() => editor.chain().focus().toggleBulletList().run()}
          >
            <List className="h-4 w-4" />
          </Button>
          <div className="ml-auto flex gap-2">
            <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
              <DialogContent className="max-w-4xl h-[80vh]">
                <div className="overflow-y-auto flex-1 prose max-w-none p-6 border rounded-md">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: editor?.getHTML() || "",
                    }}
                    className="
                    [&_h1]:text-3xl [&_h1]:font-bold [&_h1]:mb-4 [&_h1]:text-gray-900
                    [&_h2]:text-2xl [&_h2]:font-bold [&_h2]:mb-3 [&_h2]:text-gray-800
                    [&_h3]:text-xl [&_h3]:font-bold [&_h3]:mb-3 [&_h3]:text-gray-700
                    [&_p]:text-base [&_p]:leading-relaxed [&_p]:mb-4 [&_p]:text-gray-600
                    [&_ul]:list-disc [&_ul]:ml-4 [&_ul]:mb-4
                    [&_ol]:list-decimal [&_ol]:ml-4 [&_ol]:mb-4
                    [&_li]:mb-2
                    [&_blockquote]:border-l-4 [&_blockquote]:border-gray-300 [&_blockquote]:pl-4 [&_blockquote]:italic [&_blockquote]:text-gray-600
                    [&_hr]:my-8 [&_hr]:border-gray-200
                    [&_strong]:font-bold [&_strong]:text-gray-900
                    [&_em]:italic [&_em]:text-gray-800
                  "
                  />
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <EditorContent
          editor={editor}
          className="prose max-w-none p-4 focus-within:outline-none [&_*:focus]:outline-none [&_.ProseMirror]:min-h-[300px] [&_.ProseMirror]:focus:outline-none [&_.ProseMirror]:focus:ring-2 [&_.ProseMirror]:focus:ring-blue-500 [&_.ProseMirror]:focus:ring-offset-0
        [&_.ProseMirror_h1]:text-3xl [&_.ProseMirror_h1]:font-bold [&_.ProseMirror_h1]:mb-4 [&_.ProseMirror_h1]:text-gray-900
        [&_.ProseMirror_h2]:text-2xl [&_.ProseMirror_h2]:font-bold [&_.ProseMirror_h2]:mb-3 [&_.ProseMirror_h2]:text-gray-800
        [&_.ProseMirror_h3]:text-xl [&_.ProseMirror_h3]:font-bold [&_.ProseMirror_h3]:mb-3 [&_.ProseMirror_h3]:text-gray-700
        [&_.ProseMirror_p]:text-base [&_.ProseMirror_p]:leading-relaxed [&_.ProseMirror_p]:mb-4 [&_.ProseMirror_p]:text-gray-600
        [&_.ProseMirror_ul]:list-disc [&_.ProseMirror_ul]:ml-4 [&_.ProseMirror_ul]:mb-4
        [&_.ProseMirror_ol]:list-decimal [&_.ProseMirror_ol]:ml-4 [&_.ProseMirror_ol]:mb-4
        [&_.ProseMirror_li]:mb-2
        [&_.ProseMirror_blockquote]:border-l-4 [&_.ProseMirror_blockquote]:border-gray-300 [&_.ProseMirror_blockquote]:pl-4 [&_.ProseMirror_blockquote]:italic [&_.ProseMirror_blockquote]:text-gray-600
        [&_.ProseMirror_hr]:my-8 [&_.ProseMirror_hr]:border-gray-200
        [&_.ProseMirror_strong]:font-bold [&_.ProseMirror_strong]:text-gray-900
        [&_.ProseMirror_em]:italic [&_.ProseMirror_em]:text-gray-800"
        />
      </div>
    );
  }
);

Editor.displayName = "Editor";

export default Editor;