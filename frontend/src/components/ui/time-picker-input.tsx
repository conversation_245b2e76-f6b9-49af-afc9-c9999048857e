import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import React from "react";

interface TimePickerInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  picker?: boolean;
}

const TimePickerInput = React.forwardRef<
  HTMLInputElement,
  TimePickerInputProps
>(({ className, picker = false, ...props }, ref) => {
  return (
    <Input
      type="time"
      ref={ref}
      className={cn(
        "w-full",
        picker && "border-0 p-0",
        className
      )}
      {...props}
    />
  );
});

TimePickerInput.displayName = "TimePickerInput";

export { TimePickerInput };
