"use client";

import { useEffect, useState } from "react";
import { LayoutDashboard, Settings2 } from "lucide-react";
import { Table } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DataTableColumnVisibilityProps<TData> {
  table: Table<TData>;
  storageKey?: string;
}

export function DataTableColumnVisibility<TData>({
  table,
  storageKey = "user-table-column-visibility",
}: DataTableColumnVisibilityProps<TData>) {
  const [mounted, setMounted] = useState(false);

  // Load column visibility from localStorage on mount
  useEffect(() => {
    setMounted(true);

    if (typeof window !== "undefined") {
      const storedVisibility = localStorage.getItem(storageKey);

      if (storedVisibility) {
        try {
          const visibilityState = JSON.parse(storedVisibility);
          // Apply stored visibility state
          table.setColumnVisibility(visibilityState);
        } catch (error) {
          console.error(
            "Failed to parse column visibility from localStorage:",
            error
          );
        }
      }
    }
  }, [storageKey, table]);

  // Save column visibility to localStorage when it changes
  useEffect(() => {
    if (mounted && typeof window !== "undefined") {
      const currentVisibility = table.getState().columnVisibility;
      localStorage.setItem(storageKey, JSON.stringify(currentVisibility));
    }
  }, [table.getState().columnVisibility, storageKey, mounted]);

  // Function to get a display-friendly column name
  const getColumnDisplayName = (column: any): string => {
    // If the header is a string, use it directly
    if (typeof column.columnDef.header === "string") {
      return column.columnDef.header;
    }

    // Try to extract a name from the accessor key if available
    const def = column.columnDef as any;
    if (def.accessorKey) {
      const key = def.accessorKey.toString();
      // Get last part after dot for nested properties
      const parts = key.split(".");
      const lastPart = parts[parts.length - 1];
      // Make it more readable by adding spaces before capital letters
      return lastPart
        .replace(/([A-Z])/g, " $1") // Add space before capital letters
        .replace(/_/g, " ") // Replace underscores with spaces
        .trim()
        .toUpperCase();
    }

    // Fallback to ID with formatting
    return column.id
      .replace(/([A-Z])/g, " $1")
      .replace(/_/g, " ")
      .trim()
      .toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <LayoutDashboard className="mr-2 h-4 w-4" />
          Columns
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter((column) => column.getCanHide())
          .map((column) => {
            const displayName = getColumnDisplayName(column);

            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={(value) => column.toggleVisibility(!!value)}
              >
                {displayName}
              </DropdownMenuCheckboxItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
