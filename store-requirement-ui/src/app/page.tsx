import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">
            Store Requirements App
          </h1>
          
          <p className="text-xl text-gray-600 mb-12">
            App requirements compliance for Apple Store and Google Play Store
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
            <Link 
              href="/privacy-policy"
              className="block bg-blue-600 text-white py-6 px-8 rounded-lg hover:bg-blue-700 transition-colors shadow-md"
            >
              <h2 className="text-2xl font-semibold mb-2">Privacy Policy</h2>
              <p className="text-blue-100">
                View our privacy policy and data handling practices
              </p>
            </Link>

            <Link 
              href="/account/delete"
              className="block bg-red-600 text-white py-6 px-8 rounded-lg hover:bg-red-700 transition-colors shadow-md"
            >
              <h2 className="text-2xl font-semibold mb-2">Delete Account</h2>
              <p className="text-red-100">
                Request account deletion and data removal
              </p>
            </Link>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              This app complies with Apple App Store and Google Play Store requirements
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
