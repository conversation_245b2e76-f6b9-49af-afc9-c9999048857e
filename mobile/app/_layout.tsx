import '@/global.css';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Stack } from 'expo-router';
import { PortalProvider } from '@gorhom/portal';
import { useTranslation } from 'react-i18next';

import { GluestackUIProvider } from '@/components/ui/gluestack-ui-provider';
import '@/translation';
import { AppTypeProvider } from '@/core/contexts/AppTypeContext';
import { AuthProvider, useAuth } from '@/core/contexts/AuthContext';
import { NotificationsProvider } from '@/core/contexts/NotificationsContext';
import { LoadingScreen } from '@/components/LoadingScreen';
import StripeProvider from '@/components/payment/StripeProvider';

export const unstable_settings = {
  initialRouteName: 'auth',
};

// Root layout with authentication handling
function RootLayoutNav() {
  const { isLoading } = useAuth();
  const { t } = useTranslation();

  if (isLoading) {
    return <LoadingScreen message={t('common.authenticating')} />;
  }

  return (
    <StripeProvider>
      <Stack>
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="main" options={{ headerShown: false }} />
        <Stack.Screen name="technician" options={{ headerShown: false }} />
        <Stack.Screen name="customer" options={{ headerShown: false }} />
      </Stack>
    </StripeProvider>
  );
}

export default function RootLayout() {
  return (
    <GestureHandlerRootView className="flex flex-1">
      <PortalProvider>
        <GluestackUIProvider mode="light">
          <AppTypeProvider>
            <AuthProvider>
              <NotificationsProvider>
                <RootLayoutNav />
              </NotificationsProvider>
            </AuthProvider>
          </AppTypeProvider>
        </GluestackUIProvider>
      </PortalProvider>
    </GestureHandlerRootView>
  );
}
