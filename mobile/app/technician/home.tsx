import {
  TrendingUp,
  DollarSign,
  Star,
  Clock,
  CheckCircle2,
  AlertCircle,
  Calendar,
  Users,
  Activity,
} from 'lucide-react-native';
import { View, Text, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { useState, useEffect } from 'react';

import { MainLayout } from '@/components/layout/MainLayout';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

interface Statistics {
  todayOrders: number;
  weekOrders: number;
  monthOrders: number;
  completedOrders: number;
  pendingOrders: number;
  totalEarnings: number;
  averageRating: number;
  totalReviews: number;
  completionRate: number;
  avgServiceTime: string;
}

interface QuickStat {
  label: string;
  value: string | number;
  icon: any;
  color: string;
  trend?: number;
}

export default function TechnicianHome() {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<Statistics>({
    todayOrders: 0,
    weekOrders: 0,
    monthOrders: 0,
    completedOrders: 0,
    pendingOrders: 0,
    totalEarnings: 0,
    averageRating: 0,
    totalReviews: 0,
    completionRate: 0,
    avgServiceTime: '0h 0m',
  });

  const loadStatistics = async () => {
    if (!user) return;

    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);

      // Fetch today's orders
      const { data: todayData } = await supabase
        .from('orders')
        .select('*')
        .eq('technician_id', user.id)
        .gte('created_at', today.toISOString());

      // Fetch this week's orders
      const { data: weekData } = await supabase
        .from('orders')
        .select('*')
        .eq('technician_id', user.id)
        .gte('created_at', weekAgo.toISOString());

      // Fetch this month's orders
      const { data: monthData } = await supabase
        .from('orders')
        .select('*')
        .eq('technician_id', user.id)
        .gte('created_at', monthAgo.toISOString());

      // Fetch all orders for statistics
      const { data: allOrders } = await supabase
        .from('orders')
        .select('*')
        .eq('technician_id', user.id);

      if (allOrders) {
        const completed = allOrders.filter((order) => order.status === 'completed');
        const pending = allOrders.filter(
          (order) =>
            order.status === 'pending' ||
            order.status === 'confirmed' ||
            order.status === 'in_progress'
        );

        const totalEarnings = completed.reduce((sum, order) => sum + (order.amount || 0), 0);

        const ratings = completed
          .filter((order) => order.technician_rating)
          .map((order) => order.technician_rating);

        const averageRating =
          ratings.length > 0
            ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
            : 0;

        const completionRate =
          allOrders.length > 0 ? (completed.length / allOrders.length) * 100 : 0;

        setStatistics({
          todayOrders: todayData?.length || 0,
          weekOrders: weekData?.length || 0,
          monthOrders: monthData?.length || 0,
          completedOrders: completed.length,
          pendingOrders: pending.length,
          totalEarnings,
          averageRating,
          totalReviews: ratings.length,
          completionRate,
          avgServiceTime: '2h 15m', // This would need to be calculated from actual service times
        });
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, [user]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStatistics();
    setRefreshing(false);
  };

  const quickStats: QuickStat[] = [
    {
      label: 'Today',
      value: statistics.todayOrders,
      icon: Calendar,
      color: '#3b82f6',
      trend: 12,
    },
    {
      label: 'This Week',
      value: statistics.weekOrders,
      icon: Activity,
      color: '#10b981',
      trend: -5,
    },
    {
      label: 'Earnings',
      value: `$${statistics.totalEarnings.toFixed(2)}`,
      icon: DollarSign,
      color: '#f59e0b',
      trend: 8,
    },
    {
      label: 'Rating',
      value: statistics.averageRating.toFixed(1),
      icon: Star,
      color: '#ec4899',
      trend: 0,
    },
  ];

  const renderQuickStat = (stat: QuickStat) => (
    <View
      key={stat.label}
      className="flex-1 rounded-xl border border-gray-200 bg-white p-3 shadow-sm">
      <View className="mb-2 flex-row items-center justify-between">
        <View className="rounded-full p-1.5" style={{ backgroundColor: `${stat.color}20` }}>
          <stat.icon size={14} color={stat.color} />
        </View>
        {stat.trend !== undefined && stat.trend !== 0 && (
          <View className="flex-row items-center">
            <TrendingUp
              size={10}
              color={stat.trend > 0 ? '#10b981' : '#ef4444'}
              style={{ transform: [{ rotate: stat.trend > 0 ? '0deg' : '180deg' }] }}
            />
            <Text
              className={`ml-1 text-xs font-medium ${stat.trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
              {Math.abs(stat.trend)}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-base font-bold text-gray-900">{stat.value}</Text>
      <Text className="text-xs text-gray-500">{stat.label}</Text>
    </View>
  );

  return (
    <MainLayout scrollable={false}>
      <ScrollView
        className="flex-1 bg-gray-50"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#0284c7']} />
        }>
        {/* Header with Transparent Background */}
        <View className="mb-6 p-4">
          <Text className="text-2xl font-bold text-gray-900">
            Welcome back{user?.name ? `, ${user.name}` : ''}!
          </Text>
          <Text className="text-sm text-gray-600">Here's your performance overview</Text>
        </View>

        <View className="-mt-4 px-4">
          {/* Quick Stats Grid */}
          <View className="mb-3 flex-row" style={{ gap: 12 }}>
            {quickStats.slice(0, 2).map(renderQuickStat)}
          </View>
          <View className="mb-4 flex-row" style={{ gap: 12 }}>
            {quickStats.slice(2, 4).map(renderQuickStat)}
          </View>

          {/* Performance Overview */}
          <View className="mb-4 rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
            <View className="mb-4 flex-row items-center justify-between">
              <Text className="text-base font-semibold text-gray-900">Performance</Text>
              <TouchableOpacity>
                <Text className="text-sm text-blue-600">View Details</Text>
              </TouchableOpacity>
            </View>

            {/* Completion Rate */}
            <View className="mb-3">
              <View className="mb-2 flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <CheckCircle2 size={14} color="#10b981" />
                  <Text className="ml-2 text-sm text-gray-600">Completion Rate</Text>
                </View>
                <Text className="text-sm font-semibold text-gray-900">
                  {statistics.completionRate.toFixed(1)}%
                </Text>
              </View>
              <View className="h-2 rounded-full bg-gray-200">
                <View
                  className="h-full rounded-full bg-green-500"
                  style={{ width: `${statistics.completionRate}%` }}
                />
              </View>
            </View>

            {/* Average Service Time */}
            <View className="mb-3 flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Clock size={14} color="#3b82f6" />
                <Text className="ml-2 text-sm text-gray-600">Avg Service Time</Text>
              </View>
              <Text className="text-sm font-semibold text-gray-900">
                {statistics.avgServiceTime}
              </Text>
            </View>

            {/* Customer Reviews */}
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Users size={14} color="#f59e0b" />
                <Text className="ml-2 text-sm text-gray-600">Total Reviews</Text>
              </View>
              <Text className="text-sm font-semibold text-gray-900">{statistics.totalReviews}</Text>
            </View>
          </View>

          {/* Order Status Summary */}
          <View className="mb-4 rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
            <Text className="mb-3 text-base font-semibold text-gray-900">Order Status</Text>
            <View className="flex-row" style={{ gap: 12 }}>
              <View className="flex-1">
                <View className="rounded-lg bg-green-50 p-3">
                  <View className="flex-row items-center justify-between">
                    <CheckCircle2 size={18} color="#10b981" />
                    <Text className="text-lg font-bold text-green-600">
                      {statistics.completedOrders}
                    </Text>
                  </View>
                  <Text className="mt-1 text-xs text-gray-600">Completed</Text>
                </View>
              </View>
              <View className="flex-1">
                <View className="rounded-lg bg-yellow-50 p-3">
                  <View className="flex-row items-center justify-between">
                    <AlertCircle size={18} color="#f59e0b" />
                    <Text className="text-lg font-bold text-yellow-600">
                      {statistics.pendingOrders}
                    </Text>
                  </View>
                  <Text className="mt-1 text-xs text-gray-600">Pending</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </MainLayout>
  );
}
