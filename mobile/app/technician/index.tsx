import { useRouter } from 'expo-router';
import {
  <PERSON><PERSON><PERSON><PERSON>ist,
  Bell,
  TrendingUp,
  DollarSign,
  Star,
  Clock,
  CheckCircle2,
  AlertCircle,
  Calendar,
  Users,
  Award,
  Activity,
} from 'lucide-react-native';
import { View, Text, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { useState, useEffect } from 'react';
import { LinearGradient } from 'expo-linear-gradient';

import { MainLayout } from '@/components/layout/MainLayout';
import { HStack, VStack } from '@/components/ui';

interface Statistics {
  todayOrders: number;
  weekOrders: number;
  monthOrders: number;
  completedOrders: number;
  pendingOrders: number;
  totalEarnings: number;
  averageRating: number;
  totalReviews: number;
  completionRate: number;
  avgServiceTime: string;
}

interface QuickStat {
  label: string;
  value: string | number;
  icon: any;
  color: string;
  trend?: number;
}

export default function TechnicianHomeScreen() {
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<Statistics>({
    todayOrders: 5,
    weekOrders: 23,
    monthOrders: 87,
    completedOrders: 76,
    pendingOrders: 11,
    totalEarnings: 4250.5,
    averageRating: 4.8,
    totalReviews: 65,
    completionRate: 87.4,
    avgServiceTime: '2h 15m',
  });

  const loadStatistics = async () => {
    try {
      // TODO: Replace with actual API call
      // const { data, error } = await supabase
      //   .from('orders')
      //   .select('*')
      //   .eq('technician_id', user?.id);

      // For now using mock data
      setStatistics({
        todayOrders: Math.floor(Math.random() * 10),
        weekOrders: Math.floor(Math.random() * 30),
        monthOrders: Math.floor(Math.random() * 100),
        completedOrders: Math.floor(Math.random() * 80),
        pendingOrders: Math.floor(Math.random() * 20),
        totalEarnings: Math.random() * 5000,
        averageRating: 4 + Math.random(),
        totalReviews: Math.floor(Math.random() * 100),
        completionRate: 80 + Math.random() * 20,
        avgServiceTime: '2h 15m',
      });
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStatistics();
    setRefreshing(false);
  };

  const quickStats: QuickStat[] = [
    {
      label: 'Today',
      value: statistics.todayOrders,
      icon: Calendar,
      color: '#3b82f6',
      trend: 12,
    },
    {
      label: 'This Week',
      value: statistics.weekOrders,
      icon: Activity,
      color: '#10b981',
      trend: -5,
    },
    {
      label: 'Earnings',
      value: `$${statistics.totalEarnings.toFixed(2)}`,
      icon: DollarSign,
      color: '#f59e0b',
      trend: 8,
    },
    {
      label: 'Rating',
      value: statistics.averageRating.toFixed(1),
      icon: Star,
      color: '#ec4899',
      trend: 0,
    },
  ];

  const renderQuickStat = (stat: QuickStat) => (
    <View key={stat.label} className="flex-1 rounded-xl bg-white p-4 shadow-sm">
      <HStack className="mb-2 items-center justify-between">
        <View className={`rounded-full p-2`} style={{ backgroundColor: `${stat.color}20` }}>
          <stat.icon size={16} color={stat.color} />
        </View>
        {stat.trend !== undefined && stat.trend !== 0 && (
          <HStack className="items-center" space="xs">
            <TrendingUp
              size={12}
              color={stat.trend > 0 ? '#10b981' : '#ef4444'}
              style={{ transform: [{ rotate: stat.trend > 0 ? '0deg' : '180deg' }] }}
            />
            <Text
              className={`text-xs font-medium ${stat.trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
              {Math.abs(stat.trend)}%
            </Text>
          </HStack>
        )}
      </HStack>
      <Text className="text-lg font-bold text-gray-900">{stat.value}</Text>
      <Text className="text-xs text-gray-500">{stat.label}</Text>
    </View>
  );

  return (
    <MainLayout scrollable={false}>
      <ScrollView
        className="flex-1 bg-gray-50"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#0284c7']} />
        }>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#3b82f6', '#2563eb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="px-6 pb-8 pt-6">
          <Text className="text-3xl font-bold text-white">Welcome back!</Text>
          <Text className="mt-1 text-white/80">Here's your performance overview</Text>
        </LinearGradient>

        <View className="-mt-4 px-6">
          {/* Quick Stats Grid */}
          <HStack className="mb-6" space="sm">
            {quickStats.slice(0, 2).map(renderQuickStat)}
          </HStack>
          <HStack className="mb-6" space="sm">
            {quickStats.slice(2, 4).map(renderQuickStat)}
          </HStack>

          {/* Performance Overview */}
          <View className="mb-6 rounded-xl bg-white p-5 shadow-sm">
            <HStack className="mb-4 items-center justify-between">
              <Text className="text-lg font-semibold text-gray-900">Performance</Text>
              <TouchableOpacity>
                <Text className="text-sm text-blue-600">View Details</Text>
              </TouchableOpacity>
            </HStack>

            <VStack space="md">
              {/* Completion Rate */}
              <View>
                <HStack className="mb-2 items-center justify-between">
                  <HStack className="items-center" space="xs">
                    <CheckCircle2 size={16} color="#10b981" />
                    <Text className="text-sm text-gray-600">Completion Rate</Text>
                  </HStack>
                  <Text className="text-sm font-semibold text-gray-900">
                    {statistics.completionRate.toFixed(1)}%
                  </Text>
                </HStack>
                <View className="h-2 rounded-full bg-gray-200">
                  <View
                    className="h-full rounded-full bg-green-500"
                    style={{ width: `${statistics.completionRate}%` }}
                  />
                </View>
              </View>

              {/* Average Service Time */}
              <HStack className="items-center justify-between">
                <HStack className="items-center" space="xs">
                  <Clock size={16} color="#3b82f6" />
                  <Text className="text-sm text-gray-600">Avg Service Time</Text>
                </HStack>
                <Text className="text-sm font-semibold text-gray-900">
                  {statistics.avgServiceTime}
                </Text>
              </HStack>

              {/* Customer Reviews */}
              <HStack className="items-center justify-between">
                <HStack className="items-center" space="xs">
                  <Users size={16} color="#f59e0b" />
                  <Text className="text-sm text-gray-600">Total Reviews</Text>
                </HStack>
                <Text className="text-sm font-semibold text-gray-900">
                  {statistics.totalReviews}
                </Text>
              </HStack>
            </VStack>
          </View>

          {/* Order Status Summary */}
          <View className="mb-6 rounded-xl bg-white p-5 shadow-sm">
            <Text className="mb-4 text-lg font-semibold text-gray-900">Order Status</Text>
            <HStack space="md">
              <View className="flex-1">
                <View className="mb-2 rounded-lg bg-green-50 p-3">
                  <HStack className="items-center justify-between">
                    <CheckCircle2 size={20} color="#10b981" />
                    <Text className="text-xl font-bold text-green-600">
                      {statistics.completedOrders}
                    </Text>
                  </HStack>
                  <Text className="mt-1 text-xs text-gray-600">Completed</Text>
                </View>
              </View>
              <View className="flex-1">
                <View className="mb-2 rounded-lg bg-yellow-50 p-3">
                  <HStack className="items-center justify-between">
                    <AlertCircle size={20} color="#f59e0b" />
                    <Text className="text-xl font-bold text-yellow-600">
                      {statistics.pendingOrders}
                    </Text>
                  </HStack>
                  <Text className="mt-1 text-xs text-gray-600">Pending</Text>
                </View>
              </View>
            </HStack>
          </View>

          {/* Quick Actions */}
          <Text className="mb-3 text-lg font-semibold text-gray-900">Quick Actions</Text>
          <VStack space="md" className="mb-6">
            <TouchableOpacity
              className="flex-row items-center rounded-xl bg-blue-500 p-4 shadow-sm"
              onPress={() => {
                // @ts-ignore
                router.push('/technician/notifications');
              }}>
              <View className="mr-4 rounded-full bg-white/20 p-2">
                <Bell size={24} color="white" />
              </View>
              <View className="flex-1">
                <Text className="text-lg font-semibold text-white">Notifications</Text>
                <Text className="text-sm text-white/80">View new service requests</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-row items-center rounded-xl bg-green-500 p-4 shadow-sm"
              onPress={() => {
                // @ts-ignore
                router.push('/technician/order-history');
              }}>
              <View className="mr-4 rounded-full bg-white/20 p-2">
                <ClipboardList size={24} color="white" />
              </View>
              <View className="flex-1">
                <Text className="text-lg font-semibold text-white">My Orders</Text>
                <Text className="text-sm text-white/80">View order history and details</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-row items-center rounded-xl bg-purple-500 p-4 shadow-sm"
              onPress={() => {
                // Navigate to achievements or profile
              }}>
              <View className="mr-4 rounded-full bg-white/20 p-2">
                <Award size={24} color="white" />
              </View>
              <View className="flex-1">
                <Text className="text-lg font-semibold text-white">Achievements</Text>
                <Text className="text-sm text-white/80">Track your milestones</Text>
              </View>
            </TouchableOpacity>
          </VStack>
        </View>
      </ScrollView>
    </MainLayout>
  );
}
