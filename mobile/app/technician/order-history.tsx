import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'expo-router';
import { Clock, Wrench, Calendar, CheckCircle2, XCircle, AlertCircle } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Text,
  TouchableOpacity,
} from 'react-native';

import { MainLayout } from '@/components/layout/MainLayout';
import { HStack, VStack } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Order status type
export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'rejected';

// Order interface matching the database schema
interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: OrderStatus;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
}

export default function OrderHistoryScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch orders for the logged-in technician
  const fetchOrders = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          customer:customer_id(id, name, email, phone),
          service:service_id(id, name, price)
        `
        )
        .eq('technician_id', user.id)
        .order('created_at', { ascending: false });

      console.log('data', JSON.stringify(data, null, 2));

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      setOrders(data as Order[]);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load orders on component mount
  useEffect(() => {
    fetchOrders();
  }, [user]);

  // Pull-to-refresh handler
  const onRefresh = () => {
    setRefreshing(true);
    fetchOrders();
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: OrderStatus }) => {
    let bgColor = '';
    const textColor = 'text-white';
    let icon = null;

    switch (status) {
      case 'pending':
        bgColor = 'bg-yellow-500';
        icon = <Clock size={14} color="white" />;
        break;
      case 'confirmed':
        bgColor = 'bg-blue-500';
        icon = <Calendar size={14} color="white" />;
        break;
      case 'in_progress':
        bgColor = 'bg-orange-500';
        icon = <Wrench size={14} color="white" />;
        break;
      case 'completed':
        bgColor = 'bg-green-500';
        icon = <CheckCircle2 size={14} color="white" />;
        break;
      case 'cancelled':
      case 'rejected':
        bgColor = 'bg-red-500';
        icon = <XCircle size={14} color="white" />;
        break;
      default:
        bgColor = 'bg-gray-500';
        icon = <AlertCircle size={14} color="white" />;
    }

    return (
      <Badge className={`${bgColor} h-6 px-2`}>
        <HStack space="xs" className="items-center">
          {icon}
          <Text className={`text-xs font-medium ${textColor} capitalize`}>
            {status.replace('_', ' ')}
          </Text>
        </HStack>
      </Badge>
    );
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Invalid date';
    }
  };

  // Format price
  const formatPrice = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Render empty state
  const renderEmptyState = () => {
    if (loading) {
      return (
        <View className="flex-1 items-center justify-center p-4">
          <ActivityIndicator size="large" color="#0284c7" />
          <Text className="mt-4 text-gray-600">Loading orders...</Text>
        </View>
      );
    }

    return (
      <View className="flex-1 items-center justify-center p-4">
        <Text className="text-xl font-semibold text-gray-800">No Orders Found</Text>
        <Text className="mt-2 text-center text-gray-600">
          You don't have any assigned orders yet. New orders will appear here once they are assigned
          to you.
        </Text>
      </View>
    );
  };

  // Render an order item
  const renderOrderItem = ({ item }: { item: Order }) => {
    const vehicleInfo = item.order_data?.vehicle_info;
    const vehicleText = vehicleInfo
      ? `${vehicleInfo.brand || ''} ${vehicleInfo.model || ''} ${vehicleInfo.year || ''}`.trim()
      : 'No vehicle info';

    return (
      <TouchableOpacity
        className="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
        onPress={() => {
          // Navigate to order details screen
          router.push(`/technician/order/${item.id}`);
        }}>
        <HStack className="mb-2 items-center justify-between">
          <Text className="text-lg font-bold text-gray-800">{item.code}</Text>
          <StatusBadge status={item.status} />
        </HStack>

        <VStack space="xs" className="mb-3">
          <Text className="text-base font-medium text-gray-800">
            {item.service?.name || 'Unknown Service'}
          </Text>
          <Text className="text-sm text-gray-600">
            Customer: {item.customer?.name || 'Unknown'}
          </Text>
          <Text className="text-sm text-gray-600">Vehicle: {vehicleText}</Text>
          {item.order_data?.location && (
            <Text className="text-sm text-gray-600">Location: {item.order_data.location}</Text>
          )}
        </VStack>

        <HStack className="justify-between border-t border-gray-100 pt-2">
          <Text className="text-sm text-gray-500">
            Scheduled: {formatRelativeTime(item.scheduled_at)}
          </Text>
          <Text className="font-medium text-blue-600">{formatPrice(item.amount)}</Text>
        </HStack>
      </TouchableOpacity>
    );
  };

  return (
    <MainLayout scrollable={false}>
      <View className="flex-1 p-6">
        <Text className="mb-6 text-2xl font-bold">My Orders</Text>

        <FlatList
          data={orders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#0284c7']} />
          }
        />
      </View>
    </MainLayout>
  );
}
