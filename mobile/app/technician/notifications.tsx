import { <PERSON>, MapP<PERSON>, Bell } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

import { MainLayout } from '@/components/layout/MainLayout';
import { useNotification } from '@/services/toast-service';
import { Button, ButtonText } from '@/components/ui';
import {
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';

export default function TechnicianNotificationsScreen() {
  const { showToast } = useNotification();
  const [upcomingJob] = useState({
    id: '1',
    title: 'Air Conditioner Repair',
    address: '123 Nguyen Van Linh, District 7, HCMC',
    scheduledTime: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
    customer: '<PERSON>',
    phone: '0987654321',
  });

  const [showAlert, setShowAlert] = useState(false);
  const [isLocationTracked, setIsLocationTracked] = useState(false);
  const [countdown, setCountdown] = useState(30); // 30 minutes countdown

  useEffect(() => {
    // Show notification when page is loaded
    setTimeout(() => {
      setShowAlert(true);
    }, 1000);

    // Countdown timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 60000); // update every minute

    return () => clearInterval(timer);
  }, []);

  const handleConfirm = () => {
    setShowAlert(false);
    setIsLocationTracked(true);
    showToast(
      'Your location is being tracked until the job is completed',
      'info',
      'Location Tracking'
    );
  };

  const handleCompleteJob = () => {
    setIsLocationTracked(false);
    showToast('Job completed. Location tracking has stopped', 'success');
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <MainLayout>
      <ScrollView className="flex-1 bg-gray-50">
        <View className="p-6">
          <Text className="mb-6 text-2xl font-bold">Notifications</Text>

          {/* Upcoming job notification */}
          <View className="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <View className="mb-2 flex-row items-center">
              <Bell size={20} color="#3b82f6" />
              <Text className="ml-2 text-lg font-semibold">Upcoming Job</Text>
            </View>

            <View className="mb-4 mt-2">
              <Text className="text-base font-bold">{upcomingJob.title}</Text>
              <View className="mt-2 flex-row items-center">
                <Clock size={16} color="#6b7280" />
                <Text className="ml-2 text-gray-600">
                  {formatDate(upcomingJob.scheduledTime)} {formatTime(upcomingJob.scheduledTime)} (
                  {countdown} minutes left)
                </Text>
              </View>
              <View className="mt-2 flex-row items-center">
                <MapPin size={16} color="#6b7280" />
                <Text className="ml-2 text-gray-600">{upcomingJob.address}</Text>
              </View>
            </View>

            <TouchableOpacity
              onPress={() => setShowAlert(true)}
              className="rounded-md bg-blue-500 p-3">
              <Text className="text-center font-semibold text-white">Confirm Job</Text>
            </TouchableOpacity>
          </View>

          {/* Location tracking status */}
          {isLocationTracked && (
            <View className="rounded-lg bg-blue-50 p-4">
              <View className="mb-2 flex-row items-center">
                <MapPin size={20} color="#3b82f6" />
                <Text className="ml-2 text-base font-semibold text-blue-700">
                  Location Tracking Active
                </Text>
              </View>
              <Text className="mb-3 text-blue-600">
                Your location is being tracked until the job is completed
              </Text>
              <TouchableOpacity onPress={handleCompleteJob} className="rounded-md bg-green-500 p-3">
                <Text className="text-center font-semibold text-white">Complete Job</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Confirmation Alert Dialog */}
      <AlertDialog isOpen={showAlert} onClose={() => setShowAlert(false)}>
        <AlertDialogBackdrop />
        <AlertDialogContent>
          <AlertDialogHeader>
            <Text className="text-lg font-bold">Confirm Job</Text>
          </AlertDialogHeader>
          <AlertDialogBody>
            <Text className="mb-2">
              You have a job "{upcomingJob.title}" scheduled at{' '}
              {formatTime(upcomingJob.scheduledTime)} on {formatDate(upcomingJob.scheduledTime)}.
            </Text>
            <Text className="mb-2">Address: {upcomingJob.address}</Text>
            <Text className="mb-4">
              If confirmed, your location will be tracked until the job is completed.
            </Text>
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              variant="outline"
              action="secondary"
              onPress={() => setShowAlert(false)}
              className="mr-3">
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button action="primary" onPress={handleConfirm}>
              <ButtonText>Confirm</ButtonText>
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MainLayout>
  );
}
