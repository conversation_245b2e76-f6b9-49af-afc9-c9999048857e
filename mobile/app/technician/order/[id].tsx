import { formatDistanceToNow, parseISO, differenceInMinutes } from 'date-fns';
import { useLocalSearchParams, useRouter } from 'expo-router';
import {
  MapPin,
  Clock,
  User,
  Wrench,
  CheckCircle,
  AlertCircle,
  ChevronLeft,
  Calendar,
  DollarSign,
  Tag,
  MessageSquare,
  Map,
  Navigation,
} from 'lucide-react-native';
import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  StatusBar,
  RefreshControl,
  Platform,
  SafeAreaView,
  Linking,
  Animated,
  Easing,
} from 'react-native';

import { CustomerFeedbackView } from '@/components/technician/CustomerFeedbackView';
import { MainLayout } from '@/components/layout/MainLayout';
import { LocationHistoryMap } from '@/components/map/LocationHistoryMap';
import { useNotification } from '@/services/toast-service';
import { Button, ButtonText } from '@/components/ui';
import {
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { useAuth } from '@/core/contexts/AuthContext';
import { useNotifications } from '@/core/contexts/NotificationsContext';
import { supabase } from '@/lib/supabase';
import locationService from '@/services/location-service';

// Get screen dimensions for responsive layouts (unused)
// const { width: _SCREEN_WIDTH } = Dimensions.get('window');
const IS_IOS = Platform.OS === 'ios';

// Theme and UI constants
const THEME = {
  radius: {
    sm: 4,
    md: 6,
    lg: 8,
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
  },
  statusBar: {
    light: {
      barStyle: 'light-content' as const,
      backgroundColor: '#0284c7',
    },
    dark: {
      barStyle: 'dark-content' as const,
      backgroundColor: '#ffffff',
    },
  },
};

// Order interface matching the database schema
interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: string;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  feedback_id?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
}

// Status badge component for consistent styling
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          icon: <CheckCircle size={12} color="#15803d" />,
        };
      case 'in_progress':
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-800',
          icon: <Clock size={12} color="#1e40af" />,
        };
      case 'confirmed':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          icon: <AlertCircle size={12} color="#854d0e" />,
        };
      default:
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-800',
          icon: <Tag size={12} color="#4b5563" />,
        };
    }
  };

  const config = getStatusConfig();

  return (
    <View
      className={`flex-row items-center rounded-md px-2 py-1 ${config.bg}`}
      accessibilityLabel={`Status: ${status.replace('_', ' ')}`}>
      {config.icon}
      <Text className={`ml-1 font-medium ${config.text} text-xs`}>
        {status.replace('_', ' ').toUpperCase()}
      </Text>
    </View>
  );
};

// Section header component for consistent styling
const SectionHeader = ({
  icon,
  title,
  rightElement,
}: {
  icon: React.ReactNode;
  title: string;
  rightElement?: React.ReactNode;
}) => (
  <View className="mb-2 flex-row items-center justify-between">
    <View className="flex-row items-center">
      <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-sky-50">{icon}</View>
      <Text className="text-sm font-semibold text-gray-800">{title}</Text>
    </View>
    {rightElement}
  </View>
);

// Info card component for consistent card styling (unused)
// const InfoCard = ({
//   children,
//   isFirst = false,
//   isLast = false,
// }: {
//   children: React.ReactNode;
//   isFirst?: boolean;
//   isLast?: boolean;
// }) => (
//   <View
//     className={`border-l border-r border-gray-200 bg-white p-3 ${
//       isFirst ? 'rounded-t-md border-t' : ''
//     } ${isLast ? 'mb-3 rounded-b-md border-b' : ''}`}>
//     {children}
//   </View>
// );

// Info row component for consistent styling of information rows
const InfoRow = ({
  icon,
  label,
  value,
  isHighlighted = false,
}: {
  icon: React.ReactNode;
  label: string;
  value: string;
  isHighlighted?: boolean;
}) => (
  <View className="mb-2 flex-row items-center">
    <View
      className={`mr-2 h-6 w-6 items-center justify-center rounded-md ${
        isHighlighted ? 'bg-blue-50' : 'bg-gray-50'
      }`}>
      {icon}
    </View>
    <View className="flex-1">
      <Text className="text-xs text-gray-500">{label}</Text>
      <Text
        className={`${isHighlighted ? 'text-blue-800' : 'text-gray-800'} ${
          value.length > 40 ? 'text-xs' : 'text-sm'
        } ${isHighlighted ? 'font-medium' : ''}`}>
        {value}
      </Text>
    </View>
  </View>
);

// Action button component for consistent styling
const ActionButton = ({
  text,
  onPress,
  variant = 'primary',
  icon,
  disabled = false,
  compact = false,
}: {
  text: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'warning';
  icon?: React.ReactNode;
  disabled?: boolean;
  compact?: boolean;
}) => {
  const getBgColor = () => {
    if (disabled) return 'bg-gray-200';
    switch (variant) {
      case 'primary':
        return 'bg-blue-500';
      case 'secondary':
        return 'bg-gray-500';
      case 'success':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      className={`mb-2 rounded-md ${compact ? 'px-3 py-2' : 'px-3 py-2.5'} flex-row items-center justify-center ${getBgColor()} ${
        disabled ? 'opacity-60' : ''
      }`}
      accessibilityRole="button"
      accessibilityLabel={text}
      accessibilityState={{ disabled }}>
      {icon && <View className="mr-1.5">{icon}</View>}
      <Text className={`text-center font-medium text-white ${compact ? 'text-xs' : 'text-sm'}`}>
        {text}
      </Text>
    </TouchableOpacity>
  );
};

// Blinking Dot Component
const BlinkingDot = () => {
  const opacity = useState(new Animated.Value(0))[0];

  useEffect(() => {
    const blink = Animated.sequence([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 600,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0.3,
        duration: 600,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
    ]);

    Animated.loop(blink).start();

    return () => {
      opacity.stopAnimation();
    };
  }, []);

  return (
    <Animated.View
      style={{
        opacity,
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: '#2563eb',
        marginRight: 4,
      }}
    />
  );
};

export default function OrderDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { showToast } = useNotification();
  const { sendOrderStatusUpdate, sendTechnicianArrivalNotice } = useNotifications();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [isLocationTracked, setIsLocationTracked] = useState(false);
  const [hasLocationData, setHasLocationData] = useState(false);
  const [minutesRemaining, setMinutesRemaining] = useState(0);
  const [showUpcomingNotice, setShowUpcomingNotice] = useState(false);
  const [statusBarStyle, setStatusBarStyle] = useState<'light-content' | 'dark-content'>(
    IS_IOS ? 'dark-content' : 'light-content'
  );
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Fetch order details
  const fetchOrder = async (showLoadingState = true) => {
    if (!user || !id) return;

    try {
      if (showLoadingState) setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          customer:customer_id(id, name, email, phone),
          service:service_id(id, name, price),
          feedback:feedback_id(id)
        `
        )
        .eq('id', id)
        .eq('technician_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching order:', error);
        showToast('Error loading order details', 'error');
        return;
      }

      setOrder(data as Order);

      // Check if order is upcoming
      if (data) {
        const scheduledTime = parseISO(data.scheduled_at);
        const now = new Date();
        const minutes = differenceInMinutes(scheduledTime, now);

        // If order is scheduled within 30 minutes, show upcoming notice
        if (minutes > 0 && minutes <= 30) {
          setMinutesRemaining(minutes);
          setShowUpcomingNotice(true);

          // Show confirmation dialog after a short delay
          setTimeout(() => {
            setShowAlert(true);
          }, 1000);
        }
      }

      // Check if location tracking is already active and if there's location data
      await checkLocationTrackingStatus();
      await checkLocationData();
    } catch (error) {
      console.error('Failed to fetch order:', error);
      showToast('Error loading order details', 'error');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Check if there's any location data for this order
  const checkLocationData = async () => {
    if (!user || !id) return;

    try {
      const { data } = await supabase
        .from('technician_locations')
        .select('locations')
        .eq('technician_id', user.id)
        .eq('order_id', id)
        .maybeSingle();

      if (data && data.locations && data.locations.length > 0) {
        setHasLocationData(true);
      }
    } catch (error) {
      console.error('Error checking location data:', error);
    }
  };

  // Pull to refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchOrder(false);
  }, [id, user]);

  // Check location tracking status for this order
  const checkLocationTrackingStatus = async () => {
    if (!user || !id) return;

    try {
      const { data } = await supabase
        .from('technician_locations')
        .select('id, is_active')
        .eq('technician_id', user.id)
        .eq('order_id', id)
        .eq('is_active', true)
        .maybeSingle();

      if (data && data.is_active) {
        setIsLocationTracked(true);
      }
    } catch (error) {
      console.error('Error checking location tracking status:', error);
    }
  };

  // Start location tracking when order is confirmed
  const handleConfirmOrder = async () => {
    try {
      if (!order) return;

      setIsUpdatingStatus(true);
      const newStatus = 'in_progress';
      await updateOrderStatus(newStatus);

      // Start location tracking
      if (user && order.id) {
        const trackingStarted = await locationService.startTracking(order.id, user.id);
        if (trackingStarted) {
          setIsLocationTracked(true);
          showToast('Location tracking started', 'success');
        }
      }

      // Send notification to customer about status change
      if (order.customer_id) {
        await sendOrderStatusUpdate(
          order.customer_id,
          order.id,
          'in progress',
          'Your service technician has confirmed your order and will arrive as scheduled.'
        );
      }

      // Schedule a reminder for the technician
      // if (order.scheduled_at) {
      //   await sendOrderReminder(order.id, new Date(order.scheduled_at), {
      //     title: order.code || `Order #${order.id}`,
      //     service: order.service?.name || 'Service',
      //   });
      // }

      showToast('Order successfully confirmed', 'success');
    } catch (error) {
      console.error('Error confirming order:', error);
      showToast('Failed to confirm order', 'error');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Update order status
  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return false;

    try {
      const updateData: any = {
        status: newStatus,
      };

      if (newStatus === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      const { error } = await supabase.from('orders').update(updateData).eq('id', order.id);

      if (error) {
        console.error('Error updating order status:', error);
        showToast('Failed to update order status', 'error');
        return false;
      }

      // Update local state
      setOrder({
        ...order,
        status: newStatus,
        ...(newStatus === 'completed' ? { completed_at: new Date().toISOString() } : {}),
      });

      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      return false;
    }
  };

  // Complete the job and stop location tracking
  const handleCompleteJob = async () => {
    if (!user || !order) return;

    try {
      // Stop location tracking
      const success = await locationService.stopTracking();

      if (success) {
        setIsLocationTracked(false);

        // Update order status to completed
        const statusUpdated = await updateOrderStatus('completed');

        if (statusUpdated) {
          showToast('Job completed successfully', 'success');
        }
      } else {
        showToast('Failed to stop location tracking', 'error');
      }
    } catch (error) {
      console.error('Error completing job:', error);
      showToast('An error occurred', 'error');
    }
  };

  // Toggle status bar style
  const toggleStatusBarStyle = useCallback(() => {
    setStatusBarStyle((prev) => (prev === 'dark-content' ? 'light-content' : 'dark-content'));
  }, []);

  // Send arrival notification to customer
  const sendArrivalNotification = async (estimatedTime: number = 15) => {
    try {
      if (order?.customer_id && order?.id) {
        await sendTechnicianArrivalNotice(order.customer_id, order.id, estimatedTime);
        showToast('Customer notified of your arrival', 'success');
      }
    } catch (error) {
      console.error('Error sending arrival notification:', error);
      showToast('Failed to send arrival notification', 'error');
    }
  };

  // Start location tracking manually
  const handleStartTracking = async () => {
    try {
      if (!user || !order) return;

      setIsUpdatingStatus(true);
      const trackingStarted = await locationService.startTracking(order.id, user.id);

      if (trackingStarted) {
        setIsLocationTracked(true);
        showToast('Location tracking started successfully', 'success');
      } else {
        showToast('Failed to start location tracking', 'error');
      }
    } catch (error) {
      console.error('Error starting location tracking:', error);
      showToast('An error occurred', 'error');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  useEffect(() => {
    fetchOrder();
  }, [id, user]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      day: 'numeric',
      month: 'short',
      hour: 'numeric',
      minute: 'numeric',
    });
  };

  // Add this function inside the component
  const openDirections = (location: string | undefined) => {
    if (!location) return;

    const [lat, lng] = location.split(',').map((coord) => coord.trim());
    const url = Platform.select({
      ios: `maps://app?saddr=Current+Location&daddr=${lat},${lng}`,
      android: `google.navigation:q=${lat},${lng}`,
      default: `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`,
    });

    if (!url) return;

    Linking.canOpenURL(url).then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        // Fallback to web Google Maps
        Linking.openURL(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`);
      }
    });
  };

  if (loading) {
    return (
      <MainLayout>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={
            statusBarStyle === 'dark-content'
              ? THEME.statusBar.dark.backgroundColor
              : THEME.statusBar.light.backgroundColor
          }
        />
        <View className="flex-1 items-center justify-center bg-gray-50">
          <ActivityIndicator size="large" color="#0284c7" />
          <Text className="mt-2 text-sm text-gray-600">Loading order details...</Text>
        </View>
      </MainLayout>
    );
  }

  if (!order) {
    return (
      <MainLayout>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={
            statusBarStyle === 'dark-content'
              ? THEME.statusBar.dark.backgroundColor
              : THEME.statusBar.light.backgroundColor
          }
        />
        <View className="flex-1 items-center justify-center bg-gray-50 p-4">
          <AlertCircle size={40} color="#ef4444" />
          <Text className="mt-3 text-lg font-semibold text-gray-800">Order Not Found</Text>
          <Text className="mt-1 text-center text-sm text-gray-600">
            This order does not exist or you don't have permission to view it.
          </Text>
          <TouchableOpacity
            className="mt-4 rounded-md bg-blue-500 px-3 py-2"
            onPress={() => router.back()}>
            <Text className="text-sm font-medium text-white">Go Back</Text>
          </TouchableOpacity>
        </View>
      </MainLayout>
    );
  }

  // Format vehicle information
  const vehicleInfo = order.order_data?.vehicle_info;
  const vehicleBrand = vehicleInfo?.brand || '';
  const vehicleModel = vehicleInfo?.model || '';
  const vehicleYear = vehicleInfo?.year || '';
  const licensePlate = vehicleInfo?.license_plate || '';

  const vehicleText =
    [vehicleBrand, vehicleModel, vehicleYear].filter(Boolean).join(' ') || 'No vehicle info';
  const fullVehicleText =
    vehicleText === 'No vehicle info'
      ? vehicleText
      : `${vehicleText}${licensePlate ? ` (${licensePlate})` : ''}`;

  // Get appropriate action button based on order status
  const getActionButton = () => {
    if (!order) return null;

    switch (order.status) {
      case 'confirmed':
        return (
          <Button
            variant="solid"
            action="primary"
            onPress={handleConfirmOrder}
            isDisabled={isUpdatingStatus}>
            <ButtonText>{isUpdatingStatus ? 'Confirming...' : 'Confirm Order'}</ButtonText>
          </Button>
        );
      case 'in_progress':
        return (
          <View className="space-y-2">
            <Button
              variant="solid"
              action="primary"
              onPress={handleCompleteJob}
              isDisabled={isUpdatingStatus}>
              <ButtonText>{isUpdatingStatus ? 'Completing...' : 'Complete Job'}</ButtonText>
            </Button>
            {!isLocationTracked && (
              <Button
                variant="outline"
                action="primary"
                onPress={handleStartTracking}
                isDisabled={isUpdatingStatus}>
                <ButtonText>Start Location Tracking</ButtonText>
              </Button>
            )}
            <Button
              variant="outline"
              action="secondary"
              onPress={() => sendArrivalNotification(15)}
              isDisabled={isUpdatingStatus}>
              <ButtonText>Notify Customer of Arrival</ButtonText>
            </Button>
          </View>
        );
      default:
        return null;
    }
  };

  const getScheduledTimeIndicator = () => {
    const now = new Date();
    const scheduledTime = parseISO(order.scheduled_at);
    const timeDiff = differenceInMinutes(scheduledTime, now);

    if (order.status === 'completed') {
      return null;
    }

    if (timeDiff < 0 && timeDiff > -60) {
      // Overdue by less than an hour
      return (
        <Text className="text-xs font-medium text-red-600">
          Overdue by {Math.abs(timeDiff)} minutes
        </Text>
      );
    } else if (timeDiff > 0 && timeDiff < 60) {
      // Coming up in less than an hour
      return (
        <Text className="text-xs font-medium text-yellow-600">Starting in {timeDiff} minutes</Text>
      );
    }

    return null;
  };

  return (
    <MainLayout scrollable={false}>
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={
          statusBarStyle === 'dark-content'
            ? THEME.statusBar.dark.backgroundColor
            : THEME.statusBar.light.backgroundColor
        }
      />

      <SafeAreaView className="flex-1 bg-white">
        {/* Header bar with back button and status bar toggle */}
        <View className="flex-row items-center justify-between border-b border-gray-200 bg-white px-3 py-2">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="-ml-1.5 p-1.5"
              accessibilityRole="button"
              accessibilityLabel="Go back">
              <ChevronLeft size={20} color="#0f172a" />
            </TouchableOpacity>
            <Text className="ml-1.5 text-base font-medium text-gray-900">#{order.code}</Text>
          </View>
          <TouchableOpacity
            onPress={toggleStatusBarStyle}
            className="rounded-full bg-gray-100 p-1.5"
            accessibilityLabel="Toggle status bar style">
            <Clock size={14} color="#4b5563" />
          </TouchableOpacity>
        </View>

        <ScrollView
          className="flex-1 bg-gray-50"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0284c7']}
              tintColor="#0284c7"
            />
          }>
          {/* Service and Status Section - Compact header with critical information */}
          <View className="mb-2 border-b border-gray-200 bg-white p-3">
            <View className="mb-1 flex-row items-center justify-between">
              <StatusBadge status={order.status} />
              <Text className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(order.created_at), { addSuffix: true })}
              </Text>
            </View>
            <Text className="text-lg font-bold text-gray-900">{order.service?.name}</Text>

            <View className="mt-1.5 flex-row items-center justify-between">
              {order.amount > 0 && (
                <View className="flex-row items-center">
                  <DollarSign size={14} color="#6b7280" />
                  <Text className="ml-0.5 text-sm text-gray-700">
                    {order.amount.toLocaleString()}
                  </Text>
                </View>
              )}
              {getScheduledTimeIndicator()}
            </View>
          </View>

          {/* Alerts Section (Upcoming job notice and location tracking) */}
          {(showUpcomingNotice || isLocationTracked) && (
            <View className="mb-1 px-3">
              {/* Upcoming job notice */}
              {showUpcomingNotice && (
                <View className="mb-2 rounded-md border border-yellow-200 bg-yellow-50 p-2.5">
                  <View className="mb-1.5 flex-row items-center">
                    <Clock size={14} color="#d97706" />
                    <Text className="ml-1.5 text-sm font-medium text-yellow-800">
                      Job starts in {minutesRemaining} minutes
                    </Text>
                  </View>
                  <ActionButton
                    text="Confirm Now"
                    onPress={() => setShowAlert(true)}
                    variant="warning"
                    compact
                  />
                </View>
              )}

              {/* Location tracking status */}
              {isLocationTracked && (
                <View className="mb-2 rounded-md border border-blue-100 bg-blue-50 p-2.5">
                  <View className="flex-row items-center">
                    <MapPin size={14} color="#3b82f6" />
                    <Text className="ml-1.5 flex-1 text-sm font-medium text-blue-700">
                      Live location sharing active
                    </Text>
                  </View>
                </View>
              )}
            </View>
          )}

          {/* Primary Action Button */}
          <View className="mb-2 px-3">{getActionButton()}</View>

          {/* Main Information Grid - Organized in logical sections */}
          <View className="px-3">
            {/* Customer + Vehicle compact section */}
            <View className="mb-2 rounded-md border border-gray-200 bg-white">
              <View className="border-b border-gray-100 p-2.5">
                <SectionHeader
                  icon={<User size={14} color="#0369a1" />}
                  title="Customer & Vehicle"
                />

                {/* Two-column layout for customer info */}
                <View className="flex-row">
                  <View className="mr-2 flex-1">
                    <Text className="mb-0.5 text-xs text-gray-500">Name</Text>
                    <Text className="text-xs text-gray-800">
                      {order.customer?.name || 'Unknown'}
                    </Text>
                  </View>

                  {order.customer?.phone && (
                    <View className="flex-1">
                      <Text className="mb-0.5 text-xs text-gray-500">Phone</Text>
                      <Text className="text-xs font-medium text-blue-800">
                        {order.customer.phone}
                      </Text>
                    </View>
                  )}
                </View>

                {/* Vehicle info */}
                <View className="mt-2">
                  <Text className="mb-0.5 text-xs text-gray-500">Vehicle</Text>
                  <Text className="text-xs text-gray-800">{fullVehicleText}</Text>
                </View>
              </View>

              {/* Location section */}
              <View className="p-2.5">
                <Text className="mb-0.5 text-xs text-gray-500">Service Location</Text>
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-start">
                    {/* <MapPin size={12} color="#4b5563" className="mr-1 mt-0.5" /> */}
                    <Text className="flex-1 text-xs text-gray-800">
                      {order.order_data?.location || 'No location provided'}
                    </Text>
                  </View>
                  {order.order_data?.location && (
                    <TouchableOpacity
                      onPress={() => openDirections(order.order_data?.location)}
                      className="ml-2 h-8 w-8 flex-row items-center rounded-md bg-blue-50 px-2 py-1">
                      <Navigation size={12} color="#2563eb" className="mr-2 h-8 w-8" />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>

            {/* Order details and schedule section */}
            <View className="mb-2 rounded-md border border-gray-200 bg-white">
              <View className="border-b border-gray-100 p-2.5">
                <SectionHeader icon={<Clock size={14} color="#0369a1" />} title="Schedule" />

                <View className="flex-row justify-between">
                  <View className="mr-2 flex-1">
                    <Text className="mb-0.5 text-xs text-gray-500">Scheduled</Text>
                    <Text className="text-xs text-gray-800">{formatDate(order.scheduled_at)}</Text>
                  </View>

                  {order.completed_at && (
                    <View className="flex-1">
                      <Text className="mb-0.5 text-xs text-gray-500">Completed</Text>
                      <Text className="text-xs text-green-800">
                        {formatDate(order.completed_at)}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>

            {/* Notes & Requests Section - Only shown if data exists */}
            {(order.order_data?.special_requests || order.note) && (
              <View className="mb-3 rounded-md border border-gray-200 bg-white">
                <View className="p-2.5">
                  <SectionHeader
                    icon={<MessageSquare size={14} color="#0369a1" />}
                    title="Notes & Requests"
                  />

                  {order.order_data?.special_requests && (
                    <View className="mb-2">
                      <Text className="mb-0.5 text-xs text-gray-500">Special Requests</Text>
                      <Text className="text-xs text-gray-800">
                        {order.order_data.special_requests}
                      </Text>
                    </View>
                  )}

                  {order.note && (
                    <View>
                      <Text className="mb-0.5 text-xs text-gray-500">Notes</Text>
                      <Text className="text-xs text-gray-800">{order.note}</Text>
                    </View>
                  )}
                </View>
              </View>
            )}

            {/* Customer Feedback - Only shown if feedback exists */}
            {order.feedback_id && (
              <View className="mb-3">
                <CustomerFeedbackView feedbackId={order.feedback_id} />
              </View>
            )}

            {/* Location History Map - Show when there's location data */}
            {order.order_data?.location && (hasLocationData || isLocationTracked) && (
              <View className="mb-2 rounded-md border border-gray-200 bg-white">
                <View className="border-b border-gray-100 p-2.5">
                  <SectionHeader
                    icon={<Map size={14} color="#0369a1" />}
                    title="Location History"
                    rightElement={
                      order.status === 'completed' ? (
                        <Text className="text-xs text-gray-500">Job completed</Text>
                      ) : isLocationTracked ? (
                        <View className="flex-row items-center">
                          <BlinkingDot />
                          <Text className="text-xs text-blue-600">Live tracking</Text>
                        </View>
                      ) : null
                    }
                  />

                  {/* Map */}
                  {user && (
                    <LocationHistoryMap
                      orderId={order.id}
                      technicianId={user.id}
                      initialLocation={{
                        latitude: parseFloat(order.order_data.location.split(',')[0]),
                        longitude: parseFloat(order.order_data.location.split(',')[1]),
                      }}
                    />
                  )}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* Confirmation Alert Dialog */}
      <AlertDialog isOpen={showAlert} onClose={() => setShowAlert(false)}>
        <AlertDialogBackdrop />
        <AlertDialogContent>
          <AlertDialogHeader>
            <Text className="text-center text-base font-bold">Confirm Job</Text>
          </AlertDialogHeader>
          <AlertDialogBody>
            <View className="mb-3">
              <InfoRow
                icon={<Wrench size={16} color="#6b7280" />}
                label="Service"
                value={order.service?.name || ''}
              />
              <InfoRow
                icon={<Calendar size={16} color="#6b7280" />}
                label="Time"
                value={formatDate(order.scheduled_at)}
              />
              <InfoRow
                icon={<MapPin size={16} color="#6b7280" />}
                label="Location"
                value={order.order_data?.location || 'No location provided'}
              />
            </View>
            <Text className="mb-3 text-center text-sm font-medium text-blue-700">
              Your location will be shared with the customer
            </Text>
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              variant="outline"
              action="secondary"
              onPress={() => setShowAlert(false)}
              className="mr-2">
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button action="primary" onPress={handleConfirmOrder}>
              <ButtonText>Confirm</ButtonText>
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MainLayout>
  );
}
