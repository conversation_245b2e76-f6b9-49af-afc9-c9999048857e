import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { User, Mail, Lock, Phone, MapPin, Calendar } from 'lucide-react-native';
import { useForm } from 'react-hook-form';
import { ActivityIndicator, Alert } from 'react-native';

import { AuthLayout } from '../_layout';

import CertificateUploader from './CertificateUploader';

import { TechnicianFormValues, createSchemas } from '@/schemas/auth-schema';
import { InputGroup } from '@/components/form';
import { Button, ButtonText, Text, Pressable, VStack } from '@/components/ui';
import { useAuth } from '@/core/contexts/AuthContext';

export default function TechnicianRegisterScreen() {
  const router = useRouter();
  const { technicianSchema } = createSchemas((key) => key);
  const { register, isLoading } = useAuth();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<TechnicianFormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(technicianSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      password: '',
      confirmPassword: '',
      role: 'technician',
      exp: 0,
      certs: [],
    },
  });

  const certificates = watch('certs');

  const onSubmit = async (data: TechnicianFormValues) => {
    try {
      // Check if certificates are uploaded
      if (!data.certs || data.certs.length === 0) {
        Alert.alert('Validation Error', 'Please upload at least one certificate');
        return;
      }

      // Check if passwords match
      if (data.password !== data.confirmPassword) {
        Alert.alert('Validation Error', 'Passwords do not match');
        return;
      }

      // Register the user using useAuth context with profile data
      await register(data.name, data.email, data.password, data.phone, data.address, 'technician', {
        rating: 0,
        exp: data.exp,
        certs: data.certs,
      });

      // Note: The register function in AuthContext already handles:
      // - Showing error alerts
      // - Email verification notification
      // - Redirecting to the login page
    } catch (error) {
      console.error('Registration failed', error);
      // Auth context already shows alerts, no need for duplicate alerts here
    }
  };

  console.log('Certs', certificates);

  return (
    <AuthLayout title="Register" subtitle="Create a technician account">
      <VStack space="xl">
        <VStack space="md">
          <InputGroup
            control={control}
            name="name"
            label="Full Name"
            placeholder="Enter your full name"
            leftIcon={User}
            error={errors.name}
            maxLength={50}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="email"
            label="Email Address"
            placeholder="Enter your email"
            helperText="We'll send you a confirmation email"
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon={Mail}
            error={errors.email}
            maxLength={50}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="phone"
            label="Phone Number"
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            leftIcon={Phone}
            error={errors.phone}
            maxLength={15}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="address"
            label="Address"
            placeholder="Enter your address"
            leftIcon={MapPin}
            error={errors.address}
            maxLength={100}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="password"
            label="Password"
            placeholder="Create a password"
            type="password"
            leftIcon={Lock}
            error={errors.password}
            maxLength={20}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="confirmPassword"
            label="Confirm Password"
            placeholder="Confirm your password"
            type="password"
            leftIcon={Lock}
            error={errors.confirmPassword}
            maxLength={20}
            returnKeyType="done"
            className="text-base"
          />

          {/* Technical fields for technician */}
          <VStack className="mt-4 border-t border-gray-200 pt-4" space="md">
            <Text className="text-center text-lg font-bold">Technical Experience</Text>

            <InputGroup
              control={control}
              name="exp"
              label="Years of Experience"
              placeholder="Enter years of experience"
              keyboardType="numeric"
              leftIcon={Calendar}
              rightIcon={YearsText}
              error={errors.exp}
              maxLength={2}
              returnKeyType="done"
              className="text-base"
            />

            <CertificateUploader
              value={certificates}
              onChange={(urls) => setValue('certs', urls)}
              error={errors.certs}
            />
          </VStack>
        </VStack>

        <VStack space="md">
          <Button onPress={handleSubmit(onSubmit)} isDisabled={isSubmitting || isLoading}>
            {(isSubmitting || isLoading) && <ActivityIndicator size="small" color="#ffffff" />}
            <ButtonText>Create Account</ButtonText>
          </Button>

          <Pressable
            onPress={() => router.push('./login')}
            className="flex-row items-center justify-center gap-1">
            <Text className="text-gray-600">Already have an account?</Text>
            <Text className="font-semibold text-blue-800">Login</Text>
          </Pressable>
        </VStack>
      </VStack>
    </AuthLayout>
  );
}

const YearsText = () => <Text className="text-gray-700">years</Text>;
