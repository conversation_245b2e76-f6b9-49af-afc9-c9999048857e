import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { User, Mail, Lock, Phone, MapPin } from 'lucide-react-native';
import { useForm } from 'react-hook-form';
import { ActivityIndicator, Alert } from 'react-native';

import { AuthLayout } from '../_layout';

import { CustomerFormValues, createSchemas } from '@/schemas/auth-schema';
import { InputGroup } from '@/components/form';
import { Button, ButtonText, Text, Pressable, VStack } from '@/components/ui';
import { useAuth } from '@/core/contexts/AuthContext';

export default function CustomerRegisterScreen() {
  const router = useRouter();
  // Using direct English text instead of i18n
  const { customerSchema } = createSchemas((key) => key); // Dummy function since we're not using translations
  const { register, isLoading } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<CustomerFormValues>({
    resolver: zod<PERSON>esolver(customerSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      password: '',
      confirmPassword: '',
      role: 'customer',
    },
  });

  const onSubmit = async (data: CustomerFormValues) => {
    try {
      // Check if passwords match
      if (data.password !== data.confirmPassword) {
        Alert.alert('Validation Error', 'Passwords do not match');
        return;
      }

      // Register the user using useAuth context
      await register(data.name, data.email, data.password, data.phone, data.address, 'customer');

      // Note: The register function in AuthContext already handles:
      // - Showing error alerts
      // - Email verification notification (if needed)
      // - Redirecting to the login page
    } catch (error) {
      console.error('Registration failed', error);
      // Auth context already shows alerts, no need for duplicate alerts here
    }
  };

  return (
    <AuthLayout title="Register" subtitle="Create a customer account">
      <VStack space="xl">
        <VStack space="md">
          <InputGroup
            control={control}
            name="name"
            label="Full Name"
            placeholder="Enter your full name"
            leftIcon={User}
            error={errors.name}
            maxLength={50}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="email"
            label="Email Address"
            placeholder="Enter your email"
            helperText="We'll send you a confirmation email"
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon={Mail}
            error={errors.email}
            maxLength={50}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="phone"
            label="Phone Number"
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            leftIcon={Phone}
            error={errors.phone}
            maxLength={15}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="address"
            label="Address"
            placeholder="Enter your address"
            leftIcon={MapPin}
            error={errors.address}
            maxLength={100}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="password"
            label="Password"
            placeholder="Create a password"
            type="password"
            leftIcon={Lock}
            error={errors.password}
            maxLength={20}
            returnKeyType="next"
            className="text-base"
          />

          <InputGroup
            control={control}
            name="confirmPassword"
            label="Confirm Password"
            placeholder="Confirm your password"
            type="password"
            leftIcon={Lock}
            error={errors.confirmPassword}
            maxLength={20}
            returnKeyType="done"
            className="text-base"
          />
        </VStack>

        <VStack space="md">
          <Button onPress={handleSubmit(onSubmit)} isDisabled={isSubmitting || isLoading}>
            {(isSubmitting || isLoading) && <ActivityIndicator size="small" color="#ffffff" />}
            <ButtonText>Create Account</ButtonText>
          </Button>

          <Pressable
            onPress={() => router.push('./login')}
            className="flex-row items-center justify-center gap-1">
            <Text className="text-gray-600">Already have an account?</Text>
            <Text className="font-semibold text-blue-800">Login</Text>
          </Pressable>
        </VStack>
      </VStack>
    </AuthLayout>
  );
}
