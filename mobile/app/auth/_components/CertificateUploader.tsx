import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { AlertCircle, FileText, X, UploadCloud } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, Platform, ActivityIndicator, TouchableOpacity } from 'react-native';

import {
  Text,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlHelper,
  FormControlHelperText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  VStack,
  HStack,
} from '@/components/ui';
import { supabase } from '@/lib/supabase';

type Certificate = {
  name: string;
  uri: string;
};

type CertificateUploaderProps = {
  value: string[];
  onChange: (urls: string[]) => void;
  error?: { message?: string };
  label?: string;
  helperText?: string;
};

export default function CertificateUploader({
  value,
  onChange,
  error,
  label = 'Professional Certificates',
  helperText,
}: CertificateUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [, setSelectedFiles] = useState<Certificate[]>([]);
  const isInvalid = !!error;

  // Function to pick certificate files
  const pickCertificates = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true,
        multiple: true,
      });

      if (result.canceled === false && result.assets.length > 0) {
        const newFiles = result.assets.map((asset) => ({
          name: asset.name,
          uri: asset.uri,
        }));
        setSelectedFiles((prev) => [...prev, ...newFiles]);
        // Begin upload process
        uploadFiles(newFiles);
      }
    } catch {
      Alert.alert('Error', 'Something went wrong when picking documents.');
    }
  };

  // Function to upload files to Supabase Storage
  const uploadFiles = async (files: Certificate[]) => {
    try {
      setIsUploading(true);
      const uploadedUrls: string[] = [];
      const bucketName = 'certificates';

      for (const file of files) {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}_${Date.now()}.${fileExt}`;
        const filePath = `${fileName}`;

        // Read file as base64
        let fileData: string | Blob;
        if (Platform.OS === 'web') {
          // Handle web platform
          const response = await fetch(file.uri);
          fileData = await response.blob();
        } else {
          // Handle native platforms
          const base64 = await FileSystem.readAsStringAsync(file.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });
          fileData = base64;
        }

        // Upload to Supabase
        const { error } = await supabase.storage.from(bucketName).upload(filePath, fileData, {
          contentType: fileExt === 'pdf' ? 'application/pdf' : `image/${fileExt}`,
          upsert: true,
        });

        if (error) {
          Alert.alert('Upload Error', `Failed to upload ${file.name}`);
          console.error('Upload error:', error);
        } else {
          // Get public URL
          const { data: publicUrlData } = supabase.storage.from(bucketName).getPublicUrl(filePath);

          if (publicUrlData?.publicUrl) {
            uploadedUrls.push(publicUrlData.publicUrl);
          }
        }
      }

      // Update the form with URLs
      onChange([...value, ...uploadedUrls]);
      setIsUploading(false);
    } catch (error) {
      console.error('Error uploading files:', error);
      Alert.alert('Upload Error', 'Failed to upload files to storage.');
      setIsUploading(false);
    }
  };

  // Remove a certificate
  const removeCertificate = async (index: number) => {
    try {
      const newUrls = [...value];
      const removedUrl = newUrls.splice(index, 1)[0];

      // If it's a Supabase URL, delete from storage
      if (removedUrl && removedUrl.includes('supabase')) {
        // Extract the file path from the URL
        const urlParts = removedUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];

        // Delete from Supabase storage
        await supabase.storage.from('certificates').remove([fileName]);
      }

      onChange(newUrls);
    } catch (error) {
      console.error('Error removing certificate:', error);
      Alert.alert('Error', 'Failed to remove certificate.');
    }
  };

  return (
    <FormControl isInvalid={isInvalid}>
      <FormControlLabel>
        <FormControlLabelText>{label}</FormControlLabelText>
      </FormControlLabel>

      <Input
        onTouchStart={pickCertificates}
        className={isUploading ? 'opacity-60' : ''}
        pointerEvents={isUploading ? 'none' : 'auto'}>
        <InputSlot className="pl-3">
          <InputIcon as={FileText} />
        </InputSlot>

        <InputField
          placeholder="Upload certificates"
          editable={false}
          value={value && value.length > 0 ? `${value.length} certificate(s)` : ''}
        />

        <InputSlot className="pr-3">
          <InputIcon as={isUploading ? ActivityIndicator : UploadCloud} color="gray" />
        </InputSlot>
      </Input>

      {/* Display uploaded certificates */}
      {value && value.length > 0 && (
        <VStack space="xs" className="mt-2">
          {value.map((url, index) => (
            <HStack
              key={index}
              className="mt-1 flex-row items-center justify-between rounded border border-gray-200 bg-gray-50 p-2">
              <Text className="flex-1 text-xs text-gray-700" numberOfLines={1}>
                Certificate #{index + 1}
              </Text>
              <TouchableOpacity onPress={() => removeCertificate(index)} className="p-1">
                <X size={16} color="#6B7280" />
              </TouchableOpacity>
            </HStack>
          ))}
        </VStack>
      )}

      {helperText && (
        <FormControlHelper>
          <FormControlHelperText>{helperText}</FormControlHelperText>
        </FormControlHelper>
      )}

      {isInvalid && (
        <FormControlError>
          <FormControlErrorIcon as={AlertCircle} size="sm" />
          <FormControlErrorText className="text-sm">{error?.message}</FormControlErrorText>
        </FormControlError>
      )}
    </FormControl>
  );
}
