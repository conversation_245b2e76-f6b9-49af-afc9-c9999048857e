import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { Mail, Lock } from 'lucide-react-native';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert } from 'react-native';

import { AuthLayout } from './_layout';

import { LoginFormValues, createSchemas } from '@/schemas/auth-schema';
import { InputGroup } from '@/components/form';
import { Button, ButtonText, Text, Pressable, VStack, Box, Center } from '@/components/ui';
import { useAppType } from '@/core/contexts/AppTypeContext';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

export default function LoginScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { loginSchema } = createSchemas(t);
  const { isLoading } = useAuth();
  const { appType } = useAppType();
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      // Use Supabase directly for authentication, similar to web-admin implementation
      const { error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        Alert.alert('Login Error', error.message || 'Invalid credentials. Please try again.');
        return;
      }

      // Success - the auth state listener in AuthContext will handle the session
      router.replace('/main');
    } catch (error) {
      console.error('Login failed', error);
      Alert.alert('Login Error', 'An unexpected error occurred. Please try again.');
    }
  };

  return (
    <AuthLayout
      title={t('login.title')}
      subtitle={
        appType === 'technician'
          ? 'Login to your technician account'
          : 'Login to your customer account'
      }>
      <VStack space="md">
        <InputGroup
          control={control}
          name="email"
          label={t('login.email')}
          placeholder={t('login.email')}
          keyboardType="email-address"
          autoCapitalize="none"
          leftIcon={Mail}
          error={errors.email}
          maxLength={50}
          returnKeyType="next"
          className="text-base"
        />

        <InputGroup
          control={control}
          name="password"
          label={t('login.password')}
          placeholder={t('login.password')}
          type="password"
          leftIcon={Lock}
          error={errors.password}
          maxLength={20}
          returnKeyType="done"
          className="text-base"
        />

        {/* <Pressable onPress={() => router.push('./forgot-password')} className="self-end">
          <Text className="font-medium text-blue-800">{t('login.forgotPassword')}</Text>
        </Pressable> */}

        <Button
          onPress={handleSubmit(onSubmit)}
          className="mt-4 gap-2"
          isDisabled={isSubmitting || isLoading}>
          {(isSubmitting || isLoading) && <ActivityIndicator size="small" color="#ffffff" />}
          <ButtonText>{t('login.loginButton')}</ButtonText>
        </Button>
      </VStack>

      <Center className="mt-auto pt-8">
        <Box className="flex-row flex-wrap items-center justify-center">
          <Text className="text-gray-600">{t('login.noAccount')}</Text>
          <Pressable onPress={() => router.push('./register')} className="ml-1">
            <Text className="font-semibold text-blue-800">{t('login.registerNow')}</Text>
          </Pressable>
        </Box>
      </Center>
    </AuthLayout>
  );
}
