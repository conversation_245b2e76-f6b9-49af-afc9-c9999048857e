import { Stack } from 'expo-router';
import React from 'react';
import { ScrollView, View, StatusBar, KeyboardAvoidingView, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Heading, HStack, Text, VStack } from '@/components/ui';
import LogoSvg from '~/assets/svg/Logo';
import { useAppType } from '~/core/contexts/AppTypeContext';

export interface AuthLayoutProps {
  title: string;
  subtitle: string;
  children: React.ReactNode;
}

export function AuthLayout({ title, subtitle, children }: AuthLayoutProps) {
  const insets = useSafeAreaInsets();
  const { isCustomer } = useAppType();

  // Different colors for customer vs technician
  const headerBgClass = isCustomer ? 'bg-gray-900' : 'bg-blue-900';
  const statusBarColor = isCustomer ? '#111827' : '#1e3a8a';

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-white"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={0}>
      <StatusBar barStyle="light-content" backgroundColor={statusBarColor} />
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        <Box className={`relative flex-1 ${headerBgClass}`}>
          <Box
            style={{ paddingTop: insets.top }}
            className={`border-b-8 px-6 ${isCustomer ? 'border-gray-600' : 'border-blue-600'}`}>
            <HStack className="mb-6 mt-6 items-center gap-4">
              <LogoSvg width={56} height={42} />
              <VStack className="border-l border-white pl-3">
                <Heading size="2xl" className="text-start font-bold uppercase text-white">
                  {title}
                </Heading>
                <Text className="max-w-xs text-start text-gray-400">{subtitle}</Text>
              </VStack>
            </HStack>
          </Box>

          <View className="flex-1 bg-white px-6 py-8" style={{ paddingBottom: insets.bottom + 20 }}>
            {children}
          </View>
        </Box>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

export default function AuthScreenLayout() {
  return (
    <Stack screenOptions={{ headerShown: false, animation: 'fade' }}>
      <Stack.Screen name="login" />
      <Stack.Screen name="register" />
    </Stack>
  );
}
