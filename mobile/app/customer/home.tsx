import { Stack, useRouter } from 'expo-router';
import { Text, TouchableOpacity, ScrollView } from 'react-native';
import { useState, useEffect } from 'react';

import ServiceList from '@/components/main/ServiceList';
import { MainLayout } from '@/components/layout/MainLayout';
import { Box, HStack, VStack } from '@/components/ui';
import { CurrentBookingItem } from '@/components/order/CurrentBookingItem';
import { RecentBookingItem } from '@/components/order/RecentBookingItem';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'rejected';
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  is_paid: boolean;
  technician_rating?: number;
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
}

export default function CustomerHome() {
  const router = useRouter();
  const { user } = useAuth();
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchRecentOrders = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          technician:technician_id(id, name, email, phone),
          service:service_id(id, name, price)
        `
        )
        .eq('customer_id', user.id)
        .order('created_at', { ascending: false })
        .limit(4);

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      if (data && data.length > 0) {
        const current = data.find(
          (order) => order.status === 'in_progress' || order.status === 'confirmed'
        );
        setCurrentOrder(current || null);

        const recent = current
          ? data.filter((order) => order.id !== current.id).slice(0, 3)
          : data.slice(0, 3);
        setRecentOrders(recent);
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecentOrders();
  }, [user]);

  return (
    <MainLayout>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView className="px-4 py-4">
        {/* Welcome Section */}
        <Box className="mb-6">
          <Text className="text-2xl font-bold text-gray-900">
            Welcome back{user?.name ? `, ${user.name}` : ''}!
          </Text>
          <Text className="text-gray-600">Find the best auto services for your vehicle</Text>
        </Box>

        {/* Current Booking */}
        {currentOrder && (
          <Box className="mb-6">
            <HStack className="mb-3 items-center justify-between">
              <Text className="text-lg font-bold text-gray-900">Current Booking</Text>
            </HStack>
            <CurrentBookingItem
              booking={{
                service: currentOrder.service?.name || 'Service',
                garage: currentOrder.technician?.name || 'Technician',
                status:
                  currentOrder.status.charAt(0).toUpperCase() +
                  currentOrder.status.slice(1).replace('_', ' '),
                date: new Date(currentOrder.scheduled_at).toLocaleDateString(),
                time: new Date(currentOrder.scheduled_at).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                }),
                estimatedTime: '2 hours',
              }}
              onViewDetails={() => router.push(`/customer/order/${currentOrder.id}`)}
              onGetDirections={() => console.log('Get directions')}
            />
          </Box>
        )}

        {/* Recent Bookings */}
        <Box className="mb-6">
          <HStack className="mb-3 items-center justify-between">
            <Text className="text-lg font-bold text-gray-900">Recent Bookings</Text>
            <TouchableOpacity
              className="rounded-full bg-gray-900 px-3 py-1"
              onPress={() => {
                router.push('/customer/order-history');
              }}>
              <Text className="text-xs font-medium text-yellow-400">View All</Text>
            </TouchableOpacity>
          </HStack>

          {loading ? (
            <Box className="py-8">
              <Text className="text-center text-gray-500">Loading orders...</Text>
            </Box>
          ) : recentOrders.length > 0 ? (
            <VStack space="sm">
              {recentOrders.map((order) => (
                <RecentBookingItem
                  key={order.id}
                  booking={{
                    service: order.service?.name || 'Service',
                    garage: order.technician?.name || 'Technician',
                    date: new Date(order.scheduled_at).toLocaleDateString(),
                    price: `$${order.amount || 0}`,
                  }}
                  onPress={() => router.push(`/customer/order/${order.id}`)}
                />
              ))}
            </VStack>
          ) : (
            <Box className="rounded-xl bg-gray-50 p-4">
              <Text className="text-center text-gray-500">No recent bookings</Text>
            </Box>
          )}
        </Box>

        {/* Services Section */}
        <ServiceList />
      </ScrollView>
    </MainLayout>
  );
}
