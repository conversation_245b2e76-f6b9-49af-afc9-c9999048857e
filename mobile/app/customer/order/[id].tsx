import { formatDistanceToNow } from 'date-fns';
import { useLocalSearchParams, useRouter } from 'expo-router';
import {
  MapPin,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  ChevronLeft,
  DollarSign,
  Tag,
  MessageSquare,
  Car,
  Navigation,
  Map,
} from 'lucide-react-native';
import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  StatusBar,
  RefreshControl,
  Platform,
  SafeAreaView,
  Linking,
} from 'react-native';

import { FeedbackForm } from '@/components/customer/FeedbackForm';
import { FeedbackView } from '@/components/customer/FeedbackView';
import { MainLayout } from '@/components/layout/MainLayout';
import { LocationHistoryMap } from '@/components/map/LocationHistoryMap';
import { useNotification } from '@/services/toast-service';
import StripePaymentSheet from '@/components/payment/StripePaymentSheet';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Get screen dimensions for responsive layouts (unused)
// const { width: _SCREEN_WIDTH } = Dimensions.get('window');
const IS_IOS = Platform.OS === 'ios';

// Theme and UI constants
const THEME = {
  radius: {
    sm: 4,
    md: 6,
    lg: 8,
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
  },
  statusBar: {
    light: {
      barStyle: 'light-content' as const,
      backgroundColor: '#0284c7',
    },
    dark: {
      barStyle: 'dark-content' as const,
      backgroundColor: '#ffffff',
    },
  },
};

// Order interface matching the database schema
interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: string;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
  feedback_id?: string;
}

// Status badge component for consistent styling
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          icon: <CheckCircle size={12} color="#15803d" />,
        };
      case 'in_progress':
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-800',
          icon: <Clock size={12} color="#1e40af" />,
        };
      case 'confirmed':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          icon: <AlertCircle size={12} color="#854d0e" />,
        };
      default:
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-800',
          icon: <Tag size={12} color="#4b5563" />,
        };
    }
  };

  const config = getStatusConfig();

  return (
    <View
      className={`flex-row items-center rounded-md px-2 py-1 ${config.bg}`}
      accessibilityLabel={`Status: ${status.replace('_', ' ')}`}>
      {config.icon}
      <Text className={`ml-1 font-medium ${config.text} text-xs`}>
        {status.replace('_', ' ').toUpperCase()}
      </Text>
    </View>
  );
};

// Section header component for consistent styling
const SectionHeader = ({
  icon,
  title,
  rightElement,
}: {
  icon: React.ReactNode;
  title: string;
  rightElement?: React.ReactNode;
}) => (
  <View className="mb-2 flex-row items-center justify-between">
    <View className="flex-row items-center">
      <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-sky-50">{icon}</View>
      <Text className="text-sm font-semibold text-gray-800">{title}</Text>
    </View>
    {rightElement}
  </View>
);

// Info card component for consistent card styling (unused)
// const InfoCard = ({
//   children,
//   isFirst = false,
//   isLast = false,
// }: {
//   children: React.ReactNode;
//   isFirst?: boolean;
//   isLast?: boolean;
// }) => (
//   <View
//     className={`border-l border-r border-gray-200 bg-white p-3 ${
//       isFirst ? 'rounded-t-md border-t' : ''
//     } ${isLast ? 'mb-3 rounded-b-md border-b' : ''}`}>
//     {children}
//   </View>
// );

// Info row component for consistent styling of information rows (unused)
// const InfoRow = ({
//   icon,
//   label,
//   value,
//   isHighlighted = false,
// }: {
//   icon: React.ReactNode;
//   label: string;
//   value: string;
//   isHighlighted?: boolean;
// }) => (
//   <View className="mb-2 flex-row items-center">
//     <View
//       className={`mr-2 h-6 w-6 items-center justify-center rounded-md ${
//         isHighlighted ? 'bg-blue-50' : 'bg-gray-50'
//       }`}>
//       {icon}
//     </View>
//     <View className="flex-1">
//       <Text className="text-xs text-gray-500">{label}</Text>
//       <Text
//         className={`${isHighlighted ? 'text-blue-800' : 'text-gray-800'} ${
//           value.length > 40 ? 'text-xs' : 'text-sm'
//         } ${isHighlighted ? 'font-medium' : ''}`}>
//         {value}
//       </Text>
//     </View>
//   </View>
// );

export default function CustomerOrderDetailsScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuth();
  const router = useRouter();
  const { showToast } = useNotification();
  // const {} = useNotifications(); // unused

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [, setTechnicianLocation] = useState(null);
  const [hasLocationData, setHasLocationData] = useState(false);
  const [, setHasFeedback] = useState(false);
  const [feedbackId, setFeedbackId] = useState<string | null>(null);
  const [isEditingFeedback, setIsEditingFeedback] = useState(false);
  const [viewingFeedback, setViewingFeedback] = useState(false);
  const [statusBarStyle, setStatusBarStyle] = useState<'light-content' | 'dark-content'>(
    IS_IOS ? 'dark-content' : 'light-content'
  );

  // Fetch order details
  const fetchOrder = async (showLoadingState = true) => {
    if (!user || !id) return;

    try {
      if (showLoadingState) setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          technician:technician_id(id, name, email, phone),
          service:service_id(id, name, price)
        `
        )
        .eq('id', id)
        .eq('customer_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching order:', error);
        showToast('Error loading order details', 'error');
        return;
      }

      setOrder(data as Order);
      setHasFeedback(!!data.feedback_id);
      setFeedbackId(data.feedback_id || null);

      if (data.feedback_id && !isEditingFeedback && !viewingFeedback) {
        setViewingFeedback(true); // Default to viewing if feedback exists
      }

      // Check if there's technician location data
      if (data.technician_id) {
        await checkTechnicianLocationData(data.technician_id, data.id);
      }
    } catch (error) {
      console.error('Failed to fetch order:', error);
      showToast('Error loading order details', 'error');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Check if there's any technician location data for this order
  const checkTechnicianLocationData = async (technicianId: string, orderId: string) => {
    try {
      const { data } = await supabase
        .from('technician_locations')
        .select('locations, is_active')
        .eq('technician_id', technicianId)
        .eq('order_id', orderId)
        .maybeSingle();

      if (data && data.locations && data.locations.length > 0) {
        setHasLocationData(true);
        // If actively tracking, get the most recent location
        if (data.is_active && data.locations.length > 0) {
          const latestLocation = data.locations[data.locations.length - 1];
          setTechnicianLocation(latestLocation);
        }
      }
    } catch (error) {
      console.error('Error checking technician location data:', error);
    }
  };

  // Pull to refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchOrder(false);
  }, [id, user]);

  // Toggle status bar style
  const toggleStatusBarStyle = useCallback(() => {
    setStatusBarStyle((prev) => (prev === 'dark-content' ? 'light-content' : 'dark-content'));
  }, []);

  // Set up real-time subscription to technician location
  useEffect(() => {
    if (!order?.technician_id || !order?.id) return;

    // Subscribe to location updates
    const channel = supabase
      .channel(`technician-location-${order.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'technician_locations',
          filter: `technician_id=eq.${order.technician_id}`,
        },
        (payload) => {
          if (payload.new && payload.new.locations && payload.new.locations.length > 0) {
            setHasLocationData(true);
            const latestLocation = payload.new.locations[payload.new.locations.length - 1];
            setTechnicianLocation(latestLocation);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [order?.technician_id, order?.id]);

  // Initial data load
  useEffect(() => {
    fetchOrder();
  }, [id, user]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      day: 'numeric',
      month: 'short',
      hour: 'numeric',
      minute: 'numeric',
    });
  };

  // Open directions in maps app
  const openDirections = (location: string | undefined) => {
    if (!location) return;

    const [lat, lng] = location.split(',').map((coord) => coord.trim());
    const url = Platform.select({
      ios: `maps://app?saddr=Current+Location&daddr=${lat},${lng}`,
      android: `google.navigation:q=${lat},${lng}`,
      default: `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`,
    });

    if (!url) return;

    Linking.canOpenURL(url).then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        // Fallback to web Google Maps
        Linking.openURL(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`);
      }
    });
  };

  // Call technician if phone number is available
  const callTechnician = (phone: string | undefined) => {
    if (!phone) return;

    const phoneUrl = `tel:${phone}`;
    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(phoneUrl);
        } else {
          showToast('Phone calls are not supported on this device', 'error');
        }
      })
      .catch((err) => console.error('Error opening phone app:', err));
  };

  // Handle feedback submission or update
  const handleFeedbackSubmitted = useCallback(() => {
    setHasFeedback(true);
    setIsEditingFeedback(false);
    setViewingFeedback(true);
    fetchOrder(false);
  }, []);

  // Toggle edit feedback mode
  const toggleEditFeedback = useCallback(() => {
    setIsEditingFeedback(true);
    setViewingFeedback(false);
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={
            statusBarStyle === 'dark-content'
              ? THEME.statusBar.dark.backgroundColor
              : THEME.statusBar.light.backgroundColor
          }
        />
        <View className="flex-1 items-center justify-center bg-gray-50">
          <ActivityIndicator size="large" color="#0284c7" />
          <Text className="mt-2 text-sm text-gray-600">Loading order details...</Text>
        </View>
      </MainLayout>
    );
  }

  if (!order) {
    return (
      <MainLayout>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={
            statusBarStyle === 'dark-content'
              ? THEME.statusBar.dark.backgroundColor
              : THEME.statusBar.light.backgroundColor
          }
        />
        <View className="flex-1 items-center justify-center bg-gray-50 p-4">
          <AlertCircle size={40} color="#ef4444" />
          <Text className="mt-3 text-lg font-semibold text-gray-800">Order Not Found</Text>
          <Text className="mt-1 text-center text-sm text-gray-600">
            This order does not exist or you don't have permission to view it.
          </Text>
          <TouchableOpacity
            className="mt-4 rounded-md bg-blue-500 px-3 py-2"
            onPress={() => router.back()}>
            <Text className="text-sm font-medium text-white">Go Back</Text>
          </TouchableOpacity>
        </View>
      </MainLayout>
    );
  }

  // Format vehicle information
  const vehicleInfo = order.order_data?.vehicle_info;
  const vehicleBrand = vehicleInfo?.brand || '';
  const vehicleModel = vehicleInfo?.model || '';
  const vehicleYear = vehicleInfo?.year || '';
  const licensePlate = vehicleInfo?.license_plate || '';

  const vehicleText =
    [vehicleBrand, vehicleModel, vehicleYear].filter(Boolean).join(' ') || 'No vehicle info';
  const fullVehicleText =
    vehicleText === 'No vehicle info'
      ? vehicleText
      : `${vehicleText}${licensePlate ? ` (${licensePlate})` : ''}`;

  // Get status message for customer
  const getServiceStatusMessage = () => {
    switch (order.status) {
      case 'pending':
        return 'Waiting for confirmation';
      case 'confirmed':
        return 'Service confirmed, waiting to start';
      case 'in_progress':
        return 'Technician is working on your service';
      case 'completed':
        return 'Service has been completed';
      case 'cancelled':
        return 'Service has been cancelled';
      default:
        return '';
    }
  };

  return (
    <MainLayout scrollable={false}>
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={
          statusBarStyle === 'dark-content'
            ? THEME.statusBar.dark.backgroundColor
            : THEME.statusBar.light.backgroundColor
        }
      />

      <SafeAreaView className="flex-1 bg-white">
        {/* Header bar with back button and status bar toggle */}
        <View className="flex-row items-center justify-between border-b border-gray-200 bg-white px-3 py-2">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="-ml-1.5 p-1.5"
              accessibilityRole="button"
              accessibilityLabel="Go back">
              <ChevronLeft size={20} color="#0f172a" />
            </TouchableOpacity>
            <Text className="ml-1.5 text-base font-medium text-gray-900">#{order?.code}</Text>
          </View>
          <TouchableOpacity
            onPress={toggleStatusBarStyle}
            className="rounded-full bg-gray-100 p-1.5"
            accessibilityLabel="Toggle status bar style">
            <Clock size={14} color="#4b5563" />
          </TouchableOpacity>
        </View>

        <ScrollView
          className="flex-1 bg-gray-50"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0284c7']}
              tintColor="#0284c7"
            />
          }>
          {/* Service and Status Section - Compact header with critical information */}
          <View className="mb-2 border-b border-gray-200 bg-white p-3">
            <View className="mb-1 flex-row items-center justify-between">
              <StatusBadge status={order.status} />
              <Text className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(order.created_at), { addSuffix: true })}
              </Text>
            </View>
            <Text className="text-lg font-bold text-gray-900">{order.service?.name}</Text>

            <View className="mt-1.5 flex-row items-center justify-between">
              {order.amount > 0 && (
                <View className="flex-row items-center">
                  <DollarSign size={14} color="#6b7280" />
                  <Text className="ml-0.5 text-sm text-gray-700">
                    {order.amount.toLocaleString()}
                  </Text>
                </View>
              )}
              <Text className="text-xs text-gray-500">{getServiceStatusMessage()}</Text>
            </View>
          </View>

          {/* Status Alert Section (if service is in progress) */}
          {order.status === 'in_progress' && hasLocationData && (
            <View className="mb-1 px-3">
              <View className="mb-2 rounded-md border border-blue-100 bg-blue-50 p-2.5">
                <View className="flex-row items-center">
                  <MapPin size={14} color="#3b82f6" />
                  <Text className="ml-1.5 flex-1 text-sm font-medium text-blue-700">
                    Technician is on the way
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Main Information Grid - Organized in logical sections */}
          <View className="px-3">
            {/* Technician section (if assigned) */}
            {order.technician && (
              <View className="mb-2 rounded-md border border-gray-200 bg-white">
                <View className="p-2.5">
                  <SectionHeader icon={<User size={14} color="#0369a1" />} title="Technician" />

                  <View className="flex-row">
                    <View className="mr-2 flex-1">
                      <Text className="mb-0.5 text-xs text-gray-500">Name</Text>
                      <Text className="text-xs text-gray-800">
                        {order.technician?.name || 'Not assigned yet'}
                      </Text>
                    </View>

                    {order.technician?.phone && (
                      <View className="flex-1">
                        <Text className="mb-0.5 text-xs text-gray-500">Phone</Text>
                        <TouchableOpacity onPress={() => callTechnician(order.technician?.phone)}>
                          <Text className="text-xs font-medium text-blue-800">
                            {order.technician.phone}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            )}

            {/* Vehicle section */}
            <View className="mb-2 rounded-md border border-gray-200 bg-white">
              <View className="border-b border-gray-100 p-2.5">
                <SectionHeader icon={<Car size={14} color="#0369a1" />} title="Vehicle" />

                <View className="flex-row">
                  <View className="mr-2 flex-1">
                    <Text className="mb-0.5 text-xs text-gray-500">Vehicle</Text>
                    <Text className="text-xs text-gray-800">{fullVehicleText}</Text>
                  </View>

                  {licensePlate && (
                    <View className="flex-1">
                      <Text className="mb-0.5 text-xs text-gray-500">License Plate</Text>
                      <Text className="text-xs text-gray-800">{licensePlate}</Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Location section */}
              <View className="p-2.5">
                <Text className="mb-0.5 text-xs text-gray-500">Service Location</Text>
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-start">
                    <Text className="flex-1 text-xs text-gray-800">
                      {order.order_data?.location || 'No location provided'}
                    </Text>
                  </View>
                  {order.order_data?.location && (
                    <TouchableOpacity
                      onPress={() => openDirections(order.order_data?.location)}
                      className="ml-2 h-8 w-8 flex-row items-center rounded-md bg-blue-50 px-2 py-1">
                      <Navigation size={12} color="#2563eb" className="mr-2 h-8 w-8" />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>

            {/* Order details and schedule section */}
            <View className="mb-2 rounded-md border border-gray-200 bg-white">
              <View className="border-b border-gray-100 p-2.5">
                <SectionHeader icon={<Clock size={14} color="#0369a1" />} title="Schedule" />

                <View className="flex-row justify-between">
                  <View className="mr-2 flex-1">
                    <Text className="mb-0.5 text-xs text-gray-500">Scheduled</Text>
                    <Text className="text-xs text-gray-800">{formatDate(order.scheduled_at)}</Text>
                  </View>

                  {order.completed_at && (
                    <View className="flex-1">
                      <Text className="mb-0.5 text-xs text-gray-500">Completed</Text>
                      <Text className="text-xs text-green-800">
                        {formatDate(order.completed_at)}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>

            {/* Notes & Requests Section - Only shown if data exists */}
            {(order.order_data?.special_requests || order.note) && (
              <View className="mb-3 rounded-md border border-gray-200 bg-white">
                <View className="p-2.5">
                  <SectionHeader
                    icon={<MessageSquare size={14} color="#0369a1" />}
                    title="Notes & Requests"
                  />

                  {order.order_data?.special_requests && (
                    <View className="mb-2">
                      <Text className="mb-0.5 text-xs text-gray-500">Special Requests</Text>
                      <Text className="text-xs text-gray-800">
                        {order.order_data.special_requests}
                      </Text>
                    </View>
                  )}

                  {order.note && (
                    <View>
                      <Text className="mb-0.5 text-xs text-gray-500">Notes</Text>
                      <Text className="text-xs text-gray-800">{order.note}</Text>
                    </View>
                  )}
                </View>
              </View>
            )}

            {/* Location History Map - Show when there's location data from technician */}
            {order.technician_id && order.order_data?.location && hasLocationData && (
              <View className="mb-2 rounded-md border border-gray-200 bg-white">
                <View className="border-b border-gray-100 p-2.5">
                  <SectionHeader
                    icon={<Map size={14} color="#0369a1" />}
                    title="Technician Location"
                    rightElement={
                      order.status === 'in_progress' ? (
                        <Text className="text-xs text-blue-500">Live tracking</Text>
                      ) : null
                    }
                  />

                  {/* Map */}
                  <LocationHistoryMap
                    orderId={order.id}
                    technicianId={order.technician_id}
                    initialLocation={{
                      latitude: parseFloat(order.order_data.location.split(',')[0]),
                      longitude: parseFloat(order.order_data.location.split(',')[1]),
                    }}
                  />
                </View>
              </View>
            )}

            {/* Feedback Section - Only show if order is completed and has technician */}
            {order?.status === 'completed' && order?.technician_id && (
              <View className="mb-3">
                {isEditingFeedback ? (
                  <FeedbackForm
                    orderId={order.id}
                    technicianId={order.technician_id}
                    customerId={user?.id || ''}
                    feedbackId={feedbackId || undefined}
                    isEditing={isEditingFeedback}
                    onFeedbackSubmitted={handleFeedbackSubmitted}
                  />
                ) : viewingFeedback && feedbackId ? (
                  <FeedbackView feedbackId={feedbackId} onEditPress={toggleEditFeedback} />
                ) : (
                  <FeedbackForm
                    orderId={order.id}
                    technicianId={order.technician_id}
                    customerId={user?.id || ''}
                    onFeedbackSubmitted={handleFeedbackSubmitted}
                  />
                )}
              </View>
            )}

            {/* Payment Section - Only show if order is completed */}
            {order?.status === 'completed' && (
              <View className="mb-3">
                <StripePaymentSheet
                  orderId={order.id}
                  amount={order.amount}
                  onPaymentSuccess={() => {
                    showToast('Payment successful!', 'success');
                    fetchOrder(false);
                  }}
                  onPaymentError={(error) => {
                    showToast(`Payment failed: ${error}`, 'error');
                  }}
                />
              </View>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </MainLayout>
  );
}
