import React, { useState } from 'react';
import { Platform, View } from 'react-native';

import { Message, ChatHeader, ChatInput, ChatMessageList } from '@/components/chat';
import {
  formatMessageTime,
  createUserMessage,
  createServiceMessage,
} from '@/components/chat/utils';
import { KeyboardAvoidingView } from '@/components/ui/keyboard-avoiding-view';
import { MainLayout } from '~/components/layout/MainLayout';

export default function ChatScreen() {
  // Track which chat mode is active
  const [chatMode] = useState<'normal' | 'ai'>('normal');
  const [message, setMessage] = useState('');

  // Separate message histories for each mode
  const [normalMessages, setNormalMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! How can I help you?',
      sender: 'service',
      timestamp: new Date(Date.now() - 3600000),
    },
    {
      id: '2',
      text: 'I need more information about your services',
      sender: 'user',
      timestamp: new Date(Date.now() - 3500000),
    },
    {
      id: '3',
      text: 'We offer various services including consultation, technical support, and answering inquiries. Which service are you interested in?',
      sender: 'service',
      timestamp: new Date(Date.now() - 3400000),
    },
  ]);

  const [aiMessages, setAiMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I am an AI assistant, how can I help you?',
      sender: 'service',
      timestamp: new Date(Date.now() - 3600000),
    },
  ]);

  // Use the active messages based on mode
  const messages = chatMode === 'normal' ? normalMessages : aiMessages;
  const setMessages = chatMode === 'normal' ? setNormalMessages : setAiMessages;

  const handleSendMessage = () => {
    if (message.trim() === '') return;

    // Add user message
    const userMessage = createUserMessage(message);
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setMessage('');

    // Simulate service response
    setTimeout(() => {
      let serviceResponse = 'Thank you for contacting us. We will respond as soon as possible.';

      // Provide different responses based on mode
      if (chatMode === 'ai') {
        serviceResponse = 'Message received. This is a response from the AI assistant.';
      }

      const serviceMessage = createServiceMessage(serviceResponse);
      setMessages((prevMessages) => [...prevMessages, serviceMessage]);
    }, 1500);
  };

  const handleClearHistory = () => {
    if (chatMode === 'normal') {
      setNormalMessages([]);
    } else {
      setAiMessages([]);
    }
  };

  return (
    <MainLayout>
      <ChatHeader
        title={chatMode === 'normal' ? 'Service Center' : 'AI Assistant'}
        subtitle={chatMode === 'normal' ? 'Response time: 5 minutes' : 'Powered by Vinast Lotus'}
        onClearHistory={handleClearHistory}
        mode={chatMode}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        className="flex-1">
        <View className={`flex-1 ${chatMode === 'ai' ? 'bg-slate-900' : 'bg-white'}`}>
          <ChatMessageList messages={messages} formatTime={formatMessageTime} mode={chatMode} />
        </View>

        <ChatInput initialMessage="" onSendMessage={handleSendMessage} mode={chatMode} />
      </KeyboardAvoidingView>
    </MainLayout>
  );
}
