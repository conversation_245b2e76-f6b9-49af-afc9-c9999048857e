import React, { useState, useEffect } from 'react';
import { View, FlatList, ActivityIndicator, RefreshControl, Text } from 'react-native';

import { MainLayout } from '@/components/layout/MainLayout';
import { OrderItem } from '@/components/order/OrderItem';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Order status type
export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'rejected';

// Order interface matching the database schema
interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: OrderStatus;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  is_paid: boolean;
  technician_rating?: number;
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
}

export default function OrderHistoryScreen() {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch orders for the logged-in customer
  const fetchOrders = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          technician:technician_id(id, name, email, phone),
          service:service_id(id, name, price)
        `
        )
        .eq('customer_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders:', error);
        return;
      }

      setOrders(data as Order[]);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load orders on component mount
  useEffect(() => {
    fetchOrders();
  }, [user]);

  // Pull-to-refresh handler
  const onRefresh = () => {
    setRefreshing(true);
    fetchOrders();
  };

  // Render empty state
  const renderEmptyState = () => {
    if (loading) {
      return (
        <View className="flex-1 items-center justify-center p-4">
          <ActivityIndicator size="large" color="#0284c7" />
          <Text className="mt-4 text-gray-600">Loading orders...</Text>
        </View>
      );
    }

    return (
      <View className="flex-1 items-center justify-center p-4">
        <Text className="text-xl font-semibold text-gray-800">No Orders Found</Text>
        <Text className="mt-2 text-center text-gray-600">
          You don't have any orders yet. Your orders will appear here once you place them.
        </Text>
      </View>
    );
  };

  // Render an order item
  const renderOrderItem = ({ item }: { item: Order }) => {
    return <OrderItem order={item} />;
  };

  return (
    <MainLayout scrollable={false}>
      <View className="flex-1 p-6">
        <Text className="mb-6 text-2xl font-bold">My Bookings</Text>

        <FlatList
          data={orders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
          ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#fbbf24']} />
          }
        />
      </View>
    </MainLayout>
  );
}
