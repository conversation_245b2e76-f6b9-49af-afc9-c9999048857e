import { useState } from 'react';
import { View } from 'react-native';

import { TabButton, TabType, UserTab, LocationsTab, SecurityTab } from '@/components/main';
import { MainLayout } from '@/components/layout/MainLayout';

export default function AccountScreen() {
  const [activeTab, setActiveTab] = useState<TabType>('USER');

  return (
    <MainLayout>
      <View className="px-6">
        {/* Tabs */}
        <View className="flex-row">
          <TabButton
            title="USER"
            isActive={activeTab === 'USER'}
            onPress={() => setActiveTab('USER')}
          />
          <TabButton
            title="LOCATIONS"
            isActive={activeTab === 'LOCATIONS'}
            onPress={() => setActiveTab('LOCATIONS')}
          />
          <TabButton
            title="SECURITY"
            isActive={activeTab === 'SECURITY'}
            onPress={() => setActiveTab('SECURITY')}
          />
        </View>

        {/* Content based on active tab */}
        <View className="py-4">
          {activeTab === 'USER' && <UserTab />}
          {activeTab === 'LOCATIONS' && <LocationsTab />}
          {activeTab === 'SECURITY' && <SecurityTab />}
        </View>
      </View>
    </MainLayout>
  );
}
