import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Platform, View, Alert, FlatList } from 'react-native';
import uuid from 'react-native-uuid';

import { ChatHeader, ChatInput } from '@/components/chat';
import { formatMessageTime } from '@/components/chat/utils';
import { ChatMessage } from '@/components/chat/ChatMessage';
import { Message as ChatMessageType } from '@/components/chat/types';
import { Box } from '@/components/ui/box';
import { KeyboardAvoidingView } from '@/components/ui/keyboard-avoiding-view';
import { Spinner } from '@/components/ui/spinner';
import { Text } from '@/components/ui/text';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { MainLayout } from '~/components/layout/MainLayout';

// Interface for messages
interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  read_at?: string;
  // For optimistic updates
  temp_id?: string;
  isPending?: boolean;
}

// Admin configuration
const LIMIT = 50; // Load more messages initially

export default function ChatScreen() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [inputMessage, setInputMessage] = useState('');

  // Refs for optimization
  const flatListRef = useRef<FlatList>(null);
  const channelRef = useRef<any>(null);
  const optimisticMessages = useRef<Map<string, Message>>(new Map());
  const processedMessageIds = useRef<Set<string>>(new Set());
  const shouldScrollToBottom = useRef(true);

  // Scroll helpers - simplified for inverted list
  const scrollToBottom = useCallback(() => {
    // With inverted list, new messages automatically appear at bottom
    // No need to scroll manually
  }, []);

  const handleScroll = useCallback((event: any) => {
    // With inverted list, "bottom" is actually contentOffset.y near 0
    const { contentOffset } = event.nativeEvent;
    const isNearBottom = contentOffset.y < 100;
    shouldScrollToBottom.current = isNearBottom;
  }, []);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!conversationId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: false })
        .limit(LIMIT);

      if (error) throw error;

      // Keep data in DESC order for inverted FlatList
      const formattedMessages = data || [];

      // Initial load
      setMessages(formattedMessages);
      // Mark as processed to avoid duplicates
      formattedMessages.forEach((msg) => processedMessageIds.current.add(msg.id));

      // Mark messages as read (batch update with error handling)
      if (data && data.length > 0 && user) {
        // Filter unread messages from other users
        const unreadMessages = data.filter((msg) => msg.sender_id !== user.id && !msg.read_at);

        if (unreadMessages.length > 0) {
          try {
            await supabase
              .from('messages')
              .update({ read_at: new Date().toISOString() })
              .eq('conversation_id', conversationId)
              .is('read_at', null)
              .neq('sender_id', user.id);
          } catch {
            // Silent fail
          }
        }
      }
    } catch {
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [conversationId, user]);

  // Check existing conversation
  const checkExistingConversation = useCallback(async () => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error checking conversation:', error);
        return null;
      }

      return data?.id || null;
    } catch {
      return null;
    }
  }, [user]);

  // Create new conversation
  const createNewConversation = async (initialMessage: string): Promise<string | null> => {
    if (!user) return null;

    try {
      const { data: conversationData, error: conversationError } = await supabase
        .from('conversations')
        .insert({
          user_id: user.id,
          user_role: user.role,
          last_message: initialMessage,
          last_message_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (conversationError) throw conversationError;

      return conversationData.id;
    } catch {
      Alert.alert('Error', 'Unable to create conversation');
      return null;
    }
  };

  // Send message with optimistic update
  const handleSendMessage = useCallback(
    async (text: string) => {
      if (!text.trim() || !user) return;

      const tempId = String(uuid.v4());
      const optimisticMessage: Message = {
        id: tempId,
        temp_id: tempId,
        conversation_id: conversationId || '',
        sender_id: user.id,
        content: text.trim(),
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        isPending: true,
      };

      try {
        setInputMessage('');

        // Create conversation if needed
        let currentConversationId = conversationId;
        if (!currentConversationId) {
          currentConversationId = await createNewConversation(text);
          if (!currentConversationId) {
            return;
          }
          setConversationId(currentConversationId);
          optimisticMessage.conversation_id = currentConversationId;
        }

        // Add optimistic message at the beginning (will appear at bottom with inverted list)
        optimisticMessages.current.set(tempId, optimisticMessage);
        setMessages((prev) => [optimisticMessage, ...prev]);

        // Send actual message
        const { data, error } = await supabase
          .from('messages')
          .insert({
            conversation_id: currentConversationId,
            sender_id: user.id,
            content: text.trim(),
            is_system: false,
          })
          .select()
          .single();

        if (error) throw error;

        // Remove optimistic and add real message
        optimisticMessages.current.delete(tempId);
        processedMessageIds.current.add(data.id);

        setMessages((prev) => {
          // Filter out the optimistic message and any duplicates
          const filtered = prev.filter((msg) => {
            // Remove optimistic message
            if (msg.temp_id === tempId) return false;
            // Remove any existing message with same ID
            if (msg.id === data.id) return false;
            return true;
          });
          // Add the real message at the beginning
          return [{ ...data, isPending: false }, ...filtered];
        });

        // Update conversation's last message
        await supabase
          .from('conversations')
          .update({
            last_message: text.trim(),
            last_message_at: new Date().toISOString(),
          })
          .eq('id', currentConversationId);
      } catch {
        Alert.alert('Error', 'Failed to send message');
        optimisticMessages.current.delete(tempId);
        setMessages((prev) => prev.filter((msg) => msg.temp_id !== tempId));
      }
    },
    [conversationId, user, scrollToBottom]
  );

  // Setup realtime subscription
  const setupRealtimeSubscription = useCallback(
    (convId: string) => {
      // Cleanup previous subscription
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }

      channelRef.current = supabase
        .channel(`messages:${convId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${convId}`,
          },
          (payload) => {
            const newMessage = payload.new as Message;

            if (processedMessageIds.current.has(newMessage.id)) {
              return;
            }

            processedMessageIds.current.add(newMessage.id);

            setMessages((prev) => {
              const exists = prev.some((msg) => msg.id === newMessage.id && !msg.temp_id);
              if (exists) {
                return prev;
              }

              if (newMessage.sender_id === user?.id) {
                const hasOptimistic = prev.some((msg) => msg.isPending);
                if (hasOptimistic) {
                  return prev;
                }
              }

              return [newMessage, ...prev];
            });

            // Mark as read if from other user (with delay to avoid conflicts)
            if (newMessage.sender_id !== user?.id && !newMessage.read_at) {
              // Delay to avoid race condition with database
              setTimeout(async () => {
                try {
                  await supabase
                    .from('messages')
                    .update({ read_at: new Date().toISOString() })
                    .eq('id', newMessage.id)
                    .is('read_at', null);
                } catch {
                  // Silent fail
                }
              }, 1000);
            }
          }
        )
        .subscribe();
    },
    [user, scrollToBottom]
  );

  // Initialize
  useEffect(() => {
    const initializeChat = async () => {
      if (!user) return;

      // Clear any previous state
      processedMessageIds.current.clear();
      optimisticMessages.current.clear();
      setMessages([]);

      const existingConvId = await checkExistingConversation();

      if (existingConvId) {
        setConversationId(existingConvId);
        setupRealtimeSubscription(existingConvId);
        await fetchMessages();
      } else {
        setLoading(false);
      }
    };

    initializeChat();

    // Cleanup
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [user, checkExistingConversation, setupRealtimeSubscription]);

  // Fetch messages when conversation changes
  useEffect(() => {
    if (conversationId) {
      processedMessageIds.current.clear();
      optimisticMessages.current.clear();
      fetchMessages();
    }
  }, [conversationId, fetchMessages]);

  // Render message item
  const renderMessage = useCallback(
    ({ item }: { item: Message }) => {
      const message: ChatMessageType = {
        id: item.temp_id || item.id,
        text: item.content,
        sender: item.sender_id === user?.id ? 'user' : 'service',
        timestamp: new Date(item.created_at),
        sending: item.isPending,
      };

      return <ChatMessage message={message} formatTime={formatMessageTime} mode="normal" />;
    },
    [user]
  );

  // Render empty state
  const renderEmpty = useCallback(() => {
    return (
      <View className="flex-1 items-center justify-center py-20">
        <View className="items-center">
          <View className="mb-4 h-20 w-20 items-center justify-center rounded-full bg-gray-100">
            <Text className="text-3xl">💬</Text>
          </View>
          <Text className="text-base font-medium text-gray-900">No messages yet</Text>
          <Text className="mt-1 text-sm text-gray-500">Start a conversation</Text>
        </View>
      </View>
    );
  }, []);

  // Key extractor - ensure unique keys
  const keyExtractor = useCallback((item: Message, index: number) => {
    // Use temp_id if available (for optimistic updates)
    if (item.temp_id) return item.temp_id;
    // Otherwise use message id with index as fallback
    return item.id || `msg-${index}-${item.created_at}`;
  }, []);

  return (
    <MainLayout scrollable={false}>
      <ChatHeader
        title="Support Chat"
        subtitle="We're here to help"
        onClearHistory={() => {}}
        mode="normal"
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        className="flex-1">
        <View className="flex-1 bg-gray-50">
          {loading && messages.length === 0 ? (
            <View className="flex-1 items-center justify-center">
              <Spinner size="large" />
              <Text className="mt-2 text-gray-500">Loading messages...</Text>
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessage}
              keyExtractor={keyExtractor}
              className="flex-1 bg-white"
              contentContainerStyle={{
                padding: 16,
                paddingBottom: 20,
                flexGrow: messages.length === 0 ? 1 : undefined,
              }}
              showsVerticalScrollIndicator={true}
              inverted={true}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              ListEmptyComponent={renderEmpty}
              removeClippedSubviews={true}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
              updateCellsBatchingPeriod={50}
            />
          )}
        </View>

        <Box className="border-t border-gray-200">
          <ChatInput
            initialMessage={inputMessage}
            onSendMessage={handleSendMessage}
            mode="normal"
          />
        </Box>
      </KeyboardAvoidingView>
    </MainLayout>
  );
}
