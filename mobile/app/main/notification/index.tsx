import { formatDistanceToNow } from 'date-fns';
import {
  Bell,
  Check,
  Trash2,
  AlertCircle,
  CheckCircle2,
  Info,
  AlertTriangle,
} from 'lucide-react-native';
import { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';

import { HStack, VStack } from '@/components/ui';
import { MainLayout } from '~/components/layout/MainLayout';

export type NotificationType = 'info' | 'warning' | 'success' | 'error';

export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  type: NotificationType;
  metadata?: Record<string, any>;
}

export default function NotificationScreen() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load notifications from database
  const loadNotifications = async () => {
    try {
      // For now, use sample data. Replace with actual database query
      const sampleNotifications: Notification[] = [
        {
          id: '1',
          title: 'Booking Confirmed',
          message: 'Your service booking has been confirmed for tomorrow at 10:00 AM',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          read: false,
          type: 'success',
        },
        {
          id: '2',
          title: 'Payment Received',
          message: 'We have received your payment of $125.00 for order #ORD-2024-001',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          read: false,
          type: 'success',
        },
        {
          id: '3',
          title: 'Service Reminder',
          message: 'Your scheduled service is coming up in 2 hours. Please be ready.',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
          read: true,
          type: 'info',
        },
        {
          id: '4',
          title: 'Schedule Change',
          message: 'Your technician has requested to reschedule. Please review the new time.',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
          read: false,
          type: 'warning',
        },
        {
          id: '5',
          title: 'Special Offer',
          message: 'Get 20% off on your next service. Limited time offer!',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
          read: true,
          type: 'info',
        },
      ];

      setNotifications(sampleNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const markAsRead = (id: string) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({
        ...notification,
        read: true,
      }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(notifications.filter((notification) => notification.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return <CheckCircle2 size={20} color="#10b981" />;
      case 'warning':
        return <AlertTriangle size={20} color="#f59e0b" />;
      case 'error':
        return <AlertCircle size={20} color="#ef4444" />;
      default:
        return <Info size={20} color="#3b82f6" />;
    }
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const renderNotification = ({ item }: { item: Notification }) => {
    return (
      <TouchableOpacity
        onPress={() => markAsRead(item.id)}
        className={`mb-3 rounded-xl border p-4 shadow-sm ${
          item.read ? 'border-gray-200 bg-white' : 'border-blue-200 bg-blue-50'
        }`}>
        <HStack className="items-start justify-between">
          <HStack className="flex-1 items-start" space="sm">
            <View className="mt-0.5">{getNotificationIcon(item.type)}</View>
            <VStack className="flex-1" space="xs">
              <Text
                className={`text-base font-semibold ${
                  item.read ? 'text-gray-700' : 'text-gray-900'
                }`}>
                {item.title}
              </Text>
              <Text className={`text-sm ${item.read ? 'text-gray-500' : 'text-gray-600'}`}>
                {item.message}
              </Text>
              <Text className="text-xs text-gray-400">
                {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true })}
              </Text>
            </VStack>
          </HStack>

          <HStack space="xs">
            {!item.read && (
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation();
                  markAsRead(item.id);
                }}
                className="rounded-full bg-white/80 p-2 shadow-sm">
                <Check size={14} color="#6b7280" />
              </TouchableOpacity>
            )}
            <TouchableOpacity
              onPress={(e) => {
                e.stopPropagation();
                deleteNotification(item.id);
              }}
              className={`rounded-full p-2 shadow-sm ${item.read ? 'bg-gray-100' : 'bg-white/80'}`}>
              <Trash2 size={14} color="#6b7280" />
            </TouchableOpacity>
          </HStack>
        </HStack>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => {
    if (loading) {
      return (
        <View className="flex-1 items-center justify-center p-4">
          <ActivityIndicator size="large" color="#0284c7" />
          <Text className="mt-4 text-gray-600">Loading notifications...</Text>
        </View>
      );
    }

    return (
      <View className="flex-1 items-center justify-center p-4">
        <View className="mb-4 rounded-full bg-gray-100 p-4">
          <Bell size={48} color="#9ca3af" />
        </View>
        <Text className="text-xl font-semibold text-gray-800">No Notifications</Text>
        <Text className="mt-2 text-center text-gray-600">
          You're all caught up! New notifications will appear here.
        </Text>
      </View>
    );
  };

  return (
    <MainLayout scrollable={false}>
      <View className="flex-1 bg-gray-50 p-4">
        {/* Header */}
        <View className="pb-4">
          <HStack className="items-center justify-between">
            <View>
              <Text className="text-2xl font-bold text-gray-900">Notifications</Text>
              {unreadCount > 0 && (
                <Text className="text-sm text-gray-500">
                  {unreadCount} unread notification{unreadCount > 1 ? 's' : ''}
                </Text>
              )}
            </View>

            {notifications.length > 0 && (
              <HStack space="xs">
                <TouchableOpacity
                  onPress={markAllAsRead}
                  className="flex-row items-center rounded-md bg-blue-100 px-3 py-1.5">
                  <Check size={14} color="#3b82f6" />
                  <Text className="ml-1 text-xs font-medium text-blue-600">Mark all</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={clearAllNotifications}
                  className="flex-row items-center rounded-md bg-gray-100 px-3 py-1.5">
                  <Trash2 size={14} color="#6b7280" />
                  <Text className="ml-1 text-xs font-medium text-gray-600">Clear</Text>
                </TouchableOpacity>
              </HStack>
            )}
          </HStack>
        </View>

        {/* Notifications List */}
        <FlatList
          data={notifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{
            flexGrow: 1,
          }}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#0284c7']} />
          }
        />
      </View>
    </MainLayout>
  );
}
