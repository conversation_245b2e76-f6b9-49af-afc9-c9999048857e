import React, { useState, useCallback } from 'react';
import { Platform, View } from 'react-native';

import { Message, ChatHeader, ChatInput, ChatMessageList } from '@/components/chat';
import {
  formatMessageTime,
  createUserMessage,
  createServiceMessage,
  createTypingMessage,
} from '@/components/chat/utils';
import { KeyboardAvoidingView } from '@/components/ui/keyboard-avoiding-view';
import { MainLayout } from '~/components/layout/MainLayout';
import { sendMessageToAI, clearAIConversation } from '~/services/ai-assistant';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  formattedContent?: string;
}

export default function AssistantScreen() {
  const [isProcessing, setIsProcessing] = useState(false);

  // Messages for AI mode (newest first for inverted list)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I am an AI assistant, how can I help you?',
      sender: 'service',
      timestamp: new Date(),
    },
  ]);

  // Handle sending message to AI
  const handleSendMessage = useCallback(
    async (text: string) => {
      if (!text.trim() || isProcessing) return;

      // Add user message (at beginning for inverted list)
      const userMessage = createUserMessage(text);
      setMessages((prev) => [userMessage, ...prev]);

      // Get AI response
      try {
        setIsProcessing(true);

        // Add typing indicator message (at beginning for inverted list)
        const typingMessage = createTypingMessage();
        setMessages((prev) => [typingMessage, ...prev]);

        // Prepare chat history for AI context (reverse for chronological order)
        const chatHistory: ChatMessage[] = [...messages].reverse().map((msg) => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.text || '',
        }));

        // Call AI service
        const aiResponse = await sendMessageToAI(text, chatHistory);

        // Remove typing message and add actual response (at beginning for inverted list)
        setMessages((prev) => [
          createServiceMessage(aiResponse),
          ...prev.filter((msg) => !msg.isTyping),
        ]);
      } catch {
        setMessages((prev) => [
          createServiceMessage(
            'Sorry, an error occurred while processing your request. Please try again later.'
          ),
          ...prev.filter((msg) => !msg.isTyping),
        ]);
      } finally {
        setIsProcessing(false);
      }
    },
    [isProcessing, messages]
  );

  // Handle clearing chat history
  const handleClearHistory = useCallback(async () => {
    try {
      await clearAIConversation();
      setMessages([
        {
          id: '1',
          text: 'Hello! I am an AI assistant, how can I help you?',
          sender: 'service',
          timestamp: new Date(),
        },
      ]);
    } catch {
      // Silent fail
    }
  }, []);

  // Get subtitle text with AI status
  const getSubtitle = () => {
    if (isProcessing) return 'Processing...';
    return 'Powered by Vinast Lotus';
  };

  return (
    <MainLayout scrollable={false}>
      <ChatHeader
        title="AI Assistant"
        subtitle={getSubtitle()}
        onClearHistory={handleClearHistory}
        mode="ai"
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        className="flex-1">
        <View className="flex-1 bg-indigo-50">
          <ChatMessageList messages={messages} formatTime={formatMessageTime} mode="ai" />
        </View>

        <ChatInput initialMessage="" onSendMessage={handleSendMessage} mode="ai" />
      </KeyboardAvoidingView>
    </MainLayout>
  );
}
