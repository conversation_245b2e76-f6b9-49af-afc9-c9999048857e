import { Ionicons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import { Text, TouchableOpacity, ScrollView } from 'react-native';

import ServiceList from '@/components/main/ServiceList';
import { MainLayout } from '@/components/layout/MainLayout';
import { Box, HStack, VStack } from '@/components/ui';

export default function Home() {
  // Define current booking data
  const currentBooking = {
    id: 1,
    service: 'Full Car Inspection',
    garage: 'Premium Auto Service',
    date: '28 Mar 2023',
    time: '14:30',
    status: 'Confirmed',
    estimatedTime: '45 mins',
  };

  // Service data will be fetched directly by the ServiceList component

  const recentBookings = [
    {
      id: 1,
      service: 'Oil Change',
      garage: 'AutoCare Center',
      date: '24 Mar 2023',
      status: 'Completed',
    },
    {
      id: 2,
      service: 'Brake Inspection',
      garage: 'Express Repair Shop',
      date: '18 Mar 2023',
      status: 'Scheduled',
    },
  ];

  return (
    <MainLayout>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView className="px-4 py-4">
        {/* Current Booking */}
        {currentBooking && (
          <Box className="mb-6">
            <HStack className="mb-3 items-center justify-between">
              <Text className="text-lg font-bold text-gray-900">Current Booking</Text>
            </HStack>
            <Box className="rounded-xl bg-gray-900 p-4">
              <HStack className="items-center justify-between">
                <HStack className="items-center">
                  <Box className="h-12 w-12 items-center justify-center rounded-full bg-gray-800">
                    <Ionicons name="car" size={24} color="#f59e0b" />
                  </Box>
                  <VStack className="ml-3">
                    <Text className="text-base font-bold text-white">{currentBooking.service}</Text>
                    <Text className="text-sm text-gray-300">{currentBooking.garage}</Text>
                  </VStack>
                </HStack>
                <Box className="rounded-full bg-gray-800 px-3 py-1">
                  <Text className="text-xs font-medium text-yellow-400">
                    {currentBooking.status}
                  </Text>
                </Box>
              </HStack>

              <HStack className="mt-4 justify-between">
                <HStack className="items-center">
                  <Ionicons name="calendar" size={18} color="#f59e0b" />
                  <Text className="ml-2 text-sm text-gray-300">{currentBooking.date}</Text>
                </HStack>
                <HStack className="items-center">
                  <Ionicons name="time" size={18} color="#f59e0b" />
                  <Text className="ml-2 text-sm text-gray-300">{currentBooking.time}</Text>
                </HStack>
                <HStack className="items-center">
                  <Ionicons name="hourglass" size={18} color="#f59e0b" />
                  <Text className="ml-2 text-sm text-gray-300">{currentBooking.estimatedTime}</Text>
                </HStack>
              </HStack>

              <HStack className="mt-4 justify-between">
                <TouchableOpacity className="flex-1 items-center rounded-lg bg-gray-800 py-2">
                  <Text className="font-medium text-gray-300">View Details</Text>
                </TouchableOpacity>
                <Box className="w-4" />
                <TouchableOpacity className="flex-1 items-center rounded-lg bg-yellow-500 py-2">
                  <Text className="font-medium text-gray-900">Get Directions</Text>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        )}

        {/* Recent Bookings */}
        <Box className="mb-6">
          <HStack className="mb-3 items-center justify-between">
            <Text className="text-lg font-bold text-gray-900">Recent Bookings</Text>
            <TouchableOpacity>
              <Text className="text-blue-500">See All</Text>
            </TouchableOpacity>
          </HStack>

          {recentBookings.map((booking) => (
            <TouchableOpacity key={booking.id} className="mb-3 rounded-xl bg-gray-50 p-4">
              <HStack className="justify-between">
                <Text className="font-bold">{booking.service}</Text>
                <Box
                  className={`rounded-full px-2 py-1 ${
                    booking.status === 'Completed' ? 'bg-green-100' : 'bg-amber-100'
                  }`}>
                  <Text
                    className={`text-xs ${
                      booking.status === 'Completed' ? 'text-green-700' : 'text-amber-700'
                    }`}>
                    {booking.status}
                  </Text>
                </Box>
              </HStack>
              <Box className="mt-2">
                <HStack className="items-center">
                  <Ionicons name="business-outline" size={16} color="#666" />
                  <Text className="ml-2 text-gray-700">{booking.garage}</Text>
                </HStack>
                <HStack className="mt-1 items-center">
                  <Ionicons name="calendar-outline" size={16} color="#666" />
                  <Text className="ml-2 text-gray-700">{booking.date}</Text>
                </HStack>
              </Box>
            </TouchableOpacity>
          ))}
        </Box>

        {/* Services Section - now fetches data from Supabase */}
        <ServiceList />

        {/* Technician Section */}
        {/* <Box className="mb-6">
          <TouchableOpacity
            className="rounded-lg bg-yellow-500 p-3"
            onPress={() => {
              // @ts-ignore - Router type constraints are causing issues, but this works
              router.push('/technician');
            }}>
            <Text className="text-center font-bold text-gray-900">TECHNICIAN</Text>
          </TouchableOpacity>
        </Box> */}
      </ScrollView>
    </MainLayout>
  );
}
