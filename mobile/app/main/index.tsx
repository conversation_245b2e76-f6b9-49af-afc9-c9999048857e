import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { View, ActivityIndicator } from 'react-native';
import { useAuth } from '@/core/contexts/AuthContext';
import { useAppType } from '@/core/contexts/AppTypeContext';

export default function Home() {
  const router = useRouter();
  const { user } = useAuth();
  const { appType } = useAppType();

  useEffect(() => {
    // Redirect to appropriate home screen based on user role
    if (user) {
      const userRole = user.user_metadata?.role || appType;
      
      if (userRole === 'technician') {
        router.replace('/technician/home');
      } else {
        router.replace('/customer/home');
      }
    }
  }, [user, appType, router]);

  // Show loading spinner while determining where to redirect
  return (
    <View className="flex-1 items-center justify-center bg-white">
      <ActivityIndicator size="large" color="#3b82f6" />
    </View>
  );
}
