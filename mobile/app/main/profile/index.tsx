import { Stack } from 'expo-router';
import {
  User,
  Mail,
  Phone,
  Calendar,
  Settings,
  ChevronRight,
  Shield,
  Bell,
  LogOut,
} from 'lucide-react-native';
import { ReactNode } from 'react';
import { View, ScrollView, TouchableOpacity, Alert } from 'react-native';

import { Button } from '@/components/ui/button';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Text } from '@/components/ui/text';
import { useAuth } from '@/core/contexts/AuthContext';

interface ProfileMenuItemProps {
  icon: ReactNode;
  title: string;
  subtitle?: string;
  showChevron?: boolean;
  onPress?: () => void;
}

export default function ProfileScreen() {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    Alert.alert('Logout Confirmation', 'Are you sure you want to logout?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Logout',
        onPress: async () => await logout(),
        style: 'destructive',
      },
    ]);
  };

  const ProfileMenuItem = ({
    icon,
    title,
    subtitle,
    showChevron = true,
    onPress = () => {},
  }: ProfileMenuItemProps) => (
    <TouchableOpacity
      onPress={onPress}
      className="flex-row items-center border-b border-gray-100 py-4">
      <View className="mr-4 items-center justify-center">{icon}</View>
      <View className="flex-1">
        <Text className="text-base font-medium text-gray-800">{title}</Text>
        {subtitle && <Text className="text-sm text-gray-500">{subtitle}</Text>}
      </View>
      {showChevron && <ChevronRight size={16} color="#cbd5e1" />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Stack.Screen options={{ title: 'Profile', headerShown: false }} />
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 pb-8 pt-12">
          <View className="items-center">
            <View className="mb-4 h-24 w-24 items-center justify-center rounded-full bg-gray-100">
              <User size={40} color="#64748b" />
            </View>
            <Text className="text-xl font-medium text-gray-800">{user?.name || 'User'}</Text>
            <View className="mt-1 flex-row items-center">
              <Mail size={14} color="#94a3b8" className="mr-1" />
              <Text className="text-gray-500">{user?.email}</Text>
            </View>
          </View>

          <View className="mt-8 flex-row justify-between">
            <TouchableOpacity className="flex-1 items-center">
              <View className="items-center justify-center">
                <Settings size={20} color="#64748b" />
              </View>
              <Text className="mt-1 text-xs text-gray-500">Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity className="flex-1 items-center">
              <View className="items-center justify-center">
                <Shield size={20} color="#64748b" />
              </View>
              <Text className="mt-1 text-xs text-gray-500">Security</Text>
            </TouchableOpacity>

            <TouchableOpacity className="flex-1 items-center">
              <View className="items-center justify-center">
                <Bell size={20} color="#64748b" />
              </View>
              <Text className="mt-1 text-xs text-gray-500">Notifications</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Divider */}
        <View className="h-2 bg-gray-50" />

        {/* Content */}
        <View className="p-6">
          {/* Personal Info */}
          <Text className="mb-4 text-base font-semibold text-gray-800">Personal Information</Text>

          <ProfileMenuItem
            icon={<User size={18} color="#64748b" />}
            title="Full Name"
            subtitle={user?.name || 'Not set'}
          />

          <ProfileMenuItem
            icon={<Mail size={18} color="#64748b" />}
            title="Email"
            subtitle={user?.email || 'Not set'}
          />

          <ProfileMenuItem
            icon={<Phone size={18} color="#64748b" />}
            title="Phone"
            subtitle="Not set"
          />

          <ProfileMenuItem
            icon={<Calendar size={18} color="#64748b" />}
            title="Date Joined"
            subtitle="March 2023"
            showChevron={false}
          />

          {/* Account Info */}
          <Text className="mb-4 mt-8 text-base font-semibold text-gray-800">
            Account Information
          </Text>

          <ProfileMenuItem
            icon={<Shield size={18} color="#64748b" />}
            title="Security Settings"
            subtitle="Password, 2FA"
          />

          <ProfileMenuItem
            icon={<Bell size={18} color="#64748b" />}
            title="Notifications"
            subtitle="App notifications"
          />

          {/* Logout Button */}
          <View className="mt-12">
            <Button
              onPress={handleLogout}
              className="flex-row items-center justify-center bg-gray-100">
              <LogOut size={16} color="#334155" className="mr-2" />
              <Text className="font-medium text-gray-700">Logout</Text>
            </Button>
          </View>

          <Text className="mb-4 mt-8 text-center text-xs text-gray-400">Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
