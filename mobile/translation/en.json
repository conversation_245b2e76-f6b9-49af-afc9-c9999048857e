{"button": {"french": "Switch language to French", "english": "Switch language to English", "language": "Language", "selectLanguage": "Select Language"}, "common": {"loading": "Loading...", "authenticating": "Checking authentication...", "welcome": "Welcome", "error": "An error occurred"}, "getStarted": "Open up the code for this screen:", "changeCode": "Change any of the text, save the file, and your app will automatically update.", "login": {"title": "<PERSON><PERSON>", "subtitle": "Please login to continue", "email": "Email", "emailHelperText": "Your email is secure with us", "emailError": "Invalid email", "password": "Password", "passwordError": "Password must be at least 6 characters", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "registerNow": "Register now", "forgotPassword": "Forgot password?", "logoAlt": "App Logo"}, "register": {"title": "Register", "subtitle": "Create a new account", "name": "Full Name", "namePlaceholder": "Enter your full name", "nameError": "Name must be at least 2 characters", "email": "Email", "emailPlaceholder": "Enter your email", "emailHelperText": "Your email is secure with us", "emailError": "Invalid email", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "phoneError": "Phone number is required", "phoneFormatError": "Phone number must contain only digits", "address": "Address", "addressPlaceholder": "Enter your address", "addressError": "Address is required", "password": "Password", "passwordPlaceholder": "Enter your password", "passwordHelperText": "Password must be at least 6 characters", "passwordError": "Password must be at least 6 characters", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "confirmPasswordError": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "registerButton": "Register", "haveAccount": "Already have an account?", "loginNow": "<PERSON><PERSON>"}}