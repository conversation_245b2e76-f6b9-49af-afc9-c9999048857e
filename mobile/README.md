## Cấu hình môi trường

Ứng dụng hỗ trợ các biến môi trường sau:

```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_APP_URL=your_app_url
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
EXPO_PUBLIC_APP_TYPE=customer|technician
```

### APP_TYPE

Biến môi trường `EXPO_PUBLIC_APP_TYPE` được sử dụng để xác định loại ứng dụng (customer hoặc technician). Mặc định là "customer".

Để sử dụng biến này:

1. Thiết lập trong file `.env`:

   ```
   EXPO_PUBLIC_APP_TYPE=technician
   ```

2. Hoặc khi chạy ứng dụng:
   ```bash
   EXPO_PUBLIC_APP_TYPE=technician npx expo start
   ```

## Sử dụng AppTypeContext

Để sử dụng thông tin về loại ứng dụng trong component:

```tsx
import { useAppType } from '@/core/contexts/AppTypeContext';

function MyComponent() {
  const { appType, isCustomer, isTechnician } = useAppType();

  return (
    <View>
      {isCustomer && <Text>Phiên bản dành cho khách hàng</Text>}
      {isTechnician && <Text>Phiên bản dành cho kỹ thuật viên</Text>}
    </View>
  );
}
```
