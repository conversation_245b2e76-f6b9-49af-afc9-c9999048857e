{"expo": {"name": "vtech", "slug": "vtech", "version": "1.0.0", "scheme": "vtech", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-localization", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow VTech to use your location."}], ["expo-notifications", {"color": "#ffffff", "sounds": [], "androidMode": "default", "androidCollapsedTitle": "VTECH App", "iosDisplayInForeground": true, "enableBackgroundRemoteNotifications": true}], "expo-web-browser"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.ledhcg.vtech", "usesIcloudStorage": true, "infoPlist": {"LSApplicationQueriesSchemes": ["tel", "telprompt"]}}, "android": {"edgeToEdgeEnabled": true, "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.ledhcg.vtech", "permissions": ["android.permission.RECORD_AUDIO"], "config": {"googleMaps": {"apiKey": "AIzaSyAJsk90lKXgewWJogEk_zImSayhYaUjaBQ"}}}, "extra": {"router": {"origin": false}, "eas": {"projectId": "ad6da0b0-f504-4a1f-abef-842eabdf2ee1"}}}}