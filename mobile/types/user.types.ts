export type UserRole = 'customer' | 'technician' | 'staff' | 'admin';

export interface UserProfileData {
  rating?: number;
  exp?: number;
  certs?: string[];
}

// Define the user profile interface
export interface User {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  address?: string;
  role: UserRole;
  profile_data?: UserProfileData;
  created_at: string;
  updated_at: string;
}
