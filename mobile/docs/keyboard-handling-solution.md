# Keyboard Handling Solution for React Native App

## Problem
The keyboard was covering input fields in authentication screens and other forms throughout the app, making it difficult for users to see what they're typing.

## Solution Applied

### 1. AuthLayout Enhancement
Updated `/app/auth/_layout.tsx` to wrap the entire authentication flow with `KeyboardAvoidingView`:

```tsx
<KeyboardAvoidingView 
  className="flex-1 bg-white"
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
  keyboardVerticalOffset={0}>
  {/* ScrollView with proper keyboard handling */}
  <ScrollView 
    keyboardShouldPersistTaps="handled"
    showsVerticalScrollIndicator={false}>
    {/* Content */}
  </ScrollView>
</KeyboardAvoidingView>
```

Key improvements:
- Added `KeyboardAvoidingView` as the root wrapper
- Set different behaviors for iOS (`padding`) and Android (`height`)
- Added `keyboardShouldPersistTaps="handled"` to ScrollView
- Added extra bottom padding to ensure content is visible above keyboard

### 2. MainLayout Enhancement
Updated `/components/layout/MainLayout.tsx` to support keyboard avoiding throughout the app:

```tsx
// Added optional keyboardAvoidingEnabled prop (default true)
keyboardAvoidingEnabled?: boolean

// Wraps content in KeyboardAvoidingView when enabled
if (keyboardAvoidingEnabled) {
  return (
    <KeyboardAvoidingView 
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={0}>
      <Content />
    </KeyboardAvoidingView>
  );
}
```

### 3. Reusable Component
Created `/components/common/KeyboardAvoidingContainer.tsx` for consistent keyboard handling:

```tsx
export function KeyboardAvoidingContainer({
  children,
  scrollable = true,
  // ... other props
}) {
  // Provides consistent keyboard avoiding behavior
  // Can be used as a drop-in replacement anywhere in the app
}
```

## Platform-Specific Behaviors

### iOS
- Uses `padding` behavior which adds padding to the bottom of the view
- Works well with ScrollView and maintains smooth animations

### Android
- Uses `height` behavior which reduces the height of the view
- Better compatibility with Android's keyboard handling
- Prevents layout jumps

## Best Practices Applied

1. **keyboardShouldPersistTaps="handled"**: Allows taps on buttons and other interactive elements even when keyboard is visible

2. **showsVerticalScrollIndicator={false}**: Cleaner UI without scroll indicators

3. **Extra Bottom Padding**: Added `insets.bottom + 20` to ensure content isn't too close to keyboard

4. **Consistent Behavior**: All auth screens now have uniform keyboard handling

## Testing Guidelines

### iOS Testing
1. Open any authentication screen
2. Tap on email/password fields
3. Verify that:
   - Input fields scroll up above keyboard
   - You can see what you're typing
   - Submit button is accessible

### Android Testing
1. Open any authentication screen
2. Tap on input fields
3. Verify that:
   - Screen adjusts height appropriately
   - No layout jumping occurs
   - All inputs remain accessible

## Usage in Other Screens

To apply keyboard handling to other screens:

```tsx
// Option 1: Use MainLayout (has keyboard avoiding built-in)
<MainLayout>
  {/* Your content */}
</MainLayout>

// Option 2: Use KeyboardAvoidingContainer
import { KeyboardAvoidingContainer } from '@/components/common/KeyboardAvoidingContainer';

<KeyboardAvoidingContainer scrollable={true}>
  {/* Your form content */}
</KeyboardAvoidingContainer>

// Option 3: Disable keyboard avoiding if not needed
<MainLayout keyboardAvoidingEnabled={false}>
  {/* Content that doesn't need keyboard handling */}
</MainLayout>
```

## Known Considerations

1. **Performance**: KeyboardAvoidingView has minimal performance impact
2. **Nested ScrollViews**: Avoid nesting multiple ScrollViews
3. **Modal Screens**: May need additional `keyboardVerticalOffset` adjustment

## Affected Screens
- Login screen (`/app/auth/login.tsx`)
- Register screens (customer & technician)
- Chat screen (already had keyboard handling)
- Profile and other screens with MainLayout

This solution ensures a consistent and smooth keyboard experience across the entire React Native application.