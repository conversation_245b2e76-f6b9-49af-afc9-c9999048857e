import * as React from 'react';
import Svg, { De<PERSON>, <PERSON>ar<PERSON><PERSON>ient, Stop, <PERSON>lip<PERSON><PERSON>, Path } from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: style */

function LogoSvg(props: any) {
  return (
    <Svg
      id="svg3216"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 640 480"
      {...props}
      width={props.width}
      height={props.height}>
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1={81.1}
          y1={228.2}
          x2={580.7}
          y2={228.2}
          gradientUnits="userSpaceOnUse">
          <Stop offset={0} stopColor="#d00048" />
          <Stop offset={1} />
        </LinearGradient>
        <ClipPath id="clippath">
          <Path d="M81.1 188.8c23.1 13 26.2 40.3 29 50.9 85.3-28.2 199.4-130.7 276.1-39-78.1-107.7-182-2.5-247.9 12.7-13.3 1.7-28.7-12.4-57.1-24.6zm91.4 19.1c140 56.3 264.2-57.1 408.3 34.2-100.3-97.9-280.2.3-392.4-47.8l-15.9 13.5zm20.1 15.3c161.8 42.4 222.2-35.1 294.3 2.3-70-24.4-127.8 65.7-294.3-2.3zm-72.8 47.5l7.3 21.4 1.1 3.6h.3l1.2-3.6 7.6-21.4h5.4L131 302.9h-5.1l-11.4-32.2h5.4zm24.7 4.7v-4.7h23v4.7h-9v27.5h-5v-27.5h-9zm47.3-4.7v4.7h-14.6v9.1h13.1v4.7h-13.1v9.1h14.6v4.7h-19.5v-32.2h19.5zm5.9 24.7c-1.5-2.6-2.2-5.4-2.2-8.5s.7-6 2.2-8.5c1.5-2.6 3.5-4.6 6.1-6.1s5.4-2.2 8.5-2.2c4.8 0 8.8 1.8 11.9 5.3l-3.5 3.4c-1.1-1.3-2.3-2.3-3.7-3-1.4-.7-2.9-1-4.7-1s-4.2.5-6 1.5c-1.8 1-3.2 2.4-4.3 4.3s-1.6 4-1.6 6.4.5 4.5 1.6 6.4c1 1.8 2.5 3.3 4.3 4.3 1.8 1 3.8 1.5 6 1.5 3.7 0 6.7-1.5 9.2-4.5l3.6 3.4c-1.6 1.8-3.4 3.3-5.6 4.3-2.2 1-4.6 1.5-7.2 1.5s-6-.7-8.5-2.2c-2.6-1.5-4.6-3.5-6.1-6.1zm37.9-24.7v13.4h15.1v-13.4h4.9v32.2h-4.9v-14.1h-15.1v14.1h-4.9v-32.2h4.9zm51.2 0l12.2 32.2h-5.4l-3-8.3h-13.1l-3 8.3h-5.4l12.1-32.2h5.6zm-1.4 9.5l-1.2-3.7h-.3l-1.2 3.7-3.6 9.9h9.9l-3.6-9.9zm18.3 17.2c-1-2-1.5-4.3-1.5-6.9v-19.8h4.9v20.1c0 2.4.6 4.4 1.9 5.9 1.3 1.5 3 2.2 5.4 2.2s4.1-.8 5.4-2.2c1.3-1.5 1.9-3.5 1.9-5.9v-20.1h4.9v19.8c0 2.6-.5 4.9-1.5 6.8-1 2-2.4 3.5-4.2 4.6-1.8 1.1-4 1.6-6.5 1.6s-4.5-.5-6.4-1.6-3.3-2.6-4.3-4.6zm27-22v-4.7h23v4.7h-9v27.5h-4.9v-27.5h-9zm27 19.9c-1.5-2.6-2.2-5.4-2.2-8.5s.7-5.9 2.2-8.5c1.5-2.6 3.5-4.6 6-6.1s5.4-2.2 8.5-2.2 5.9.8 8.5 2.2c2.5 1.5 4.5 3.5 6 6.1 1.5 2.6 2.2 5.4 2.2 8.5s-.7 5.9-2.2 8.5c-1.5 2.6-3.5 4.6-6 6.1s-5.4 2.2-8.5 2.2-6-.8-8.5-2.2-4.6-3.5-6-6.1zm24.7-2.2c1-1.8 1.6-3.9 1.6-6.3s-.5-4.4-1.6-6.3c-1-1.8-2.4-3.3-4.2-4.3s-3.8-1.6-5.9-1.6-4.1.5-5.9 1.6-3.2 2.5-4.2 4.3c-1 1.8-1.6 3.9-1.6 6.3s.5 4.4 1.6 6.3 2.4 3.3 4.2 4.3c1.8 1 3.8 1.6 5.9 1.6s4.1-.5 5.9-1.6c1.8-1 3.2-2.5 4.2-4.3zm18.9-22.4l9.4 24.5h.3l9.4-24.5h6.7v32.2h-4.9v-18.2l.3-5.7h-.3l-9.4 23.9h-3.9l-9.4-23.9h-.3l.3 5.7v18.2h-4.9v-32.2h6.7zm47.8 0l12.2 32.2h-5.4l-3-8.3h-13.1l-3 8.3h-5.4l12.1-32.2h5.6zm-1.4 9.5l-1.2-3.7h-.3l-1.2 3.7-3.6 9.9h9.9l-3.6-9.9zm12.6-4.8v-4.7h23v4.7h-9v27.5h-4.9v-27.5h-9zm32.7-4.7v32.2h-5v-32.2h5zm7.4 24.6c-1.5-2.6-2.2-5.4-2.2-8.5s.7-5.9 2.2-8.5c1.5-2.6 3.5-4.6 6-6.1s5.4-2.2 8.5-2.2 5.9.8 8.5 2.2 4.5 3.5 6 6.1c1.5 2.6 2.2 5.4 2.2 8.5s-.7 5.9-2.2 8.5c-1.5 2.6-3.5 4.6-6 6.1s-5.4 2.2-8.5 2.2-6-.8-8.5-2.2-4.6-3.5-6-6.1zm24.6-2.2c1-1.8 1.6-3.9 1.6-6.3s-.5-4.4-1.6-6.3c-1-1.8-2.4-3.3-4.2-4.3s-3.8-1.6-5.9-1.6-4.1.5-5.9 1.6-3.2 2.5-4.2 4.3c-1 1.8-1.6 3.9-1.6 6.3s.5 4.4 1.6 6.3 2.4 3.3 4.2 4.3c1.8 1 3.8 1.6 5.9 1.6s4.1-.5 5.9-1.6c1.8-1 3.2-2.5 4.2-4.3zm18.1-22.4l14.6 23.7h.3l-.3-6.2v-17.5h4.9v32.2h-5.1L542.2 278h-.3l.3 6.2v18.7h-4.9v-32.2h5.8z" />
        </ClipPath>
      </Defs>
      <Path d="M81.1 188.8c23.1 13 26.2 40.3 29 50.9 85.3-28.2 199.4-130.7 276.1-39-78.1-107.7-182-2.5-247.9 12.7-13.3 1.7-28.7-12.4-57.1-24.6zm91.4 19.1c140 56.3 264.2-57.1 408.3 34.2-100.3-97.9-280.2.3-392.4-47.8l-15.9 13.5zm20.1 15.3c161.8 42.4 222.2-35.1 294.3 2.3-70-24.4-127.8 65.7-294.3-2.3zm-72.8 47.5l7.3 21.4 1.1 3.6h.3l1.2-3.6 7.6-21.4h5.4L131 302.9h-5.1l-11.4-32.2h5.4zm24.7 4.7v-4.7h23v4.7h-9v27.5h-5v-27.5h-9zm47.3-4.7v4.7h-14.6v9.1h13.1v4.7h-13.1v9.1h14.6v4.7h-19.5v-32.2h19.5zm5.9 24.7c-1.5-2.6-2.2-5.4-2.2-8.5s.7-6 2.2-8.5c1.5-2.6 3.5-4.6 6.1-6.1s5.4-2.2 8.5-2.2c4.8 0 8.8 1.8 11.9 5.3l-3.5 3.4c-1.1-1.3-2.3-2.3-3.7-3-1.4-.7-2.9-1-4.7-1s-4.2.5-6 1.5c-1.8 1-3.2 2.4-4.3 4.3s-1.6 4-1.6 6.4.5 4.5 1.6 6.4c1 1.8 2.5 3.3 4.3 4.3 1.8 1 3.8 1.5 6 1.5 3.7 0 6.7-1.5 9.2-4.5l3.6 3.4c-1.6 1.8-3.4 3.3-5.6 4.3-2.2 1-4.6 1.5-7.2 1.5s-6-.7-8.5-2.2c-2.6-1.5-4.6-3.5-6.1-6.1zm37.9-24.7v13.4h15.1v-13.4h4.9v32.2h-4.9v-14.1h-15.1v14.1h-4.9v-32.2h4.9zm51.2 0l12.2 32.2h-5.4l-3-8.3h-13.1l-3 8.3h-5.4l12.1-32.2h5.6zm-1.4 9.5l-1.2-3.7h-.3l-1.2 3.7-3.6 9.9h9.9l-3.6-9.9zm18.3 17.2c-1-2-1.5-4.3-1.5-6.9v-19.8h4.9v20.1c0 2.4.6 4.4 1.9 5.9 1.3 1.5 3 2.2 5.4 2.2s4.1-.8 5.4-2.2c1.3-1.5 1.9-3.5 1.9-5.9v-20.1h4.9v19.8c0 2.6-.5 4.9-1.5 6.8-1 2-2.4 3.5-4.2 4.6-1.8 1.1-4 1.6-6.5 1.6s-4.5-.5-6.4-1.6-3.3-2.6-4.3-4.6zm27-22v-4.7h23v4.7h-9v27.5h-4.9v-27.5h-9zm27 19.9c-1.5-2.6-2.2-5.4-2.2-8.5s.7-5.9 2.2-8.5c1.5-2.6 3.5-4.6 6-6.1s5.4-2.2 8.5-2.2 5.9.8 8.5 2.2c2.5 1.5 4.5 3.5 6 6.1 1.5 2.6 2.2 5.4 2.2 8.5s-.7 5.9-2.2 8.5c-1.5 2.6-3.5 4.6-6 6.1s-5.4 2.2-8.5 2.2-6-.8-8.5-2.2-4.6-3.5-6-6.1zm24.7-2.2c1-1.8 1.6-3.9 1.6-6.3s-.5-4.4-1.6-6.3c-1-1.8-2.4-3.3-4.2-4.3s-3.8-1.6-5.9-1.6-4.1.5-5.9 1.6-3.2 2.5-4.2 4.3c-1 1.8-1.6 3.9-1.6 6.3s.5 4.4 1.6 6.3 2.4 3.3 4.2 4.3c1.8 1 3.8 1.6 5.9 1.6s4.1-.5 5.9-1.6c1.8-1 3.2-2.5 4.2-4.3zm18.9-22.4l9.4 24.5h.3l9.4-24.5h6.7v32.2h-4.9v-18.2l.3-5.7h-.3l-9.4 23.9h-3.9l-9.4-23.9h-.3l.3 5.7v18.2h-4.9v-32.2h6.7zm47.8 0l12.2 32.2h-5.4l-3-8.3h-13.1l-3 8.3h-5.4l12.1-32.2h5.6zm-1.4 9.5l-1.2-3.7h-.3l-1.2 3.7-3.6 9.9h9.9l-3.6-9.9zm12.6-4.8v-4.7h23v4.7h-9v27.5h-4.9v-27.5h-9zm32.7-4.7v32.2h-5v-32.2h5zm7.4 24.6c-1.5-2.6-2.2-5.4-2.2-8.5s.7-5.9 2.2-8.5c1.5-2.6 3.5-4.6 6-6.1s5.4-2.2 8.5-2.2 5.9.8 8.5 2.2 4.5 3.5 6 6.1c1.5 2.6 2.2 5.4 2.2 8.5s-.7 5.9-2.2 8.5c-1.5 2.6-3.5 4.6-6 6.1s-5.4 2.2-8.5 2.2-6-.8-8.5-2.2-4.6-3.5-6-6.1zm24.6-2.2c1-1.8 1.6-3.9 1.6-6.3s-.5-4.4-1.6-6.3c-1-1.8-2.4-3.3-4.2-4.3s-3.8-1.6-5.9-1.6-4.1.5-5.9 1.6-3.2 2.5-4.2 4.3c-1 1.8-1.6 3.9-1.6 6.3s.5 4.4 1.6 6.3 2.4 3.3 4.2 4.3c1.8 1 3.8 1.6 5.9 1.6s4.1-.5 5.9-1.6c1.8-1 3.2-2.5 4.2-4.3zm18.1-22.4l14.6 23.7h.3l-.3-6.2v-17.5h4.9v32.2h-5.1L542.2 278h-.3l.3 6.2v18.7h-4.9v-32.2h5.8z" />
    </Svg>
  );
}

export default LogoSvg;
