# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the App
- `npm run start` - Start Expo development server
- `npm run start:customer` - Start app in customer mode
- `npm run start:technician` - Start app in technician mode
- `npm run start:cross:customer` - Start customer mode with cross-env (Windows compatible)
- `npm run start:cross:technician` - Start technician mode with cross-env (Windows compatible)
- `npm run ios` - Run on iOS simulator
- `npm run android` - Run on Android emulator
- `npm run web` - Run on web

### Code Quality
- `npm run lint` - Run ESLint and Prettier checks
- `npm run format` - Fix ESLint issues and format code with Prettier

### Build
- `npm run prebuild` - Generate native code for iOS/Android

## Project Architecture

### App Structure
This is an Expo React Native app using:
- **expo-router** for file-based routing with typed routes
- **GlueStack UI** as the primary component library
- **NativeWind** for Tailwind CSS styling
- **Supabase** for backend services
- **i18next** for internationalization (Vietnamese/English)

### Dual App Configuration
The app supports two modes via `EXPO_PUBLIC_APP_TYPE` environment variable:
- `customer` (default) - Customer-facing interface
- `technician` - Technician-facing interface

Use `useAppType()` hook to access current app type and conditional rendering helpers.

### Key Directories
- `app/` - File-based routing (expo-router)
  - `auth/` - Authentication screens
  - `customer/` - Customer-specific screens
  - `technician/` - Technician-specific screens
  - `main/` - Shared main app screens
- `components/` - Reusable UI components
  - `ui/` - GlueStack UI component wrappers
  - `chat/` - Chat-related components
  - `notification/` - Notification system components
- `core/` - Core application logic
  - `contexts/` - React contexts (AppType, Auth, Notifications)
  - `i18n/` - Internationalization setup
- `services/` - Business logic and API integrations
- `lib/` - External service configurations (Supabase)
- `types/` - TypeScript type definitions

### AI Integration
The app includes Google Gemini AI integration for chat functionality. Set `EXPO_PUBLIC_GEMINI_API_KEY` in environment variables.

### Location Services
Location tracking is implemented using expo-location and expo-task-manager for background location updates.

### Required Environment Variables
```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_APP_URL=your_app_url
EXPO_PUBLIC_GEMINI_AI_KEY=your_gemini_api_key
EXPO_PUBLIC_APP_TYPE=customer|technician
```

### Styling
- Primary UI framework: GlueStack UI
- Styling: NativeWind (Tailwind CSS for React Native)
- Global styles in `global.css`
- Component-specific styles using Tailwind classes

### Navigation
- File-based routing with expo-router
- Typed routes enabled via experiments.typedRoutes
- Layout components handle tab navigation and authentication flow