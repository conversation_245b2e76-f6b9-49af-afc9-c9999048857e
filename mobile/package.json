{"name": "vtech", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "start": "expo start", "start:customer": "EXPO_PUBLIC_APP_TYPE=customer expo start", "start:technician": "EXPO_PUBLIC_APP_TYPE=technician expo start", "start:cross:customer": "cross-env EXPO_PUBLIC_APP_TYPE=customer expo start", "start:cross:technician": "cross-env EXPO_PUBLIC_APP_TYPE=technician expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.0.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.52", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.26", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@google/generative-ai": "^0.24.0", "@gorhom/bottom-sheet": "^5.0.0-alpha.11", "@gorhom/portal": "^1.0.14", "@hookform/resolvers": "^4.1.3", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.0.3", "@stripe/stripe-react-native": "^0.50.1", "@supabase/supabase-js": "^2.49.4", "@types/react-native-uuid": "^2.0.0", "axios": "^1.11.0", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "expo": "^53.0.13", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-device": "^7.1.4", "expo-document-picker": "~13.1.6", "expo-image": "^2.0.7", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-notifications": "^0.31.4", "expo-router": "~5.1.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-task-manager": "^13.1.5", "expo-web-browser": "~14.2.0", "i18next": "^23.7.20", "lucide-react-native": "^0.483.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^14.0.1", "react-native": "0.79.5", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-web": "^0.20.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9.33.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "cross-env": "^7.0.3", "eslint": "^9.33.0", "eslint-config-universe": "^15.0.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jscodeshift": "0.15.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1"}, "private": true}