import { TouchableOpacity } from 'react-native';

import { Text } from '@/components/ui/text';

type TabType = 'USER' | 'LOCATIONS' | 'SECURITY';

type TabButtonProps = {
  title: TabType;
  isActive: boolean;
  onPress: () => void;
};

export const TabButton = ({ title, isActive, onPress }: TabButtonProps) => (
  <TouchableOpacity
    onPress={onPress}
    className={`flex-1 border-b-2 py-3 ${isActive ? 'border-blue-500' : 'border-gray-200'}`}>
    <Text
      className={`text-center text-base ${
        isActive ? 'font-medium text-gray-800' : 'text-gray-500'
      }`}>
      {title}
    </Text>
  </TouchableOpacity>
);

// Export TabType for reuse
export type { TabType };
