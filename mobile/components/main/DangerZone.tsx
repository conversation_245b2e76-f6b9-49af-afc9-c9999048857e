import { Trash2 } from 'lucide-react-native';
import { Alert, View } from 'react-native';

import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useAuth } from '@/core/contexts/AuthContext';

export const DangerZone = () => {
  const { deleteAccount } = useAuth();

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAccount();
            } catch {
              Alert.alert('Error', 'Failed to delete account');
            }
          },
        },
      ]
    );
  };

  return (
    <View className="rounded-lg border border-red-200 bg-red-50 p-4">
      <View className="mb-2 flex-row items-center">
        <Trash2 size={20} color="#dc2626" />
        <Text className="ml-3 text-lg font-medium text-red-700">Danger Zone</Text>
      </View>
      <Text className="mb-3 text-sm text-red-600">
        Once you delete your account, there is no going back. Please be certain.
      </Text>
      <Button onPress={handleDeleteAccount} className="w-full bg-red-500">
        <ButtonText className="font-medium text-white">Delete Account</ButtonText>
      </Button>
    </View>
  );
};
