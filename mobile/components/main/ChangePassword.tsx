import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { ChevronDown, ChevronUp, Lock } from 'lucide-react-native';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ActivityIndicator, Alert, Animated, TouchableOpacity, View } from 'react-native';
import { z } from 'zod';

import { InputGroup } from '@/components/form/InputGroup';
import { HStack, VStack } from '@/components/ui';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useAuth } from '@/core/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Password validation schema
const changePasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

export const ChangePassword = () => {
  const [isPasswordExpanded, setIsPasswordExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const animationHeight = useState(new Animated.Value(0))[0];
  const router = useRouter();
  const { logout } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  });

  const togglePasswordSection = () => {
    if (isPasswordExpanded) {
      Animated.timing(animationHeight, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start(() => setIsPasswordExpanded(false));
    } else {
      setIsPasswordExpanded(true);
      Animated.timing(animationHeight, {
        toValue: 500,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  };

  const onSubmitPassword = async (data: ChangePasswordFormData) => {
    try {
      setIsLoading(true);

      // Update password directly without checking current password
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword,
      });

      if (error) throw error;

      // Reset form and close section first
      reset();
      togglePasswordSection();

      // Show success message and sign out
      Alert.alert(
        'Success',
        'Your password has been changed successfully. You will be logged out for security reasons.',
        [
          {
            text: 'OK',
            onPress: async () => {
              try {
                // Use logout from AuthContext which handles everything
                await logout();
                // The AuthContext will handle navigation to login screen
              } catch (signOutError) {
                console.error('Error signing out:', signOutError);
                // If logout fails, manually navigate
                router.replace('/auth/login');
              }
            },
          },
        ],
        { cancelable: false }
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to change password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View
      className={`rounded-lg border border-blue-200 ${
        isPasswordExpanded ? 'bg-blue-50/30' : 'bg-white'
      }`}>
      <TouchableOpacity onPress={togglePasswordSection} className="p-4" activeOpacity={0.7}>
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <View className="mb-2 flex-row items-center">
              <Lock size={20} color="#3b82f6" />
              <Text className="ml-3 text-lg font-medium text-blue-600">Password</Text>
            </View>
            <Text className="text-sm text-blue-500">
              Update your password regularly to keep your account secure
            </Text>
          </View>
          {isPasswordExpanded ? (
            <ChevronUp size={24} color="#3b82f6" />
          ) : (
            <ChevronDown size={24} color="#3b82f6" />
          )}
        </View>
      </TouchableOpacity>

      {/* Expandable Password Form */}
      {isPasswordExpanded && (
        <Animated.View
          style={{ maxHeight: animationHeight, overflow: 'hidden' }}
          className="border-blue-200">
          <VStack space="md" className="p-4">
            {/* Password Requirements */}
            <View className="rounded-lg border border-blue-200 bg-blue-100/50 p-3">
              <Text className="mb-2 text-xs font-semibold text-blue-900">
                Password Requirements:
              </Text>
              <VStack space="xs">
                <Text className="text-xs text-blue-700">• At least 8 characters</Text>
                <Text className="text-xs text-blue-700">• One uppercase letter</Text>
                <Text className="text-xs text-blue-700">• One lowercase letter</Text>
                <Text className="text-xs text-blue-700">• One number</Text>
                <Text className="text-xs text-blue-700">• One special character</Text>
              </VStack>
            </View>

            {/* Form Fields */}
            <InputGroup
              control={control}
              name="newPassword"
              label="New Password"
              placeholder="Enter new password"
              type="password"
              leftIcon={Lock}
              error={errors.newPassword}
            />

            <InputGroup
              control={control}
              name="confirmPassword"
              label="Confirm New Password"
              placeholder="Confirm new password"
              type="password"
              leftIcon={Lock}
              error={errors.confirmPassword}
            />

            {/* Buttons */}
            <HStack space="sm" className="mt-2">
              <Button
                onPress={handleSubmit(onSubmitPassword)}
                disabled={isLoading}
                className={`flex-1 ${isLoading ? 'bg-gray-400' : 'bg-blue-500'}`}>
                {isLoading ? (
                  <HStack space="xs" className="items-center">
                    <ActivityIndicator size="small" color="white" />
                    <ButtonText className="text-sm font-medium text-white">Changing...</ButtonText>
                  </HStack>
                ) : (
                  <ButtonText className="text-sm font-medium text-white">
                    Change Password
                  </ButtonText>
                )}
              </Button>
              <Button
                onPress={() => {
                  reset();
                  togglePasswordSection();
                }}
                disabled={isLoading}
                className="flex-1 border border-gray-300 bg-white">
                <ButtonText className="text-sm font-medium text-gray-700">Cancel</ButtonText>
              </Button>
            </HStack>
          </VStack>
        </Animated.View>
      )}
    </View>
  );
};
