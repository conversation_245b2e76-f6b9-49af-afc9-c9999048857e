import { FontAwesome5 } from '@expo/vector-icons';
import React, { useState } from 'react';
import { View, TouchableOpacity, Alert, TextInput, ScrollView } from 'react-native';

import { Text } from '@/components/ui/text';

export const PaymentsTab = () => {
  const [amount, setAmount] = useState('');
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);

  const paymentOptions = [
    { id: 'google_pay', name: 'Google Pay', icon: 'google', iconProvider: FontAwesome5 },
    { id: 'apple_pay', name: 'Apple Pay', icon: 'apple', iconProvider: FontAwesome5 },
    { id: 'stripe', name: 'Stripe', icon: 'credit-card', iconProvider: FontAwesome5 },
    { id: 'paypal', name: 'PayPal', icon: 'paypal', iconProvider: FontAwesome5 },
  ];

  const handlePaymentSelection = (optionId: string) => {
    setSelectedPayment(optionId);
  };

  const handlePaymentSubmit = () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Invalid amount', 'Please enter a valid amount.');
      return;
    }

    if (!selectedPayment) {
      Alert.alert('No payment method', 'Please select a payment method.');
      return;
    }

    // In a real app, this is where you would integrate with the payment provider
    Alert.alert(
      'Payment Processing',
      `Processing $${amount} payment via ${
        paymentOptions.find((option) => option.id === selectedPayment)?.name
      }.`,
      [
        {
          text: 'OK',
          onPress: () => {
            setAmount('');
            setSelectedPayment(null);
          },
        },
      ]
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 pb-3 pt-4 shadow-sm">
          <Text className="text-2xl font-bold text-gray-900">Payment</Text>
          <Text className="mt-1 text-sm text-gray-500">
            Manage payment methods and transactions
          </Text>
        </View>

        {/* Payment Content */}
        <View className="px-4 py-4">
          <View className="mb-6">
            <Text className="mb-2 text-base font-medium text-gray-700">Amount (USD)</Text>
            <TextInput
              className="h-12 rounded-lg border border-gray-300 bg-white px-4"
              keyboardType="numeric"
              placeholder="Enter amount"
              value={amount}
              onChangeText={setAmount}
            />
          </View>

          <Text className="mb-4 text-base font-medium text-gray-700">Select Payment Method</Text>

          {paymentOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              className={`mb-4 flex-row items-center rounded-lg border p-4 ${
                selectedPayment === option.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
              onPress={() => handlePaymentSelection(option.id)}>
              <View className="mr-4 h-6 w-6 items-center justify-center">
                {option.iconProvider && (
                  <option.iconProvider
                    name={option.icon}
                    size={20}
                    color={selectedPayment === option.id ? '#3b82f6' : '#6b7280'}
                  />
                )}
              </View>
              <Text
                className={`text-base ${
                  selectedPayment === option.id ? 'font-semibold text-blue-600' : 'text-gray-700'
                }`}>
                {option.name}
              </Text>
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            className={`mt-6 rounded-lg p-4 ${
              selectedPayment && amount ? 'bg-blue-500' : 'bg-gray-300'
            }`}
            onPress={handlePaymentSubmit}
            disabled={!selectedPayment || !amount}>
            <Text className="text-center text-base font-medium text-white">Pay Now</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};
