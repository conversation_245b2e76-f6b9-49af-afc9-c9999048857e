import { Ionicons } from '@expo/vector-icons';
import React, { useState, useEffect } from 'react';
import { Text, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';

import { CategoryItem } from './CategoryItem';
import { ServiceItem } from './ServiceItem';

import { Box, HStack } from '@/components/ui';
import {
  getActiveServices,
  getActiveCategories,
  Service,
  Category,
} from '@/services/service-service';

// Interface for component props
interface ServiceListProps {
  initialServices?: Service[];
  initialCategories?: Category[];
}

// Helper function to get the appropriate Ionicons name
const getCategoryIconName = (icon: string | null): keyof typeof Ionicons.glyphMap => {
  if (!icon) return 'construct-outline';

  // Map emoji icons to valid Ionicons names
  switch (icon) {
    case '🔧':
      return 'construct-outline';
    case '🛑':
      return 'stop-circle-outline';
    case '⚙️':
      return 'settings-outline';
    case '🛞':
      return 'car-outline';
    case '⚡':
      return 'flash-outline';
    default:
      return 'construct-outline';
  }
};

const ServiceList: React.FC<ServiceListProps> = ({ initialServices, initialCategories }) => {
  const [services, setServices] = useState<Service[]>(initialServices || []);
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialServices || !initialCategories);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Fetch services and categories on component mount if not provided
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch categories and services in parallel
        const [servicesData, categoriesData] = await Promise.all([
          getActiveServices(),
          getActiveCategories(),
        ]);

        setServices(servicesData);
        setCategories(categoriesData);
        setError(null);
      } catch (err) {
        console.error('Error fetching service data:', err);
        setError('Failed to load services. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if data wasn't provided as props
    if (!initialServices || !initialCategories) {
      fetchData();
    }
  }, [initialServices, initialCategories]);

  // If loading, show a loading indicator
  if (loading) {
    return (
      <Box className="items-center justify-center py-8">
        <ActivityIndicator size="large" color="#f59e0b" />
        <Text className="mt-2 text-gray-600">Loading services...</Text>
      </Box>
    );
  }

  // If error occurred, show error message
  if (error) {
    return (
      <Box className="items-center justify-center py-8">
        <Ionicons name="alert-circle-outline" size={32} color="#ef4444" />
        <Text className="mt-2 text-gray-800">{error}</Text>
        <TouchableOpacity
          className="mt-4 rounded-lg bg-blue-500 px-4 py-2"
          onPress={() => {
            setLoading(true);
            Promise.all([getActiveServices(), getActiveCategories()])
              .then(([servicesData, categoriesData]) => {
                setServices(servicesData);
                setCategories(categoriesData);
                setError(null);
              })
              .catch(() => {
                setError('Failed to load services. Please try again.');
              })
              .finally(() => setLoading(false));
          }}>
          <Text className="font-medium text-white">Try Again</Text>
        </TouchableOpacity>
      </Box>
    );
  }

  // Group services by category
  const servicesByCategory = categories.reduce(
    (acc, category) => {
      // Filter services for this category
      const categoryServices = services.filter(
        (service) => service.category_id === category.id && service.is_active
      );

      // Only add categories that have active services
      if (categoryServices.length > 0) {
        acc.push({
          category,
          services: categoryServices,
        });
      }
      return acc;
    },
    [] as { category: Category; services: Service[] }[]
  );

  const handleServicePress = (service: Service) => {
    // Navigate to service detail page
    // router.push(`/main/service/${service.id}`);
    console.log('Service pressed:', service.name);
  };

  // If no services to display
  if (servicesByCategory.length === 0) {
    return (
      <Box className="items-center justify-center py-8">
        <Ionicons name="cart-outline" size={32} color="#9ca3af" />
        <Text className="mt-2 text-center text-gray-500">No services available</Text>
      </Box>
    );
  }

  // Filter services based on selected category
  const filteredServices = selectedCategory
    ? servicesByCategory.filter((item) => item.category.id === selectedCategory)
    : servicesByCategory;

  return (
    <Box className="mb-6">
      {/* Section Header */}
      <HStack className="mb-4 items-center justify-between">
        <Text className="text-xl font-bold text-gray-900">Our Services</Text>
      </HStack>

      {/* Categories Horizontal Scroll */}
      <Box className="mb-5">
        <Text className="mb-3 text-sm font-semibold text-gray-700">Categories</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingRight: 20 }}
          className="-mx-4 px-4">
          {/* All Services Category */}
          <CategoryItem
            name="All"
            icon="apps"
            isActive={!selectedCategory}
            onPress={() => setSelectedCategory(null)}
          />
          {categories
            .filter((category) => category.is_active)
            .map((category) => (
              <CategoryItem
                key={category.id}
                name={category.name}
                icon={getCategoryIconName(category.icon)}
                isActive={selectedCategory === category.id}
                onPress={() => setSelectedCategory(category.id)}
              />
            ))}
        </ScrollView>
      </Box>

      {/* Services Grid */}
      <Box>
        <Text className="mb-3 text-sm font-semibold text-gray-700">
          {selectedCategory
            ? `${categories.find((c) => c.id === selectedCategory)?.name} Services`
            : 'Popular Services'}
        </Text>

        {filteredServices.length === 0 ? (
          <Box className="items-center justify-center rounded-2xl bg-gray-900 py-8">
            <Ionicons name="construct-outline" size={32} color="#f59e0b" />
            <Text className="mt-2 text-center text-gray-400">No services in this category</Text>
          </Box>
        ) : (
          <Box className="-mx-2 flex-row flex-wrap">
            {filteredServices.map(({ category, services }) =>
              services.map((service) => (
                <Box key={service.id} className="mb-4 w-1/2 px-2">
                  <ServiceItem
                    name={service.name}
                    description={service.description || undefined}
                    price={service.price}
                    duration={service.duration}
                    categoryName={category.name}
                    onPress={() => handleServicePress(service)}
                  />
                </Box>
              ))
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ServiceList;
