import { Ionicons } from '@expo/vector-icons';
// import { useRouter } from 'expo-router'; // unused
import React, { useRef, useState, useEffect } from 'react';
import { Text, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';

import { Box, HStack, VStack } from '@/components/ui';
import {
  getActiveServices,
  getActiveCategories,
  Service,
  Category,
} from '@/services/service-service';

// Interface for component props
interface ServiceListProps {
  initialServices?: Service[];
  initialCategories?: Category[];
}

// Helper function to get the appropriate Ionicons name
const getCategoryIconName = (icon: string | null): keyof typeof Ionicons.glyphMap => {
  if (!icon) return 'construct-outline';

  // Map emoji icons to valid Ionicons names
  switch (icon) {
    case '🔧':
      return 'construct-outline';
    case '🛑':
      return 'stop-circle-outline';
    case '⚙️':
      return 'settings-outline';
    case '🛞':
      return 'car-outline';
    case '⚡':
      return 'flash-outline';
    default:
      return 'construct-outline';
  }
};

const ServiceList: React.FC<ServiceListProps> = ({ initialServices, initialCategories }) => {
  const [services, setServices] = useState<Service[]>(initialServices || []);
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialServices || !initialCategories);
  const [error, setError] = useState<string | null>(null);

  // Fetch services and categories on component mount if not provided
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch categories and services in parallel
        const [servicesData, categoriesData] = await Promise.all([
          getActiveServices(),
          getActiveCategories(),
        ]);

        setServices(servicesData);
        setCategories(categoriesData);
        setError(null);
      } catch (err) {
        console.error('Error fetching service data:', err);
        setError('Failed to load services. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if data wasn't provided as props
    if (!initialServices || !initialCategories) {
      fetchData();
    }
  }, [initialServices, initialCategories]);
  // const _router = useRouter(); // unused
  const categoryScrollRef = useRef<ScrollView>(null);

  // If loading, show a loading indicator
  if (loading) {
    return (
      <Box className="items-center justify-center py-8">
        <ActivityIndicator size="large" color="#f59e0b" />
        <Text className="mt-2 text-gray-600">Loading services...</Text>
      </Box>
    );
  }

  // If error occurred, show error message
  if (error) {
    return (
      <Box className="items-center justify-center py-8">
        <Ionicons name="alert-circle-outline" size={32} color="#ef4444" />
        <Text className="mt-2 text-gray-800">{error}</Text>
        <TouchableOpacity
          className="mt-4 rounded-lg bg-blue-500 px-4 py-2"
          onPress={() => {
            setLoading(true);
            Promise.all([getActiveServices(), getActiveCategories()])
              .then(([servicesData, categoriesData]) => {
                setServices(servicesData);
                setCategories(categoriesData);
                setError(null);
              })
              .catch(() => {
                setError('Failed to load services. Please try again.');
              })
              .finally(() => setLoading(false));
          }}>
          <Text className="font-medium text-white">Try Again</Text>
        </TouchableOpacity>
      </Box>
    );
  }

  // Group services by category
  const servicesByCategory = categories.reduce(
    (acc, category) => {
      // Filter services for this category
      const categoryServices = services.filter(
        (service) => service.category_id === category.id && service.is_active
      );

      // Only add categories that have active services
      if (categoryServices.length > 0) {
        acc.push({
          category,
          services: categoryServices,
        });
      }
      return acc;
    },
    [] as { category: Category; services: Service[] }[]
  );

  const handleServicePress = (service: Service) => {
    // Navigate to service detail page
    // router.push(`/main/service/${service.id}`);
    console.log('Service pressed:', service.name);
  };

  const handleCategoryPress = (categoryId: string) => {
    // Scroll to category section
    const index = servicesByCategory.findIndex((item) => item.category.id === categoryId);
    if (index !== -1 && categoryScrollRef.current) {
      categoryScrollRef.current.scrollTo({ x: 0, y: index * 300, animated: true });
    }
  };

  // Format price to display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  // If no services to display
  if (servicesByCategory.length === 0) {
    return (
      <Box className="items-center justify-center py-8">
        <Ionicons name="cart-outline" size={32} color="#9ca3af" />
        <Text className="mt-2 text-center text-gray-500">No services available</Text>
      </Box>
    );
  }

  return (
    <Box className="mb-6">
      <Box className="mb-3">
        <HStack className="mb-3 items-center justify-between">
          <Text className="text-lg font-bold text-gray-900">Services</Text>
          <TouchableOpacity className="rounded-md bg-blue-50 px-2 py-1">
            <Text className="text-xs font-medium text-blue-500">See All</Text>
          </TouchableOpacity>
        </HStack>

        {/* Categories Horizontal Scroll */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingRight: 20 }}
          className="mb-4">
          {categories
            .filter((category) => category.is_active)
            .map((category) => (
              <TouchableOpacity
                key={category.id}
                onPress={() => handleCategoryPress(category.id)}
                className="mr-4 items-center">
                <Box className="h-14 w-14 items-center justify-center rounded-full border border-gray-100 bg-white shadow-sm">
                  <Box className="h-8 w-8 items-center justify-center rounded-full bg-yellow-50">
                    <Ionicons name={getCategoryIconName(category.icon)} size={16} color="#f59e0b" />
                  </Box>
                </Box>
                <Text
                  className="mt-2 text-center text-xs font-medium text-gray-700"
                  numberOfLines={1}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
        </ScrollView>
      </Box>

      {/* Services by Category */}
      <ScrollView ref={categoryScrollRef} showsVerticalScrollIndicator={false}>
        {servicesByCategory.map(({ category, services }) => (
          <Box key={category.id} className="mb-5">
            <HStack className="mb-3 items-center">
              <Box className="h-7 w-7 items-center justify-center rounded-full border border-gray-100 bg-gray-50">
                <Ionicons name={getCategoryIconName(category.icon)} size={15} color="#f59e0b" />
              </Box>
              <Text className="ml-2 text-sm font-semibold text-gray-800">{category.name}</Text>
              <Text className="ml-1 text-xs text-gray-400">({services.length})</Text>
            </HStack>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingRight: 16 }}
              className="-mx-2 px-2">
              {services.map((service) => (
                <TouchableOpacity
                  key={service.id}
                  onPress={() => handleServicePress(service)}
                  className="mr-3 w-56 rounded-xl border border-gray-200"
                  activeOpacity={0.7}>
                  <Box className="overflow-hidden rounded-xl bg-white shadow-sm">
                    {/* Card content wrapper */}
                    <Box className="p-3">
                      {/* Category indicator - subtle dot */}
                      <HStack className="mb-1 items-center">
                        <Box className="mr-1.5 h-2 w-2 rounded-full bg-yellow-400" />
                        <Text className="text-xs font-medium text-gray-500" numberOfLines={1}>
                          {category.name}
                        </Text>
                      </HStack>

                      {/* Service Title */}
                      <Text className="text-base font-semibold text-gray-900" numberOfLines={1}>
                        {service.name}
                      </Text>

                      {/* Service Description */}
                      {service.description ? (
                        <Text className="mb-2 mt-0.5 text-xs text-gray-500" numberOfLines={2}>
                          {service.description}
                        </Text>
                      ) : (
                        <Box className="mb-2" />
                      )}

                      {/* Service Details */}
                      <HStack className="items-center justify-between border-t border-gray-100 pt-2">
                        {/* Price */}
                        <VStack>
                          <Text className="text-xs text-gray-500">Price</Text>
                          <Text className="text-sm font-bold text-gray-900">
                            {formatPrice(service.price)}
                          </Text>
                        </VStack>

                        {/* Duration in a pill */}
                        <Box className="rounded-full border border-gray-100 bg-gray-50 px-2.5 py-1">
                          <HStack className="items-center">
                            <Ionicons name="time-outline" size={12} color="#6b7280" />
                            <Text className="ml-1 text-xs font-medium text-gray-600">
                              {service.duration} min
                            </Text>
                          </HStack>
                        </Box>
                      </HStack>
                    </Box>
                  </Box>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Box>
        ))}
      </ScrollView>
    </Box>
  );
};

export default ServiceList;
