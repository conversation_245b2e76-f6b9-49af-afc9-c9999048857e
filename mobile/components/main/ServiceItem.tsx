import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box, HStack, VStack, Text } from '@/components/ui';

interface ServiceItemProps {
  name: string;
  description?: string;
  price: number;
  duration: number;
  categoryName: string;
  onPress?: () => void;
}

export const ServiceItem: React.FC<ServiceItemProps> = ({
  name,
  description,
  price,
  duration,
  categoryName,
  onPress,
}) => {
  const formatPrice = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  return (
    <TouchableOpacity onPress={onPress} className="flex-1" activeOpacity={1}>
      <Box className="overflow-hidden rounded-2xl bg-gray-900 p-4" style={{ minHeight: 180 }}>
        {/* Header with category */}
        <HStack className="mb-3 items-center justify-between">
          <Box className="rounded-full bg-gray-800 px-3 py-1">
            <Text className="text-xs font-medium text-yellow-400">{categoryName}</Text>
          </Box>
          {/* <Ionicons name="arrow-forward" size={16} color="#f59e0b" /> */}
        </HStack>

        {/* Service Title */}
        <Text className="mb-1 text-base font-bold text-white" numberOfLines={1}>
          {name}
        </Text>

        {/* Service Description */}
        <Text className="mb-3 text-xs text-gray-400" numberOfLines={2} style={{ minHeight: 32 }}>
          {description || 'Professional service with quality guaranteed'}
        </Text>

        {/* Bottom Details */}
        <HStack className="items-center justify-between border-t border-gray-800 pt-3">
          {/* Price */}
          <VStack>
            <Text className="text-xs text-gray-500">Starting at</Text>
            <Text className="text-lg font-bold text-yellow-400">{formatPrice(price)}</Text>
          </VStack>

          {/* Duration */}
          <Box className="rounded-full bg-gray-800 px-3 py-1.5">
            <HStack className="items-center">
              <Ionicons name="time" size={14} color="#f59e0b" />
              <Text className="ml-1.5 text-xs font-medium text-gray-300">{duration} min</Text>
            </HStack>
          </Box>
        </HStack>
      </Box>
    </TouchableOpacity>
  );
};
