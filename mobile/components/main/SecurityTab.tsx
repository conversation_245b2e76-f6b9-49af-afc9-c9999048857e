import { View, ScrollView } from 'react-native';

import { ChangePassword } from './ChangePassword';
import { DangerZone } from './DangerZone';

import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui';

export const SecurityTab = () => {
  return (
    <View className="flex-1">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 pb-3 pt-4 shadow-sm">
          <Text className="text-2xl font-bold text-gray-900">Security</Text>
          <Text className="mt-1 text-sm text-gray-500">
            Manage your account security and privacy
          </Text>
        </View>

        {/* Security Actions */}
        <VStack space="md" className="px-4 py-4">
          {/* Change Password Component */}
          <ChangePassword />

          {/* Danger Zone Component */}
          <DangerZone />
        </VStack>
      </ScrollView>
    </View>
  );
};
