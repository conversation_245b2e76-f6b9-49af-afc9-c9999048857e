import { Mail, Phone, User as UserIcon } from 'lucide-react-native';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { View, ScrollView, ActivityIndicator } from 'react-native';

import { InputGroup } from '@/components/form/InputGroup';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useAuth } from '@/core/contexts/AuthContext';
import { VStack } from '~/components/ui';
import { useNotification } from '@/services/toast-service';

// User form type
type UserFormValues = {
  name: string;
  email: string;
  phone: string;
};

export const UserTab = () => {
  const { user, updateUserInfo } = useAuth();
  const { showToast } = useNotification();
  const [isSaving, setIsSaving] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<UserFormValues>({
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
    },
  });

  const onSubmit = async (data: UserFormValues) => {
    if (!user) {
      showToast('Please login to update profile', 'error');
      return;
    }

    try {
      setIsSaving(true);
      await updateUserInfo(data.name, '', data.email, data.phone, user.address);

      // Show success notification
      showToast('Profile updated successfully!', 'success');
    } catch (error) {
      console.error('Error updating profile:', error);
      showToast('Failed to update profile. Please try again.', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <View className="flex-1">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 pb-3 pt-4 shadow-sm">
          <Text className="text-2xl font-bold text-gray-900">User Information</Text>
          <Text className="mt-1 text-sm text-gray-500">Manage your personal information</Text>
        </View>

        {/* Form Content */}
        <VStack space="md" className="px-4 py-4">
          <InputGroup
            control={control}
            name="name"
            label="Full Name"
            placeholder="Enter your full name"
            leftIcon={UserIcon}
            error={errors.name}
          />

          <InputGroup
            control={control}
            name="email"
            label="Email"
            placeholder="Enter email"
            leftIcon={Mail}
            keyboardType="email-address"
            error={errors.email}
          />

          <InputGroup
            control={control}
            name="phone"
            label="Phone"
            placeholder="Enter phone number"
            leftIcon={Phone}
            keyboardType="phone-pad"
            error={errors.phone}
          />
        </VStack>

        {/* Update Profile Button */}
        <View className="px-4 pb-4">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={isSaving}
            className={`w-full ${isSaving ? 'bg-gray-400' : 'bg-blue-500'}`}>
            {isSaving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <ButtonText className="font-medium text-white">Update Profile</ButtonText>
            )}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};
