import * as Location from 'expo-location';
import { MapPin, <PERSON>hair, Plus, Minus, Locate, Search } from 'lucide-react-native';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  View,
  Alert,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  Keyboard,
  Animated,
  Vibration,
  ScrollView,
} from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';

import { googlePlacesService } from '@/services/google-places';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui';
import { AddressInputGroup } from '@/components/form';
import { useAuth } from '@/core/contexts/AuthContext';
import { useNotification } from '@/services/toast-service';

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.005;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

interface LocationFormData {
  address: string;
}

export const LocationsTab = () => {
  const mapRef = useRef<MapView>(null);
  const { user, updateUserInfo } = useAuth();
  const { showToast } = useNotification();
  const isMapMovingRef = useRef(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const { control, setValue } = useForm<LocationFormData>({
    defaultValues: {
      address: user?.address || '',
    },
  });

  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  const [selectedLocation, setSelectedLocation] = useState<{
    address: string;
    latitude: number;
    longitude: number;
  } | null>(null);

  const [isSaving, setIsSaving] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const [isCurrentLocationActive, setIsCurrentLocationActive] = useState(false);
  const [currentZoom, setCurrentZoom] = useState({
    latitudeDelta: LATITUDE_DELTA,
    longitudeDelta: LONGITUDE_DELTA,
  });

  // Animate UI elements on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Load user's saved address on mount
  useEffect(() => {
    if (user?.address) {
      setValue('address', user.address);
      geocodeAddress(user.address);
    }
    getCurrentLocation();
  }, [user, setValue]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'Please enable location permission to use this feature. You can change this in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Location.requestForegroundPermissionsAsync() },
          ]
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setCurrentLocation(coords);

      // If no saved address, use current location
      if (!user?.address) {
        await getAddressFromCoordinates(coords);
      }
    } catch {
      Alert.alert('Unable to get location', 'Please check your location settings and try again.');
    }
  };

  const geocodeAddress = async (address: string) => {
    try {
      // Try Google Places autocomplete first to get better results
      const predictions = await googlePlacesService.autocomplete({
        input: address,
        types: 'address',
      });

      if (predictions && predictions.length > 0) {
        const placeId = predictions[0].place_id;
        const details = await googlePlacesService.getPlaceDetails(placeId);

        if (details) {
          const location = {
            address: details.formatted_address,
            latitude: details.geometry.location.lat,
            longitude: details.geometry.location.lng,
          };
          setSelectedLocation(location);
          animateToLocation(location.latitude, location.longitude);
          setValue('address', details.formatted_address);
          return;
        }
      }

      // Fallback to expo-location geocoding
      const geocodeResults = await Location.geocodeAsync(address);
      if (geocodeResults.length > 0) {
        const location = {
          address,
          latitude: geocodeResults[0].latitude,
          longitude: geocodeResults[0].longitude,
        };
        setSelectedLocation(location);
        animateToLocation(location.latitude, location.longitude);
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
      // Silent fail - address might not be geocodable
    }
  };

  const getAddressFromCoordinates = async (coords: { latitude: number; longitude: number }) => {
    try {
      // Try Google Places reverse geocoding first
      const googleResults = await googlePlacesService.reverseGeocode(
        coords.latitude,
        coords.longitude
      );

      if (googleResults && googleResults.length > 0) {
        const address = googleResults[0].description;
        setValue('address', address);
        setSelectedLocation({
          address,
          latitude: coords.latitude,
          longitude: coords.longitude,
        });
      } else {
        // Fallback to expo-location reverse geocoding
        const reverseGeocode = await Location.reverseGeocodeAsync(coords);

        if (reverseGeocode[0]) {
          const geo = reverseGeocode[0];
          const parts = [geo.streetNumber, geo.street, geo.district, geo.city, geo.region].filter(
            Boolean
          );

          const address =
            parts.join(', ') || `${coords.latitude.toFixed(4)}, ${coords.longitude.toFixed(4)}`;

          setValue('address', address);
          setSelectedLocation({
            address,
            latitude: coords.latitude,
            longitude: coords.longitude,
          });
        }
      }
    } catch (error) {
      console.error('Error getting address from coordinates:', error);
      // Fallback to coordinates display
      const address = `${coords.latitude.toFixed(4)}, ${coords.longitude.toFixed(4)}`;
      setValue('address', address);
      setSelectedLocation({
        address,
        latitude: coords.latitude,
        longitude: coords.longitude,
      });
    }
  };

  const handleLocationSelect = async (location: {
    address: string;
    latitude?: number;
    longitude?: number;
    placeId?: string;
  }) => {
    Vibration.vibrate(10);
    setValue('address', location.address);
    setIsCurrentLocationActive(false); // User selected a different location

    if (location.latitude && location.longitude) {
      setSelectedLocation({
        address: location.address,
        latitude: location.latitude,
        longitude: location.longitude,
      });
      animateToLocation(location.latitude, location.longitude);
    } else if (location.placeId) {
      // If it's a Google Places result without coordinates, fetch details
      try {
        const details = await googlePlacesService.getPlaceDetails(location.placeId);
        if (details) {
          const coords = {
            address: details.formatted_address,
            latitude: details.geometry.location.lat,
            longitude: details.geometry.location.lng,
          };
          setValue('address', details.formatted_address);
          setSelectedLocation(coords);
          animateToLocation(coords.latitude, coords.longitude);
        }
      } catch (error) {
        console.error('Error fetching place details:', error);
        // Fallback to geocoding
        geocodeAddress(location.address);
      }
    } else {
      // If no coordinates or placeId, try to geocode
      geocodeAddress(location.address);
    }

    Keyboard.dismiss();
  };

  const animateToLocation = (latitude: number, longitude: number) => {
    const region = {
      latitude,
      longitude,
      latitudeDelta: currentZoom.latitudeDelta,
      longitudeDelta: currentZoom.longitudeDelta,
    };

    isMapMovingRef.current = true;
    mapRef.current?.animateToRegion(region, 1000);

    // Pulse animation for crosshair
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Reset flag after animation
    setTimeout(() => {
      isMapMovingRef.current = false;
    }, 1500);
  };

  const handleMapRegionChangeComplete = useCallback(
    async (region: Region) => {
      // Don't update if this change was triggered by our own animation
      if (isMapMovingRef.current) {
        return;
      }

      // Update current zoom level
      setCurrentZoom({
        latitudeDelta: region.latitudeDelta,
        longitudeDelta: region.longitudeDelta,
      });

      // Update address based on new map center
      const coords = {
        latitude: region.latitude,
        longitude: region.longitude,
      };

      // Check if user moved away from current location
      if (currentLocation && isCurrentLocationActive) {
        const distance = Math.sqrt(
          Math.pow(coords.latitude - currentLocation.latitude, 2) +
            Math.pow(coords.longitude - currentLocation.longitude, 2)
        );
        // If moved more than 0.0001 degrees (roughly 11 meters)
        if (distance > 0.0001) {
          setIsCurrentLocationActive(false);
        }
      }

      await getAddressFromCoordinates(coords);
    },
    [currentLocation, isCurrentLocationActive]
  );

  const handleZoomIn = () => {
    Vibration.vibrate(10);
    const newZoom = {
      latitudeDelta: currentZoom.latitudeDelta / 2,
      longitudeDelta: currentZoom.longitudeDelta / 2,
    };
    setCurrentZoom(newZoom);

    if (selectedLocation) {
      const region = {
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        ...newZoom,
      };
      mapRef.current?.animateToRegion(region, 300);
    }
  };

  const handleZoomOut = () => {
    Vibration.vibrate(10);
    const newZoom = {
      latitudeDelta: currentZoom.latitudeDelta * 2,
      longitudeDelta: currentZoom.longitudeDelta * 2,
    };
    setCurrentZoom(newZoom);

    if (selectedLocation) {
      const region = {
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        ...newZoom,
      };
      mapRef.current?.animateToRegion(region, 300);
    }
  };

  const handleCenterToLocation = () => {
    Vibration.vibrate(10);
    if (currentLocation) {
      setIsCurrentLocationActive(true);
      animateToLocation(currentLocation.latitude, currentLocation.longitude);
      getAddressFromCoordinates(currentLocation);
    } else {
      getCurrentLocation();
    }
  };

  const handleSaveLocation = async () => {
    if (!selectedLocation) {
      showToast('Please select a location first', 'warning');
      return;
    }

    if (!user) {
      showToast('Please login to save location', 'error');
      return;
    }

    try {
      setIsSaving(true);
      Vibration.vibrate(10);

      // Update user's address using the AuthContext method
      await updateUserInfo(
        user.name || '',
        '', // lastName parameter kept for compatibility
        user.email || '',
        user.phone,
        selectedLocation.address
      );

      // Show success notification
      showToast('Your address has been saved successfully!', 'success');
    } catch (error) {
      console.error('Error saving location:', error);
      showToast('Unable to save location. Please try again.', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const initialRegion = useMemo(() => {
    if (selectedLocation) {
      return {
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      };
    }
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      };
    }
    // Default to Ho Chi Minh City
    return {
      latitude: 10.8231,
      longitude: 106.6297,
      latitudeDelta: LATITUDE_DELTA,
      longitudeDelta: LONGITUDE_DELTA,
    };
  }, [currentLocation, selectedLocation]);

  // Create sections for FlatList to avoid nested ScrollView issues
  const sections = [
    { id: 'header', type: 'header' },
    { id: 'map', type: 'map' },
    ...(selectedLocation ? [{ id: 'selectedAddress', type: 'selectedAddress' }] : []),
    { id: 'saveButton', type: 'saveButton' },
  ];

  const renderSection = ({ item }: { item: (typeof sections)[0] }) => {
    switch (item.type) {
      case 'header':
        return (
          <View className="bg-white px-4 pb-3 pt-4 shadow-sm">
            <Text className="text-2xl font-bold text-gray-900">Location</Text>
            <Text className="mt-1 text-sm text-gray-500">Manage your delivery address</Text>
          </View>
        );

      case 'map':
        return (
          <View className="mx-4 mb-4 h-96 overflow-hidden rounded-md shadow-lg">
            <MapView
              ref={mapRef}
              style={{ width: '100%', height: '100%' }}
              initialRegion={initialRegion}
              onRegionChangeComplete={handleMapRegionChangeComplete}
              onMapReady={() => setMapReady(true)}
              showsUserLocation
              showsMyLocationButton={false}
              showsCompass={false}
              showsScale={false}>
              {/* User's current location circle */}
              {currentLocation && (
                <Marker coordinate={currentLocation} anchor={{ x: 0.5, y: 0.5 }}>
                  <View className="items-center justify-center">
                    <View className="h-6 w-6 rounded-full bg-blue-500/20">
                      <View className="m-1.5 h-3 w-3 rounded-full bg-blue-500" />
                    </View>
                  </View>
                </Marker>
              )}
            </MapView>

            {/* Center Crosshair with animation */}
            <View className="pointer-events-none absolute inset-0 items-center justify-center">
              <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                <Crosshair size={30} color="#3b82f6" strokeWidth={2} />
              </Animated.View>
            </View>

            {/* Map Control Buttons */}
            <View className="absolute bottom-4 right-4">
              <TouchableOpacity
                onPress={handleCenterToLocation}
                className={`mb-2 h-11 w-11 items-center justify-center rounded-full border shadow-sm ${
                  isCurrentLocationActive
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300 bg-white'
                }`}>
                <Locate size={20} color={isCurrentLocationActive ? '#ffffff' : '#3b82f6'} />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleZoomIn}
                className="mb-2 h-11 w-11 items-center justify-center rounded-full border border-gray-300 bg-white shadow-sm">
                <Plus size={20} color="#3b82f6" />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleZoomOut}
                className="h-11 w-11 items-center justify-center rounded-full border border-gray-300 bg-white shadow-sm">
                <Minus size={20} color="#3b82f6" />
              </TouchableOpacity>
            </View>

            {/* Map loading indicator */}
            {!mapReady && (
              <View className="absolute inset-0 items-center justify-center bg-white/80">
                <ActivityIndicator size="large" color="#3b82f6" />
                <Text className="mt-2 text-sm text-gray-500">Loading map...</Text>
              </View>
            )}

            {/* Address Search Bar */}
            <View className="absolute left-3 right-3 top-3" style={{ zIndex: 1000 }}>
              <View className="rounded-xl bg-white shadow-lg">
                <AddressInputGroup
                  control={control}
                  name="address"
                  placeholder="Search location..."
                  onLocationSelect={handleLocationSelect}
                  compact={true}
                  leftIcon={Search}
                />
              </View>
            </View>
          </View>
        );

      case 'selectedAddress':
        return (
          <View className="mx-4 mb-4">
            <View className="rounded-md border border-blue-200 bg-blue-50 p-4">
              <HStack className="items-start" space="sm">
                <View className="rounded-full bg-blue-500 p-2">
                  <MapPin size={16} color="white" />
                </View>
                <View className="flex-1">
                  <Text className="text-xs font-semibold uppercase text-blue-600">
                    SELECTED ADDRESS
                  </Text>
                  <Text className="mt-1 text-base font-semibold text-gray-900">
                    {selectedLocation?.address}
                  </Text>
                </View>
              </HStack>
            </View>
          </View>
        );

      case 'saveButton':
        return (
          <View className="mx-4 mb-4">
            <Button
              onPress={handleSaveLocation}
              disabled={isSaving || !selectedLocation}
              className={`w-full ${selectedLocation ? 'bg-blue-500' : 'bg-gray-300'}`}>
              {isSaving ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <ButtonText className="font-medium text-white">Save Location</ButtonText>
              )}
            </Button>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <ScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}>
          {sections.map((section) => (
            <View key={section.id}>{renderSection({ item: section })}</View>
          ))}
        </ScrollView>
      </Animated.View>
    </View>
  );
};
