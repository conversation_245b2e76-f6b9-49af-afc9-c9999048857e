import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box, Text } from '@/components/ui';

interface CategoryItemProps {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  isActive?: boolean;
  onPress?: () => void;
}

export const CategoryItem: React.FC<CategoryItemProps> = ({
  name,
  icon,
  isActive = false,
  onPress,
}) => {
  return (
    <TouchableOpacity onPress={onPress} className="mr-4 items-center">
      <Box
        className={`h-16 w-16 items-center justify-center rounded-2xl ${
          isActive ? 'bg-yellow-500' : 'bg-gray-900'
        }`}>
        <Ionicons name={icon} size={24} color={isActive ? '#111827' : '#f59e0b'} />
      </Box>
      <Text
        className={`mt-2 text-center text-xs font-medium ${
          isActive ? 'text-gray-900' : 'text-gray-600'
        }`}
        numberOfLines={1}>
        {name}
      </Text>
    </TouchableOpacity>
  );
};
