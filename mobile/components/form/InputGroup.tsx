import { <PERSON>ert<PERSON><PERSON><PERSON>, EyeIcon, EyeOffIcon } from 'lucide-react-native';
import React, { useState } from 'react';
import { Controller, Control, FieldValues, Path, FieldError } from 'react-hook-form';

import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlHelper,
  FormControlHelperText,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
} from '../ui';

// Extract InputFieldProps based on TextInput props
type InputFieldProps = React.ComponentProps<typeof InputField>;

type InputGroupProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label: string;
  placeholder?: string;
  helperText?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  leftIcon?: React.ElementType;
  rightIcon?: React.ElementType;
  onRightIconPress?: () => void;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  error?: FieldError;
  type?: 'text' | 'password';
} & Omit<
  InputFieldProps,
  | 'value'
  | 'onChangeText'
  | 'secureTextEntry'
  | 'keyboardType'
  | 'autoCapitalize'
  | 'placeholder'
  | 'onBlur'
>;

export function InputGroup<T extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  helperText,
  secureTextEntry,
  keyboardType = 'default',
  leftIcon,
  rightIcon,
  onRightIconPress,
  autoCapitalize = 'none',
  error,
  type = 'text',
  ...rest
}: InputGroupProps<T>) {
  const isInvalid = !!error;
  const [showPassword, setShowPassword] = useState(false);

  // Determine if this is a password field
  const isPassword = type === 'password';

  // Determine secureTextEntry based on showPassword state if this is a password field
  const isSecureTextEntry = isPassword ? !showPassword : secureTextEntry;

  // Handle password visibility toggle
  const handleTogglePassword = () => {
    setShowPassword((prev) => !prev);
  };

  // Check if this is a numeric input
  const isNumeric = keyboardType === 'numeric' || keyboardType === 'phone-pad';

  return (
    <FormControl isInvalid={isInvalid}>
      <FormControlLabel>
        <FormControlLabelText>{label}</FormControlLabelText>
      </FormControlLabel>

      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, onBlur, value } }) => (
          <Input>
            {leftIcon && (
              <InputSlot className="pl-3">
                <InputIcon as={leftIcon} />
              </InputSlot>
            )}

            <InputField
              placeholder={placeholder}
              value={typeof value === 'number' ? String(value) : value}
              onChangeText={(text) => {
                // Convert to number if numeric keyboard type
                if (isNumeric && keyboardType === 'numeric') {
                  const numValue = text === '' ? null : Number(text);
                  onChange(numValue);
                } else {
                  onChange(text);
                }
              }}
              onBlur={onBlur}
              secureTextEntry={isSecureTextEntry}
              keyboardType={keyboardType}
              autoCapitalize={autoCapitalize}
              {...rest}
            />

            {rightIcon && onRightIconPress ? (
              <InputSlot className="pr-3" onPress={onRightIconPress}>
                <InputIcon as={rightIcon} />
              </InputSlot>
            ) : isPassword ? (
              <InputSlot className="pr-3" onPress={handleTogglePassword}>
                <InputIcon as={showPassword ? EyeIcon : EyeOffIcon} />
              </InputSlot>
            ) : rightIcon ? (
              <InputSlot className="pr-3">
                <InputIcon as={rightIcon} />
              </InputSlot>
            ) : null}
          </Input>
        )}
      />

      {helperText && (
        <FormControlHelper>
          <FormControlHelperText>{helperText}</FormControlHelperText>
        </FormControlHelper>
      )}

      {isInvalid && (
        <FormControlError>
          <FormControlErrorIcon as={AlertCircle} size="sm" />
          <FormControlErrorText className="text-sm">{error?.message}</FormControlErrorText>
        </FormControlError>
      )}
    </FormControl>
  );
}
