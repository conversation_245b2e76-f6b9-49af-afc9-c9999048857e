import { Alert<PERSON>ircle, MapPin } from 'lucide-react-native';
import { useState, useRef, useEffect } from 'react';
import { TouchableOpacity, View, Keyboard, ScrollView } from 'react-native';
import { Controller, Control, FieldValues, Path, FieldError } from 'react-hook-form';
import * as Location from 'expo-location';

import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlHelper,
  FormControlHelperText,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  Text,
  VStack,
  HStack,
} from '../ui';

import { googlePlacesService } from '@/services/google-places';

type LocationSuggestion = {
  id: string;
  placeId?: string;
  address: string;
  description?: string;
  latitude?: number;
  longitude?: number;
};

type AddressInputGroupProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  helperText?: string;
  error?: FieldError;
  onLocationSelect?: (location: LocationSuggestion) => void;
  minSearchLength?: number;
  debounceMs?: number;
  inputPadding?: string;
  compact?: boolean;
  leftIcon?: React.ElementType;
  rightIcon?: React.ElementType;
  onRightIconPress?: () => void;
};

export function AddressInputGroup<T extends FieldValues>({
  control,
  name,
  label,
  placeholder = 'Enter address...',
  helperText,
  error,
  onLocationSelect,
  minSearchLength = 3,
  debounceMs = 500,
  inputPadding,
  compact = false,
  leftIcon = MapPin,
  rightIcon,
  onRightIconPress,
}: AddressInputGroupProps<T>) {
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const debounceTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const inputRef = useRef<any>(null);

  const isInvalid = !!error;

  const searchLocations = async (text: string) => {
    if (text.length < minSearchLength) {
      setSuggestions([]);
      return;
    }

    setIsSearching(true);

    try {
      // Get current location for biasing results
      let currentLocation: { lat: number; lng: number } | undefined;
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Low,
          });
          currentLocation = {
            lat: location.coords.latitude,
            lng: location.coords.longitude,
          };
        }
      } catch {
        // Silent fail - will search without location bias
      }

      // Use Google Places Autocomplete API
      const predictions = await googlePlacesService.autocomplete({
        input: text,
        location: currentLocation,
        radius: 50000, // 50km radius
        components: 'country:vn|country:us', // Restrict to Vietnam and US
        language: 'en',
      });

      if (predictions && predictions.length > 0) {
        const locationSuggestions: LocationSuggestion[] = predictions.map((prediction) => ({
          id: prediction.place_id,
          placeId: prediction.place_id,
          address: prediction.description,
          description: prediction.structured_formatting.secondary_text,
        }));

        setSuggestions(locationSuggestions);
      } else {
        // Fallback to expo-location if Google Places returns no results
        const results = await Location.geocodeAsync(text);

        if (results && results.length > 0) {
          const locationSuggestions: LocationSuggestion[] = await Promise.all(
            results.slice(0, 5).map(async (result, index) => {
              const reverseGeocode = await Location.reverseGeocodeAsync({
                latitude: result.latitude,
                longitude: result.longitude,
              });

              const address = reverseGeocode[0];
              const formattedAddress = [
                address?.name,
                address?.street,
                address?.district,
                address?.city,
                address?.region,
                address?.country,
              ]
                .filter(Boolean)
                .join(', ');

              return {
                id: `${result.latitude}-${result.longitude}-${index}`,
                address: formattedAddress || text,
                description: `${address?.city || ''} ${address?.region || ''}`.trim(),
                latitude: result.latitude,
                longitude: result.longitude,
              };
            })
          );

          setSuggestions(locationSuggestions);
        } else {
          setSuggestions([]);
        }
      }
    } catch (error) {
      console.error('Error searching locations:', error);
      setSuggestions([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleTextChange = (text: string, onChange: (value: any) => void) => {
    onChange(text);

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      searchLocations(text);
    }, debounceMs);
  };

  const handleFocus = () => {
    setIsFocused(true);
    setShowSuggestions(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setTimeout(() => {
      if (!isFocused) {
        setShowSuggestions(false);
      }
    }, 200);
  };

  const selectLocation = async (location: LocationSuggestion, onChange: (value: any) => void) => {
    onChange(location.address);
    setShowSuggestions(false);
    setSuggestions([]);
    Keyboard.dismiss();

    // If it's a Google Places result, fetch details to get coordinates
    if (location.placeId && !location.latitude) {
      try {
        const details = await googlePlacesService.getPlaceDetails(location.placeId);
        if (details) {
          location.latitude = details.geometry.location.lat;
          location.longitude = details.geometry.location.lng;
          location.address = details.formatted_address;
          onChange(details.formatted_address);
        }
      } catch (error) {
        console.error('Error fetching place details:', error);
      }
    }

    if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  const renderSuggestionItem = ({
    item,
    onChange,
  }: {
    item: LocationSuggestion;
    onChange: (value: any) => void;
  }) => (
    <TouchableOpacity
      key={item.id}
      onPress={() => selectLocation(item, onChange)}
      className="border-b border-gray-100 p-3">
      <HStack space="sm" className="items-center">
        <MapPin size={20} color="#6b7280" />
        <VStack className="flex-1">
          <Text className="text-sm font-medium text-gray-900">{item.address}</Text>
          {item.description && <Text className="text-xs text-gray-500">{item.description}</Text>}
        </VStack>
      </HStack>
    </TouchableOpacity>
  );

  return (
    <FormControl isInvalid={isInvalid}>
      {label && (
        <FormControlLabel>
          <FormControlLabelText>{label}</FormControlLabelText>
        </FormControlLabel>
      )}

      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, onBlur, value } }) => (
          <View
            className={`relative ${showSuggestions && suggestions.length > 0 ? 'z-50' : 'z-0'}`}>
            <Input className={compact ? 'min-h-0' : ''}>
              {leftIcon && (
                <InputSlot className={inputPadding || (compact ? 'pl-2' : 'pl-3')}>
                  <InputIcon as={leftIcon} size={compact ? 'sm' : 'md'} />
                </InputSlot>
              )}

              <InputField
                className={compact ? 'py-2 text-sm' : ''}
                ref={inputRef}
                placeholder={placeholder}
                value={value || ''}
                onChangeText={(text) => handleTextChange(text, onChange)}
                onFocus={handleFocus}
                onBlur={() => {
                  onBlur();
                  handleBlur();
                }}
                autoCapitalize="none"
                autoCorrect={false}
              />

              {isSearching ? (
                <InputSlot className={inputPadding || (compact ? 'pr-2' : 'pr-3')}>
                  <Text className="text-xs text-gray-500">Searching...</Text>
                </InputSlot>
              ) : rightIcon ? (
                <InputSlot
                  className={inputPadding || (compact ? 'pr-2' : 'pr-3')}
                  onPress={onRightIconPress}>
                  <InputIcon as={rightIcon} size={compact ? 'sm' : 'md'} />
                </InputSlot>
              ) : null}
            </Input>

            {showSuggestions && suggestions.length > 0 && isFocused && (
              <View
                className="absolute left-0 right-0 top-full z-50 mt-1 max-h-48 rounded-lg border border-gray-200 bg-white shadow-2xl"
                style={{ elevation: 10 }}>
                <ScrollView
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                  showsVerticalScrollIndicator={false}>
                  {suggestions.map((item) => renderSuggestionItem({ item, onChange }))}
                </ScrollView>
              </View>
            )}
          </View>
        )}
      />

      {helperText && (
        <FormControlHelper>
          <FormControlHelperText>{helperText}</FormControlHelperText>
        </FormControlHelper>
      )}

      {isInvalid && (
        <FormControlError>
          <FormControlErrorIcon as={AlertCircle} size="sm" />
          <FormControlErrorText className="text-sm">{error?.message}</FormControlErrorText>
        </FormControlError>
      )}
    </FormControl>
  );
}
