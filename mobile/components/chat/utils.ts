import { Message } from './types';

/**
 * Format message time for display
 * - Shows hours:minutes for messages from today
 * - Shows day/month + hours:minutes for messages older than 24 hours
 */
export const formatMessageTime = (date: Date): string => {
  const now = new Date();
  const diffInHours = Math.abs(now.getTime() - date.getTime()) / 36e5;

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else {
    return `${date.toLocaleDateString([], { day: '2-digit', month: '2-digit' })} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  }
};

/**
 * Create a message from the user
 */
export const createUserMessage = (text: string): Message => ({
  id: Date.now().toString(),
  text,
  sender: 'user',
  timestamp: new Date(),
});

/**
 * Create a message from the user with an image
 */
export const createUserImageMessage = (imageUri: string): Message => ({
  id: Date.now().toString(),
  image: imageUri,
  sender: 'user',
  timestamp: new Date(),
});

/**
 * Create a message from the service
 */
export const createServiceMessage = (
  response: string | { text: string; formatted?: string }
): Message => {
  const text = typeof response === 'string' ? response : response.text;
  const formattedText = typeof response === 'string' ? undefined : response.formatted;

  return {
    id: (Date.now() + 1).toString(),
    text,
    formattedText,
    sender: 'service',
    timestamp: new Date(),
  };
};

/**
 * Create a typing indicator message from the service
 */
export const createTypingMessage = (): Message => ({
  id: (Date.now() + 1).toString(),
  text: '',
  sender: 'service',
  timestamp: new Date(),
  isTyping: true,
});
