import React from 'react';
import { useColorScheme } from 'react-native';
import Markdown from 'react-native-markdown-display';

interface FormattedMarkdownProps {
  content: string;
  isUserMessage?: boolean;
}

export function FormattedMarkdown({ content, isUserMessage = false }: FormattedMarkdownProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark' || isUserMessage;

  // Tailwind-inspired styles for markdown
  const markdownStyle = {
    body: {
      fontSize: 12,
      color: isDark ? '#FBBF24' : '#1F2937',
      lineHeight: 18,
    },
    heading1: {
      fontSize: 16,
      fontWeight: 'bold',
      marginTop: 8,
      marginBottom: 4,
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    heading2: {
      fontSize: 14,
      fontWeight: 'bold',
      marginTop: 6,
      marginBottom: 3,
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    heading3: {
      fontSize: 13,
      fontWeight: 'bold',
      marginTop: 4,
      marginBottom: 2,
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    paragraph: {
      marginVertical: 4,
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    bullet_list: {
      marginLeft: 8,
      marginVertical: 4,
    },
    bullet_list_icon: {
      color: isDark ? '#FBBF24' : '#6366F1',
    },
    bullet_list_item: {
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    ordered_list: {
      marginLeft: 8,
      marginVertical: 4,
    },
    ordered_list_icon: {
      color: isDark ? '#FBBF24' : '#6366F1',
    },
    ordered_list_item: {
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    blockquote: {
      backgroundColor: isDark ? '#4B5563' : '#E5E7EB',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderLeftWidth: 4,
      borderLeftColor: isDark ? '#FBBF24' : '#6366F1',
      marginVertical: 4,
    },
    hr: {
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#4B5563' : '#D1D5DB',
      marginVertical: 8,
    },
    code_block: {
      backgroundColor: isDark ? '#374151' : '#F3F4F6',
      padding: 8,
      borderRadius: 4,
      fontFamily: 'monospace',
      fontSize: 11,
    },
    code_inline: {
      backgroundColor: isDark ? '#374151' : '#F3F4F6',
      paddingHorizontal: 4,
      borderRadius: 2,
      fontFamily: 'monospace',
      fontSize: 11,
      color: isDark ? '#FBBF24' : '#6366F1',
    },
    link: {
      color: isDark ? '#93C5FD' : '#6366F1',
      textDecorationLine: 'underline',
    },
    image: {
      maxWidth: '100%',
      marginVertical: 8,
      borderRadius: 4,
    },
    strong: {
      fontWeight: 'bold',
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    em: {
      fontStyle: 'italic',
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    table: {
      marginVertical: 8,
      borderWidth: 1,
      borderColor: isDark ? '#4B5563' : '#D1D5DB',
    },
    thead: {
      backgroundColor: isDark ? '#4B5563' : '#F3F4F6',
    },
    th: {
      padding: 6,
      fontWeight: 'bold',
      color: isDark ? '#FBBF24' : '#1F2937',
    },
    td: {
      padding: 6,
      borderWidth: 1,
      borderColor: isDark ? '#4B5563' : '#D1D5DB',
      color: isDark ? '#FBBF24' : '#1F2937',
    },
  };

  return <Markdown style={markdownStyle as any}>{content}</Markdown>;
}
