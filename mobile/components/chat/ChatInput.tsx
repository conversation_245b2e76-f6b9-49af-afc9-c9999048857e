import { SendHorizonal, Zap } from 'lucide-react-native';
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, TouchableOpacity, Platform, TextInput, Pressable } from 'react-native';

import { Box } from '@/components/ui';

interface ChatInputProps {
  initialMessage?: string;
  onSendMessage: (text: string) => void;
  mode?: 'normal' | 'ai';
}

export function ChatInput({ initialMessage = '', onSendMessage, mode = 'normal' }: ChatInputProps) {
  // Local state
  const [isFocused, setIsFocused] = useState(false);
  const [message, setMessage] = useState(initialMessage);

  // Refs
  const inputRef = useRef<TextInput>(null);

  // Update local message when initialMessage changes (e.g. after sending)
  useEffect(() => {
    setMessage(initialMessage);
  }, [initialMessage]);

  // Focus input field
  const focusInput = useCallback(() => {
    inputRef.current?.focus();
    setIsFocused(true);
  }, []);

  // Handle send button press
  const handleSend = useCallback(() => {
    if (message.trim()) {
      onSendMessage(message.trim());
    }
  }, [message, onSendMessage]);

  // Style properties based on mode and focus state
  const styles = {
    border: isFocused
      ? mode === 'normal'
        ? '#111827'
        : '#4f46e5'
      : mode === 'normal'
        ? '#e2e8f0'
        : '#c7d2fe',
    background: mode === 'normal' ? '#f8fafc' : '#eef2ff',
    container: mode === 'normal' ? 'bg-white' : 'bg-gradient-to-r from-white to-indigo-50',
    borderClass: mode === 'normal' ? 'border-slate-100' : 'border-indigo-100',
    placeholder: mode === 'normal' ? '#a3a3a3' : '#818cf8',
    buttonBg: mode === 'normal' ? 'bg-gray-900' : 'bg-indigo-600',
    placeholderText: mode === 'normal' ? 'Enter a message...' : 'Ask AI assistant...',
  };

  return (
    <Box
      className={`flex-row items-end border-t ${styles.borderClass} ${styles.container} px-4 py-3`}>
      {/* Input field */}
      <Pressable onPress={focusInput} className="flex-1">
        <View
          className="max-h-64 flex-row items-center rounded-xl border px-4 py-2"
          style={{
            borderColor: styles.border,
            backgroundColor: styles.background,
          }}>
          <TextInput
            ref={inputRef}
            value={message}
            onChangeText={setMessage}
            className="flex-1 py-1 text-left font-normal text-neutral-800"
            placeholder={styles.placeholderText}
            placeholderTextColor={styles.placeholder}
            multiline
            maxLength={500}
            numberOfLines={Platform.OS === 'ios' ? undefined : 1}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            blurOnSubmit={false}
          />
        </View>
      </Pressable>

      {/* Send button - only show when there's text to send */}
      {message.trim() && (
        <TouchableOpacity
          onPress={handleSend}
          activeOpacity={0.7}
          className={`ml-2 flex items-center justify-center rounded-full ${styles.buttonBg} p-3`}>
          {mode === 'normal' ? (
            <SendHorizonal size={18} color="#f59e0b" strokeWidth={2} />
          ) : (
            <Zap size={18} color="#f0abfc" strokeWidth={2} />
          )}
        </TouchableOpacity>
      )}
    </Box>
  );
}
