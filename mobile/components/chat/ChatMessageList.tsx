import React, { useRef } from 'react';
import { FlatList, View } from 'react-native';

import { ChatMessage } from './ChatMessage';
import { Message } from './types';

interface ChatMessageListProps {
  messages: Message[];
  formatTime: (date: Date) => string;
  mode?: 'normal' | 'ai';
}

export function ChatMessageList({ messages, formatTime, mode = 'normal' }: ChatMessageListProps) {
  const flatListRef = useRef<FlatList<Message>>(null);

  const renderItem = ({ item }: { item: Message }) => (
    <ChatMessage message={item} formatTime={formatTime} mode={mode} />
  );

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      className="flex-1 bg-white"
      contentContainerStyle={{ padding: 16, paddingBottom: 20 }}
      showsVerticalScrollIndicator={true}
      removeClippedSubviews={true}
      initialNumToRender={20}
      maxToRenderPerBatch={10}
      windowSize={10}
      updateCellsBatchingPeriod={50}
      inverted={true}
      ListEmptyComponent={
        <View className="flex-1 items-center justify-center py-20">
          <View className="items-center">
            <View className="mb-4 h-16 w-16 items-center justify-center rounded-full bg-gray-100">
              <View className="h-8 w-8 rounded-full bg-gray-300" />
            </View>
            <View className="items-center">
              <View className="mb-2 h-4 w-32 rounded bg-gray-200" />
              <View className="h-3 w-48 rounded bg-gray-100" />
            </View>
          </View>
        </View>
      }
    />
  );
}
