import React, { useEffect, useState } from 'react';
import { View } from 'react-native';

import { FormattedMarkdown } from './FormattedMarkdown';
import { Message } from './types';

import { Box } from '@/components/ui';
import { Text } from '@/components/ui/text';

interface ChatMessageProps {
  message: Message;
  formatTime: (date: Date) => string;
  mode?: 'normal' | 'ai';
}

export function ChatMessage({ message, formatTime, mode = 'normal' }: ChatMessageProps) {
  const { sender, text, formattedText, timestamp, isTyping, sending } = message;
  const isUser = sender === 'user';
  const [typingDots, setTypingDots] = useState('');

  // Typing effect for AI messages
  useEffect(() => {
    if (isTyping && !isUser) {
      const interval = setInterval(() => {
        setTypingDots((prev) => {
          if (prev === '...') return '.';
          if (prev === '..') return '...';
          if (prev === '.') return '..';
          return '.';
        });
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isTyping, isUser]);

  // Simplified style based on sender and mode
  const messageStyle = isUser
    ? 'bg-gray-900 rounded-lg rounded-tr-none ml-auto'
    : mode === 'ai'
      ? 'bg-indigo-100 rounded-lg rounded-tl-none'
      : 'bg-gray-100 rounded-lg rounded-tl-none';

  const textStyle = isUser ? 'text-yellow-400' : 'text-gray-800';

  return (
    <View
      className={`my-1 max-w-[80%] ${isUser ? 'self-end' : 'self-start'} ${sending ? 'opacity-70' : ''}`}>
      <Box className={`px-3 py-2 ${messageStyle}`}>
        {isTyping ? (
          <Text className={`text-sm ${textStyle} italic`}>Thinking {typingDots}</Text>
        ) : formattedText ? (
          <FormattedMarkdown content={formattedText} isUserMessage={isUser} />
        ) : (
          <Text className={`text-sm ${textStyle}`}>{text}</Text>
        )}
        {sending ? (
          <Text className="mt-1 text-xs text-gray-400">sending</Text>
        ) : (
          <Text className="mt-1 text-xs text-gray-500">{formatTime(timestamp)}</Text>
        )}
      </Box>
    </View>
  );
}
