import { Refresh<PERSON><PERSON>, MessageCircle, Bot } from 'lucide-react-native';
import React from 'react';
import { Pressable } from 'react-native';

import { Box, HStack, VStack } from '@/components/ui';
import { Text } from '@/components/ui/text';

interface ChatHeaderProps {
  title: string;
  subtitle?: string;
  onClearHistory: () => void;
  mode?: 'normal' | 'ai';
}

export function ChatHeader({ title, subtitle, onClearHistory, mode = 'normal' }: ChatHeaderProps) {
  // Modern flat design colors
  const bgClass = mode === 'ai' ? 'bg-indigo-50' : 'bg-gray-50';
  const borderClass = mode === 'ai' ? 'border-indigo-100' : 'border-gray-100';
  const textClass = mode === 'ai' ? 'text-indigo-900' : 'text-gray-900';
  const subtitleClass = mode === 'ai' ? 'text-indigo-500/80' : 'text-gray-500/80';

  // Icon styling
  const iconColor = mode === 'ai' ? '#4f46e5' : '#facc15';
  const iconBgClass = mode === 'ai' ? 'bg-white' : 'bg-gray-900';
  const buttonActiveClass = 'active:bg-indigo-200';

  return (
    <Box className={`border-b ${borderClass} ${bgClass}`}>
      <HStack className="items-center justify-between px-5 py-4">
        {/* Chat title with icon */}
        <HStack className="items-center gap-3">
          <Box className={`rounded-full ${iconBgClass} p-2.5`}>
            {mode === 'ai' ? (
              <Bot size={20} color={iconColor} />
            ) : (
              <MessageCircle size={20} color={iconColor} />
            )}
          </Box>
          <VStack>
            <Text className={`text-base font-medium ${textClass}`}>{title}</Text>
            {subtitle && <Text className={`text-xs ${subtitleClass}`}>{subtitle}</Text>}
          </VStack>
        </HStack>

        {/* Clear history button - only for AI mode */}
        {mode === 'ai' && (
          <Pressable onPress={onClearHistory} className={`rounded-full p-2.5 ${buttonActiveClass}`}>
            <RefreshCw size={18} color="#4338ca" />
          </Pressable>
        )}
      </HStack>
    </Box>
  );
}
