import * as Clipboard from 'expo-clipboard';
import React from 'react';
import { TouchableOpacity, Linking, Alert, Platform } from 'react-native';

import { Text } from '@/components/ui';

interface BookNowButtonProps {
  phoneNumber?: string;
  className?: string;
  textClassName?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function BookNowButton({
  phoneNumber = '0987654321',
  className = '',
  textClassName = '',
  size = 'sm',
}: BookNowButtonProps) {
  const handlePress = async () => {
    try {
      // Choose the appropriate URL scheme based on platform
      let phoneUrl = '';

      if (Platform.OS === 'ios') {
        // On iOS, try telprompt: first (shows confirmation dialog)
        phoneUrl = `telprompt:${phoneNumber}`;
      } else {
        // On Android, use tel: scheme
        phoneUrl = `tel:${phoneNumber}`;
      }

      const canOpen = await Linking.canOpenURL(phoneUrl);

      if (canOpen) {
        await Linking.openURL(phoneUrl);
        return;
      }

      // If the primary approach fails, try the alternative
      const alternativeUrl =
        Platform.OS === 'ios'
          ? `tel:${phoneNumber}` // Fallback to tel: on iOS
          : `tel://${phoneNumber}`; // Try alternative format on Android

      const canOpenAlternative = await Linking.canOpenURL(alternativeUrl);
      if (canOpenAlternative) {
        await Linking.openURL(alternativeUrl);
        return;
      }

      // If both approaches fail, show the number to the user
      Alert.alert('Call Customer Service', `Please call our customer service at ${phoneNumber}`, [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Copy Number',
          onPress: async () => {
            try {
              // Use expo-clipboard's setStringAsync
              await Clipboard.setStringAsync(phoneNumber);
              Alert.alert('Number Copied', `${phoneNumber} copied to clipboard`);
            } catch (clipboardError) {
              console.error('Failed to copy to clipboard:', clipboardError);
              Alert.alert('Error', 'Could not copy number to clipboard');
            }
          },
        },
      ]);
    } catch (error) {
      console.error('Failed to make phone call:', error);
      // Show a user-friendly message with the phone number
      Alert.alert(
        'Could not initiate call',
        `Please call our customer service directly at ${phoneNumber}`,
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  return (
    <TouchableOpacity
      className={`rounded-md bg-yellow-500 px-4 py-2 ${className}`}
      onPress={handlePress}>
      <Text className={`font-bold text-gray-900 ${textClassName}`} size={size}>
        Book Now
      </Text>
    </TouchableOpacity>
  );
}
