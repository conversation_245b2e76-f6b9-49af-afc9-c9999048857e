import { Star } from 'lucide-react-native';
import React from 'react';
import { View, Text } from 'react-native';

interface RatingDisplayProps {
  rating: number;
  showValue?: boolean;
  size?: number;
  textClassName?: string;
  colorScheme?: 'amber' | 'blue' | 'green';
  spacing?: number;
  maxRating?: number;
}

export const RatingDisplay: React.FC<RatingDisplayProps> = ({
  rating,
  showValue = true,
  size = 16,
  textClassName,
  colorScheme = 'amber',
  spacing = 2,
  maxRating = 5,
}) => {
  // Ensure rating is between 1 and maxRating
  const safeRating = Math.max(1, Math.min(maxRating, rating));

  // Define color schemes
  const colorSchemes = {
    amber: {
      fill: '#f59e0b',
      text: 'text-amber-500',
    },
    blue: {
      fill: '#3b82f6',
      text: 'text-blue-500',
    },
    green: {
      fill: '#10b981',
      text: 'text-green-500',
    },
  };

  const activeColor = colorSchemes[colorScheme].fill;
  const textColor = colorSchemes[colorScheme].text;

  // Calculate margin based on spacing prop
  const marginStyle = { marginRight: spacing };

  return (
    <View className="flex-row items-center">
      <View className="flex-row">
        {Array.from({ length: maxRating }, (_, i) => i + 1).map((value) => (
          <Star
            key={value}
            size={size}
            color={activeColor}
            fill={value <= safeRating ? activeColor : 'transparent'}
            style={value < maxRating ? marginStyle : undefined}
          />
        ))}
      </View>

      {showValue && (
        <Text
          className={`ml-2 font-semibold ${textColor} ${textClassName || ''}`}
          style={{ fontSize: Math.max(size * 0.8, 12) }}>
          {safeRating.toFixed(1)}
        </Text>
      )}
    </View>
  );
};
