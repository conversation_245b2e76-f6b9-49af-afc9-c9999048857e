import { formatDistanceToNow } from 'date-fns';
import { Star, Edit, Calendar } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';

import { RatingDisplay } from './RatingDisplay';

import { supabase } from '@/lib/supabase';

interface FeedbackViewProps {
  feedbackId: string;
  onEditPress: () => void;
}

// Section header component (đồng nhất với [id].tsx)
const SectionHeader = ({
  icon,
  title,
  rightElement,
}: {
  icon: React.ReactNode;
  title: string;
  rightElement?: React.ReactNode;
}) => (
  <View className="mb-2 flex-row items-center justify-between">
    <View className="flex-row items-center">
      <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-sky-50">{icon}</View>
      <Text className="text-sm font-semibold text-gray-800">{title}</Text>
    </View>
    {rightElement}
  </View>
);

interface Feedback {
  id: string;
  order_id: string;
  technician_id: string;
  customer_id: string;
  rate: number;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export const FeedbackView: React.FC<FeedbackViewProps> = ({ feedbackId, onEditPress }) => {
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeedback();
  }, [feedbackId]);

  const fetchFeedback = async () => {
    if (!feedbackId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('feedback')
        .select('*')
        .eq('id', feedbackId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      setFeedback(data as Feedback);
    } catch (error) {
      console.error('Error fetching feedback:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
        <View className="items-center justify-center py-4">
          <ActivityIndicator size="small" color="#0284c7" />
          <Text className="mt-2 text-gray-500">Loading feedback...</Text>
        </View>
      </View>
    );
  }

  if (!feedback) {
    return (
      <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
        <Text className="text-center text-gray-500">Feedback not found</Text>
      </View>
    );
  }

  return (
    <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
      <SectionHeader
        icon={<Star size={14} color="#0369a1" />}
        title="Your Feedback"
        rightElement={
          <TouchableOpacity onPress={onEditPress} className="rounded-md bg-amber-50 px-2.5 py-1">
            <View className="flex-row items-center">
              <Edit size={12} color="#92400e" />
              <Text className="ml-1 text-xs font-medium text-amber-800">Edit</Text>
            </View>
          </TouchableOpacity>
        }
      />

      {/* Rating display with improved alignment */}
      <View className="mb-3 flex-row items-center justify-center py-1">
        <RatingDisplay rating={feedback.rate} size={24} showValue />
      </View>

      {/* Comment section with proper spacing and styling */}
      {feedback.description && (
        <View className="mb-3">
          <Text className="mb-1 text-xs text-gray-500">Your Comments</Text>
          <View className="rounded-md bg-gray-50 px-3 py-2">
            <Text className="text-sm text-gray-800">{feedback.description}</Text>
          </View>
        </View>
      )}

      {/* Timestamp with improved styling */}
      <View className="flex-row items-center justify-end">
        <Calendar size={12} color="#6b7280" />
        <Text className="ml-1 text-xs text-gray-500">
          {formatDistanceToNow(new Date(feedback.created_at), { addSuffix: true })}
          {feedback.updated_at !== feedback.created_at && ' (edited)'}
        </Text>
      </View>
    </View>
  );
};
