import { Star, Edit } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';

import { useNotification } from '@/services/toast-service';
import { supabase } from '@/lib/supabase';

interface FeedbackFormProps {
  orderId: string;
  technicianId: string;
  customerId: string;
  feedbackId?: string;
  isEditing?: boolean;
  onFeedbackSubmitted?: () => void;
}

// Section header component (đồng nhất với [id].tsx)
const SectionHeader = ({
  icon,
  title,
  rightElement,
}: {
  icon: React.ReactNode;
  title: string;
  rightElement?: React.ReactNode;
}) => (
  <View className="mb-2 flex-row items-center justify-between">
    <View className="flex-row items-center">
      <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-sky-50">{icon}</View>
      <Text className="text-sm font-semibold text-gray-800">{title}</Text>
    </View>
    {rightElement}
  </View>
);

export const FeedbackForm: React.FC<FeedbackFormProps> = ({
  orderId,
  technicianId,
  customerId,
  feedbackId,
  isEditing = false,
  onFeedbackSubmitted,
}) => {
  const [rating, setRating] = useState<number>(0);
  const [description, setDescription] = useState<string>('');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { showToast } = useNotification();

  // Fetch existing feedback if in editing mode
  useEffect(() => {
    if (isEditing && feedbackId) {
      fetchExistingFeedback();
    }
  }, [isEditing, feedbackId]);

  const fetchExistingFeedback = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('feedback')
        .select('*')
        .eq('id', feedbackId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (data) {
        setRating(data.rate || 0);
        setDescription(data.description || '');
      }
    } catch (error: any) {
      console.error('Error fetching feedback:', error);
      showToast('Could not load existing feedback', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRatingPress = (value: number) => {
    setRating(value);
  };

  const submitFeedback = async () => {
    if (rating === 0) {
      showToast('Please select a rating', 'error');
      return;
    }

    try {
      setSubmitting(true);

      if (isEditing && feedbackId) {
        // Update existing feedback
        const { error: updateError } = await supabase
          .from('feedback')
          .update({
            rate: rating,
            description: description.trim() || null,
          })
          .eq('id', feedbackId);

        if (updateError) {
          throw new Error(updateError.message);
        }

        showToast('Your feedback has been updated', 'success');
      } else {
        // Insert new feedback
        const { data: feedbackData, error: feedbackError } = await supabase
          .from('feedback')
          .insert({
            order_id: orderId,
            technician_id: technicianId,
            customer_id: customerId,
            rate: rating,
            description: description.trim() || null,
          })
          .select('id')
          .single();

        if (feedbackError) {
          throw new Error(feedbackError.message);
        }

        // Update order with feedback reference
        const { error: orderError } = await supabase
          .from('orders')
          .update({ feedback_id: feedbackData.id })
          .eq('id', orderId);

        if (orderError) {
          throw new Error(orderError.message);
        }

        showToast('Thank you for your feedback!', 'success');
      }

      if (onFeedbackSubmitted) {
        onFeedbackSubmitted();
      }

      // Reset form if not editing
      if (!isEditing) {
        setRating(0);
        setDescription('');
      }
    } catch (error: any) {
      console.error('Error submitting feedback:', error);
      showToast(error.message || 'Failed to submit feedback', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
        <View className="items-center justify-center py-4">
          <Text className="text-gray-500">Loading feedback...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
      <SectionHeader
        icon={isEditing ? <Edit size={14} color="#0369a1" /> : <Star size={14} color="#0369a1" />}
        title={isEditing ? 'Edit Your Feedback' : 'Rate Your Service'}
      />

      <View className="my-2 flex-row justify-center">
        {[1, 2, 3, 4, 5].map((value) => (
          <TouchableOpacity
            key={value}
            onPress={() => handleRatingPress(value)}
            className="px-1.5 py-1.5">
            <Star
              size={32}
              color={value <= rating ? '#f59e0b' : '#d1d5db'}
              fill={value <= rating ? '#f59e0b' : 'transparent'}
            />
          </TouchableOpacity>
        ))}
      </View>

      <View className="mb-4">
        <Text className="mb-1.5 text-xs text-gray-500">Your Comments</Text>

        <TextInput
          className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-gray-800"
          placeholder="Tell us about your experience (optional)"
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>

      <TouchableOpacity
        className={`items-center rounded-md px-4 py-2 ${
          submitting
            ? 'bg-blue-400'
            : rating === 0
              ? 'bg-gray-400'
              : isEditing
                ? 'bg-amber-600'
                : 'bg-blue-600'
        }`}
        onPress={submitFeedback}
        disabled={submitting || rating === 0}>
        <Text className="text-base font-semibold text-white">
          {submitting ? 'Submitting...' : isEditing ? 'Update Feedback' : 'Submit Feedback'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
