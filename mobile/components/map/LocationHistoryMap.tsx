import { format } from 'date-fns';
import { Clock, Navigation, MapPin } from 'lucide-react-native';
import React, { useEffect, useState, useRef } from 'react';
import { View, Text, Dimensions, TouchableOpacity, Platform } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Callout, Circle } from 'react-native-maps';

import { supabase } from '@/lib/supabase';

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.0922;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;
const IS_IOS = Platform.OS === 'ios';

interface LocationPoint {
  lat: number;
  lng: number;
  ts: number;
}

interface LocationHistoryMapProps {
  orderId: string;
  technicianId: string;
  initialLocation?: {
    latitude: number;
    longitude: number;
  };
}

export function LocationHistoryMap({
  orderId,
  technicianId,
  initialLocation = {
    latitude: 10.8231, // Default to Ho Chi Minh City
    longitude: 106.6297,
  },
}: LocationHistoryMapProps) {
  const [locations, setLocations] = useState<LocationPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPoint, setSelectedPoint] = useState<LocationPoint | null>(null);
  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    fetchLocationHistory();

    // Subscribe to real-time updates
    const channel = supabase
      .channel('location_updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'technician_locations',
          filter: `order_id=eq.${orderId}`,
        },
        (payload) => {
          if (payload.new && payload.new.locations) {
            setLocations(payload.new.locations);
            // Fit to new coordinates when updated
            setTimeout(() => fitToCoordinates(), 500);
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [orderId, technicianId]);

  const fetchLocationHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('technician_locations')
        .select('locations, started_at, ended_at')
        .eq('order_id', orderId)
        .eq('technician_id', technicianId)
        .single();

      if (error) {
        throw error;
      }

      if (data && data.locations) {
        setLocations(data.locations);
        // Fit to coordinates after loading
        setTimeout(() => fitToCoordinates(), 500);
      }
    } catch (err: any) {
      console.error('Error fetching location history:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp: number) => {
    return format(new Date(timestamp), 'HH:mm:ss');
  };

  const fitToCoordinates = () => {
    if (mapRef.current && locations.length > 0) {
      const coords = locations.map((loc) => ({
        latitude: loc.lat,
        longitude: loc.lng,
      }));

      mapRef.current.fitToCoordinates(coords, {
        edgePadding: {
          top: 50,
          right: 50,
          bottom: 50,
          left: 50,
        },
        animated: true,
      });
    }
  };

  if (loading) {
    return (
      <View className="items-center justify-center rounded-lg bg-gray-50 p-4">
        <Text className="text-sm text-gray-600">Loading location history...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="items-center justify-center rounded-lg bg-red-50 p-4">
        <Text className="text-sm text-red-600">Failed to load location history</Text>
      </View>
    );
  }

  if (!locations.length) {
    return (
      <View className="items-center justify-center rounded-lg bg-gray-50 p-4">
        <Text className="text-sm text-gray-600">No location history available</Text>
      </View>
    );
  }

  // Calculate map region from location points
  const region = {
    latitude: locations[0]?.lat || initialLocation.latitude,
    longitude: locations[0]?.lng || initialLocation.longitude,
    latitudeDelta: LATITUDE_DELTA,
    longitudeDelta: LONGITUDE_DELTA,
  };

  // Create coordinates array for polyline
  const coordinates = locations.map((loc) => ({
    latitude: loc.lat,
    longitude: loc.lng,
  }));

  return (
    <View className="rounded-lg">
      {/* Map container */}
      <View className="h-64 overflow-hidden rounded-lg">
        <MapView
          ref={mapRef}
          style={{ width: '100%', height: '100%' }}
          initialRegion={region}
          provider={IS_IOS ? undefined : 'google'}
          mapType={IS_IOS ? 'mutedStandard' : 'standard'}>
          {/* Main route line */}
          <Polyline
            coordinates={coordinates}
            strokeColor="#2563eb"
            strokeWidth={3}
            lineDashPattern={IS_IOS ? [1] : undefined}
          />

          {/* Route direction arrows */}
          {locations.map((loc, index) => {
            if (index === 0 || index === locations.length - 1) return null;
            const prevLoc = locations[index - 1];
            const nextLoc = locations[index + 1];

            // Calculate direction arrow coordinates
            const coordinate = {
              latitude: loc.lat,
              longitude: loc.lng,
            };

            return (
              <Polyline
                key={`arrow-${loc.ts}`}
                coordinates={[
                  { latitude: prevLoc.lat, longitude: prevLoc.lng },
                  coordinate,
                  { latitude: nextLoc.lat, longitude: nextLoc.lng },
                ]}
                strokeColor="#60a5fa"
                strokeWidth={2}
                lineDashPattern={[2, 3]}
              />
            );
          })}

          {/* Small dots for all location points */}
          {locations.map((loc) => {
            const coordinate = {
              latitude: loc.lat,
              longitude: loc.lng,
            };

            return (
              <Circle
                key={`dot-${loc.ts}`}
                center={coordinate}
                radius={5}
                strokeWidth={1}
                strokeColor="#2563eb"
                fillColor="#60a5fa"
                // Circle doesn't support onPress directly
              />
            );
          })}

          {/* Transparent markers for location points to handle touch events */}
          {locations.map((loc, index) => {
            // Skip start and end points as they already have markers
            const isStart = index === 0;
            const isEnd = index === locations.length - 1;
            if (isStart || isEnd) return null;

            return (
              <Marker
                key={`touch-${loc.ts}`}
                coordinate={{
                  latitude: loc.lat,
                  longitude: loc.lng,
                }}
                opacity={0}
                onPress={() => setSelectedPoint(loc)}>
                <Callout>
                  <View className="p-1">
                    <Text className="font-medium">Location Point {index}</Text>
                    <Text className="text-xs text-gray-600">Time: {formatTime(loc.ts)}</Text>
                  </View>
                </Callout>
              </Marker>
            );
          })}

          {/* Main location markers (start/end) */}
          {locations.map((loc, index) => {
            const isStart = index === 0;
            const isEnd = index === locations.length - 1;

            // Only show markers for start and end points
            if (!isStart && !isEnd) return null;

            const coordinate = {
              latitude: loc.lat,
              longitude: loc.lng,
            };

            return (
              <Marker
                key={loc.ts}
                coordinate={coordinate}
                pinColor={isStart ? 'green' : 'red'}
                onPress={() => setSelectedPoint(loc)}>
                <Callout>
                  <View className="p-1">
                    <Text className="font-medium">
                      {isStart ? 'Start Point' : 'Current Location'}
                    </Text>
                    <Text className="text-xs text-gray-600">Time: {formatTime(loc.ts)}</Text>
                  </View>
                </Callout>
              </Marker>
            );
          })}
        </MapView>

        {/* Map controls */}
        <View className="absolute bottom-2 right-2">
          <TouchableOpacity
            onPress={fitToCoordinates}
            className="rounded-full bg-white p-2 shadow-sm">
            <MapPin size={20} color="#4b5563" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Location details */}
      <View className="mt-2 rounded-lg bg-white p-3">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Clock size={14} color="#4b5563" />
            <Text className="ml-1 text-xs text-gray-600">{locations.length} points tracked</Text>
          </View>
          <View className="flex-row items-center">
            <Navigation size={14} color="#4b5563" />
            <Text className="ml-1 text-xs text-gray-600">
              {formatTime(locations[0].ts)} - {formatTime(locations[locations.length - 1].ts)}
            </Text>
          </View>
        </View>

        {/* Selected point details */}
        {selectedPoint && (
          <View className="mt-2 rounded-md bg-blue-50 p-2">
            <Text className="text-xs font-medium text-blue-800">Selected Point Details:</Text>
            <Text className="text-xs text-blue-600">Time: {formatTime(selectedPoint.ts)}</Text>
            <Text className="text-xs text-blue-600">
              Location: {selectedPoint.lat.toFixed(6)}, {selectedPoint.lng.toFixed(6)}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}
