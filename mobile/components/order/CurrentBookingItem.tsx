import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box, HStack, VStack, Text } from '@/components/ui';

interface CurrentBookingItemProps {
  booking: {
    service: string;
    garage: string;
    status: string;
    date: string;
    time: string;
    estimatedTime: string;
  };
  onViewDetails?: () => void;
  onGetDirections?: () => void;
}

export const CurrentBookingItem: React.FC<CurrentBookingItemProps> = ({
  booking,
  onViewDetails,
  onGetDirections,
}) => {
  return (
    <Box className="rounded-xl bg-gray-900 p-4">
      <HStack className="items-center justify-between">
        <HStack className="items-center">
          <Box className="h-12 w-12 items-center justify-center rounded-full bg-gray-800">
            <Ionicons name="car" size={24} color="#f59e0b" />
          </Box>
          <VStack className="ml-3">
            <Text className="text-base font-bold text-white">{booking.service}</Text>
            <Text className="text-sm text-gray-300">{booking.garage}</Text>
          </VStack>
        </HStack>
        <Box className="rounded-full bg-gray-800 px-3 py-1">
          <Text className="text-xs font-medium text-yellow-400">{booking.status}</Text>
        </Box>
      </HStack>

      <HStack className="mt-4 justify-between">
        <HStack className="items-center">
          <Ionicons name="calendar" size={18} color="#f59e0b" />
          <Text className="ml-2 text-sm text-gray-300">{booking.date}</Text>
        </HStack>
        <HStack className="items-center">
          <Ionicons name="time" size={18} color="#f59e0b" />
          <Text className="ml-2 text-sm text-gray-300">{booking.time}</Text>
        </HStack>
        <HStack className="items-center">
          <Ionicons name="hourglass" size={18} color="#f59e0b" />
          <Text className="ml-2 text-sm text-gray-300">{booking.estimatedTime}</Text>
        </HStack>
      </HStack>

      <HStack className="mt-4 justify-between">
        <TouchableOpacity
          className="flex-1 items-center rounded-lg bg-gray-800 py-2"
          onPress={onViewDetails}>
          <Text className="font-medium text-gray-300">View Details</Text>
        </TouchableOpacity>
        <Box className="w-4" />
        <TouchableOpacity
          className="flex-1 items-center rounded-lg bg-yellow-500 py-2"
          onPress={onGetDirections}>
          <Text className="font-medium text-gray-900">Get Directions</Text>
        </TouchableOpacity>
      </HStack>
    </Box>
  );
};
