import { Ionicons } from '@expo/vector-icons';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'expo-router';
import {
  Clock,
  Wrench,
  Calendar,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Star,
  MapPin,
  Car,
  User,
} from 'lucide-react-native';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { Box, HStack, VStack } from '@/components/ui';
import { Card } from '@/components/ui/card';

export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'rejected';

interface Order {
  id: string;
  code: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  status: OrderStatus;
  amount: number;
  scheduled_at: string;
  completed_at?: string;
  note?: string;
  order_data?: {
    location?: string;
    vehicle_info?: {
      brand?: string;
      model?: string;
      year?: string;
      license_plate?: string;
    };
    special_requests?: string;
  };
  created_at: string;
  updated_at: string;
  is_paid: boolean;
  technician_rating?: number;
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service?: {
    id: string;
    name: string;
    price: number;
  };
}

interface OrderItemProps {
  order: Order;
  viewType?: 'customer' | 'technician';
}

const StatusBadge = ({ status }: { status: OrderStatus }) => {
  let bgColor = '';
  let textColor = '';
  let iconColor = '';
  let icon = null;

  switch (status) {
    case 'pending':
      bgColor = 'bg-gray-500';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <Clock size={12} color={iconColor} />;
      break;
    case 'confirmed':
      bgColor = 'bg-blue-600';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <Calendar size={12} color={iconColor} />;
      break;
    case 'in_progress':
      bgColor = 'bg-amber-500';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <Wrench size={12} color={iconColor} />;
      break;
    case 'completed':
      bgColor = 'bg-green-600';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <CheckCircle2 size={12} color={iconColor} />;
      break;
    case 'cancelled':
    case 'rejected':
      bgColor = 'bg-red-600';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <XCircle size={12} color={iconColor} />;
      break;
    default:
      bgColor = 'bg-gray-400';
      textColor = 'text-white';
      iconColor = '#ffffff';
      icon = <AlertCircle size={12} color={iconColor} />;
  }

  return (
    <View className={`${bgColor} rounded-md px-3 py-1.5`}>
      <HStack space="xs" className="items-center">
        <Text className={`text-xs font-bold ${textColor} uppercase tracking-wider`}>
          {status.replace('_', ' ')}
        </Text>
        {icon}
      </HStack>
    </View>
  );
};

const RatingDisplay = ({ rating }: { rating?: number }) => {
  if (!rating) return null;

  const getRatingColor = () => {
    if (rating >= 4.0) return '#16a34a'; // Green for good ratings
    if (rating >= 3.0) return '#f59e0b'; // Amber for average
    return '#dc2626'; // Red for poor ratings
  };

  const starColor = getRatingColor();

  return (
    <HStack space="xs" className="items-center">
      <Star size={12} color={starColor} fill={starColor} />
      <Text className="text-xs font-bold text-gray-900">{rating.toFixed(1)}</Text>
    </HStack>
  );
};

const formatRelativeTime = (dateString: string) => {
  try {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  } catch {
    return 'Invalid date';
  }
};

const formatPrice = (amount: number) => {
  return `$${amount.toFixed(2)}`;
};

export const OrderItem: React.FC<OrderItemProps> = ({ order, viewType = 'customer' }) => {
  const router = useRouter();
  const vehicleInfo = order.order_data?.vehicle_info;
  const vehicleText = vehicleInfo
    ? `${vehicleInfo.brand || ''} ${vehicleInfo.model || ''} ${vehicleInfo.year || ''}`.trim()
    : 'No vehicle info';

  const handlePress = () => {
    if (viewType === 'customer') {
      router.push(`/customer/order/${order.id}`);
    } else {
      router.push(`/technician/order/${order.id}`);
    }
  };

  // Get card background color based on status - using scientific color theory
  const getCardBgColor = () => {
    switch (order.status) {
      case 'pending':
        return 'bg-white'; // Clean white for waiting state
      case 'confirmed':
        return 'bg-blue-50'; // Light blue for confirmed
      case 'in_progress':
        return 'bg-amber-50'; // Light amber for active
      case 'completed':
        return 'bg-green-50'; // Light green for success
      case 'cancelled':
      case 'rejected':
        return 'bg-red-50'; // Light red for error
      default:
        return 'bg-gray-50';
    }
  };

  // Get border color based on status - scientific approach with left accent
  const getCardBorderColor = () => {
    switch (order.status) {
      case 'pending':
        return 'border border-slate-400'; // Neutral accent
      case 'confirmed':
        return 'border border-blue-500'; // Trust and confirmation
      case 'in_progress':
        return 'border border-amber-500'; // Active/warning
      case 'completed':
        return 'border border-green-500'; // Success
      case 'cancelled':
      case 'rejected':
        return 'border border-red-500'; // Error/danger
      default:
        return 'border border-gray-300';
    }
  };

  // Get text color synchronized with card status colors
  const getTextColor = (type: 'primary' | 'secondary') => {
    switch (order.status) {
      case 'pending':
        return type === 'primary' ? 'text-gray-900' : 'text-gray-600';
      case 'confirmed':
        return type === 'primary' ? 'text-blue-900' : 'text-blue-700';
      case 'in_progress':
        return type === 'primary' ? 'text-amber-900' : 'text-amber-700';
      case 'completed':
        return type === 'primary' ? 'text-green-900' : 'text-green-700';
      case 'cancelled':
      case 'rejected':
        return type === 'primary' ? 'text-red-900' : 'text-red-700';
      default:
        return type === 'primary' ? 'text-gray-900' : 'text-gray-700';
    }
  };

  // Get icon color synchronized with text colors
  const getIconColor = () => {
    switch (order.status) {
      case 'pending':
        return '#6b7280'; // gray-500
      case 'confirmed':
        return '#2563eb'; // blue-600
      case 'in_progress':
        return '#f59e0b'; // amber-500
      case 'completed':
        return '#16a34a'; // green-600
      case 'cancelled':
      case 'rejected':
        return '#dc2626'; // red-600
      default:
        return '#9ca3af'; // gray-400
    }
  };

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handlePress}>
      <Card className={`overflow-hidden rounded-lg ${getCardBorderColor()} ${getCardBgColor()}`}>
        {/* Header Section */}
        <Box className="bg-transparent px-3 py-2">
          <HStack className="items-center justify-between">
            <HStack space="xs" className="items-center">
              <View className={`pr-1`}>
                <Ionicons name="receipt-outline" size={16} color={getIconColor()} />
              </View>
              <Text className={`text-sm font-bold ${getTextColor('primary')}`}>{order.code}</Text>
            </HStack>
            <StatusBadge status={order.status} />
          </HStack>
        </Box>

        {/* Content Section */}
        <Box className="bg-transparent px-3 py-2">
          {/* Service Name */}
          <Text className={`mb-2 text-base font-bold ${getTextColor('primary')}`}>
            {order.service?.name || 'Unknown Service'}
          </Text>

          {/* Info Grid */}
          <VStack space="xs">
            {/* Person Info */}
            {viewType === 'customer' && order.technician && (
              <HStack className="items-center justify-between">
                <HStack space="xs" className="flex-1 items-center">
                  <User size={14} color={getIconColor()} />
                  <Text className={`text-xs ${getTextColor('secondary')}`}>
                    {order.technician?.name || 'Unassigned'}
                  </Text>
                </HStack>
                {order.technician_rating && <RatingDisplay rating={order.technician_rating} />}
              </HStack>
            )}
            {viewType === 'technician' && order.customer && (
              <HStack space="xs" className="items-center">
                <User size={14} color={getIconColor()} />
                <Text className={`text-xs ${getTextColor('secondary')}`}>
                  {order.customer?.name || 'Unknown'}
                </Text>
              </HStack>
            )}

            {/* Vehicle Info */}
            <HStack space="xs" className="items-center">
              <Car size={14} color={getIconColor()} />
              <Text className={`text-xs ${getTextColor('secondary')}`}>{vehicleText}</Text>
              {vehicleInfo?.license_plate && (
                <View className="ml-auto rounded-md bg-gray-200 px-2 py-0.5">
                  <Text className="font-mono text-xs font-bold text-gray-800">
                    {vehicleInfo.license_plate}
                  </Text>
                </View>
              )}
            </HStack>

            {/* Location */}
            {order.order_data?.location && (
              <HStack space="xs" className="items-start">
                <MapPin size={14} color={getIconColor()} className="mt-0.5" />
                <Text className={`flex-1 text-xs ${getTextColor('secondary')}`}>
                  {order.order_data.location}
                </Text>
              </HStack>
            )}

            {/* Schedule */}
            <HStack space="xs" className="items-center">
              <Clock size={14} color={getIconColor()} />
              <Text className={`text-xs ${getTextColor('secondary')}`}>
                {formatRelativeTime(order.scheduled_at)}
              </Text>
            </HStack>
          </VStack>
        </Box>

        {/* Footer Section */}
        <Box className={`border-t border-gray-200 px-3 py-2`}>
          <HStack className="items-center justify-between">
            <HStack space="xs" className="items-center">
              <Text className={`text-xs font-medium ${getTextColor('secondary')}`}>Total</Text>
              {order.is_paid && (
                <View className="rounded-md bg-green-600 px-2 py-0.5">
                  <Text className="text-xs font-bold text-white">Paid</Text>
                </View>
              )}
            </HStack>
            <Text className={`text-xl font-black ${getTextColor('primary')}`}>
              {formatPrice(order.amount)}
            </Text>
          </HStack>
        </Box>
      </Card>
    </TouchableOpacity>
  );
};
