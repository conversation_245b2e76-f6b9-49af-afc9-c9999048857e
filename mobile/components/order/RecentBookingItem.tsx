import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box, HStack, VStack, Text } from '@/components/ui';

interface RecentBookingItemProps {
  booking: {
    service: string;
    garage: string;
    date: string;
    price: string;
  };
  onPress?: () => void;
}

export const RecentBookingItem: React.FC<RecentBookingItemProps> = ({ booking, onPress }) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <Box className="rounded-lg bg-gray-900 p-3">
        <HStack className="items-center justify-between">
          <HStack className="items-center">
            <Box className="h-10 w-10 items-center justify-center rounded-full bg-gray-800">
              <Ionicons name="car" size={20} color="#f59e0b" />
            </Box>
            <VStack className="ml-3">
              <Text className="text-sm font-semibold text-white">{booking.service}</Text>
              <Text className="text-xs text-gray-400">{booking.garage}</Text>
            </VStack>
          </HStack>
          <VStack className="items-end">
            <Text className="text-sm font-semibold text-white">{booking.price}</Text>
            <Text className="text-xs text-gray-400">{booking.date}</Text>
          </VStack>
        </HStack>
      </Box>
    </TouchableOpacity>
  );
};
