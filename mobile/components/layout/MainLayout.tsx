import { Menu, X, LogOut } from 'lucide-react-native';
import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  Alert,
  StatusBar,
  ScrollView,
  RefreshControl,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { BookNowButton } from '@/components/common/BookNowButton';
import { Box, HStack, Text, VStack } from '@/components/ui';
import {
  Drawer,
  DrawerBackdrop,
  DrawerContent,
  DrawerBody,
  DrawerHeader,
  DrawerCloseButton,
  DrawerFooter,
} from '@/components/ui/drawer';
import { useAuth } from '@/core/contexts/AuthContext';
import { routes } from '@/navigation/routes';
import LogoSVG from '~/assets/svg/Logo';
import LogoFullSVG from '~/assets/svg/LogoFull';
import { useAppType } from '~/core/contexts/AppTypeContext';

interface ScrollOptions {
  refreshing?: boolean;
  onRefresh?: () => void;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: object;
}

interface MainLayoutProps {
  children: React.ReactNode;
  scrollable?: boolean;
  scrollOptions?: ScrollOptions;
  headerShown?: boolean;
  backgroundColor?: string;
  keyboardAvoidingEnabled?: boolean;
}

export function MainLayout({
  children,
  scrollable = true,
  scrollOptions = {},
  headerShown = true,
  backgroundColor = 'white',
  keyboardAvoidingEnabled = true,
}: MainLayoutProps) {
  const { isCustomer } = useAppType();
  const insets = useSafeAreaInsets();
  const { user, logout } = useAuth();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleOpenDrawer = () => {
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
      handleCloseDrawer();
    } catch (error) {
      console.error('Logout failed:', error);
      Alert.alert('Logout Error', 'Failed to logout. Please try again.');
    }
  };

  const {
    refreshing = false,
    onRefresh,
    showsVerticalScrollIndicator = false,
    contentContainerStyle = {},
  } = scrollOptions;

  // Get greeting based on time of day
  const getGreeting = () => {
    const hours = new Date().getHours();
    if (hours < 12) return 'Good morning';
    if (hours < 18) return 'Good afternoon';
    return 'Good evening';
  };

  // Get user role display
  const getUserRoleDisplay = () => {
    if (!user) return '';

    switch (user.role) {
      case 'admin':
        return 'Admin Account';
      case 'technician':
        return 'Technician Account';
      case 'customer':
      default:
        return 'Customer Account';
    }
  };

  const isAdmin = user?.role === 'admin';

  // Different colors for customer vs technician
  const headerBgClass = isCustomer ? 'bg-gray-900' : 'bg-blue-900';
  const statusBarColor = isCustomer ? '#111827' : '#1e3a8a';
  const borderClass = isCustomer ? 'border-gray-600' : 'border-blue-600';

  const Content = () => (
    <>
      <StatusBar barStyle="light-content" backgroundColor={statusBarColor} />
      <Box className={`relative flex-1 ${headerBgClass}`}>
        {/* Header with hamburger menu and Book Now button */}
        {headerShown && (
          <Box style={{ paddingTop: insets.top }} className={`border-b-4 ${borderClass}`}>
            <HStack className="items-center justify-between px-6 pb-4 pt-2">
              {/* Logo */}
              <HStack className="flex-1">
                <LogoFullSVG width={130} height={30} />
              </HStack>
              <HStack className="items-center gap-4">
                {/* Book Now button */}
                <BookNowButton className="ml-2" phoneNumber="0987654321" />

                {/* Hamburger Menu */}
                <TouchableOpacity onPress={handleOpenDrawer}>
                  <Menu color="#FFFFFF" size={24} />
                </TouchableOpacity>
              </HStack>
            </HStack>
          </Box>
        )}

        {/* Main content */}
        {scrollable ? (
          <ScrollView
            className={`flex-1 bg-${backgroundColor}`}
            showsVerticalScrollIndicator={showsVerticalScrollIndicator}
            keyboardShouldPersistTaps="handled"
            refreshControl={
              onRefresh ? (
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
              ) : undefined
            }
            contentContainerStyle={{
              paddingBottom: insets.bottom,
              flexGrow: 1,
              ...contentContainerStyle,
            }}>
            {children}
          </ScrollView>
        ) : (
          <View className={`flex-1 bg-${backgroundColor}`} style={{ paddingBottom: insets.bottom }}>
            {children}
          </View>
        )}

        {/* Drawer */}
        <Drawer isOpen={isDrawerOpen} onClose={handleCloseDrawer} size="lg" anchor="right">
          <DrawerBackdrop />
          <DrawerContent
            className={`border-none ${headerBgClass}`}
            style={{
              paddingTop: insets.top,
              paddingBottom: insets.bottom,
              paddingRight: insets.right,
              paddingLeft: insets.left,
            }}>
            <DrawerHeader
              className={`h-16 flex-row items-center justify-between gap-4 border-b-4 ${borderClass} px-4 pb-4 pt-2`}>
              <HStack className="flex-1 items-center justify-between gap-2 ">
                <LogoSVG width={40} height={30} />
                <VStack className="flex-1 border-l border-gray-100 pl-2">
                  <Text className="text-sm italic text-white">{getGreeting()},</Text>
                  <Text className="text-sm font-bold text-yellow-300">
                    {user?.name || 'Guest'}
                    {isAdmin && ' (Admin)'}
                  </Text>
                </VStack>
              </HStack>
              <DrawerCloseButton>
                <X size={24} color="#facc15" />
              </DrawerCloseButton>
            </DrawerHeader>
            <DrawerBody className="m-0 px-4">
              {/* Drawer menu items */}
              <VStack className="mt-4 flex-1 space-y-1">
                {routes(isCustomer).map((route, index) => {
                  const Icon = route.icon;
                  return (
                    <TouchableOpacity
                      key={index}
                      className="flex-row items-center rounded-lg px-3 py-4 hover:bg-gray-800 active:bg-gray-800"
                      onPress={() => route.onPress(handleCloseDrawer)}>
                      <Icon size={16} color="#FFFFFF" />
                      <Text className="ml-4 font-medium text-white">{route.name}</Text>
                    </TouchableOpacity>
                  );
                })}

                <Box className="my-2 h-px bg-gray-700" />

                <TouchableOpacity
                  className="flex-row items-center rounded-lg px-3 py-4 hover:bg-gray-800"
                  onPress={() => {
                    Alert.alert('Logout', 'Are you sure you want to logout?', [
                      {
                        text: 'Cancel',
                        style: 'cancel',
                      },
                      {
                        text: 'Logout',
                        onPress: handleLogout,
                      },
                    ]);
                  }}>
                  <LogOut size={16} color="#FF5252" />
                  <Text className="ml-4 font-medium text-red-400">Logout</Text>
                </TouchableOpacity>
              </VStack>
            </DrawerBody>
            <DrawerFooter className="justify-start border-t border-gray-800 px-4 pt-4">
              {/* App Version Footer */}
              <Text className="ml-2 text-xs text-gray-500">Version 1.0.0</Text>
              {user && <Text className="ml-2 text-xs text-gray-500">{getUserRoleDisplay()}</Text>}
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </Box>
    </>
  );

  if (keyboardAvoidingEnabled) {
    return (
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}>
        <Content />
      </KeyboardAvoidingView>
    );
  }

  return <Content />;
}
