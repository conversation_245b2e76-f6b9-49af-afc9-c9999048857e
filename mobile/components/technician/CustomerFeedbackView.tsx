import { formatDistanceToNow } from 'date-fns';
import { Star, Calendar } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';

import { RatingDisplay } from '@/components/customer/RatingDisplay';
import { supabase } from '@/lib/supabase';

interface CustomerFeedbackViewProps {
  feedbackId: string;
}

// Section header component
const SectionHeader = ({ icon, title }: { icon: React.ReactNode; title: string }) => (
  <View className="mb-2 flex-row items-center justify-between">
    <View className="flex-row items-center">
      <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-sky-50">{icon}</View>
      <Text className="text-sm font-semibold text-gray-800">{title}</Text>
    </View>
  </View>
);

interface Feedback {
  id: string;
  order_id: string;
  technician_id: string;
  customer_id: string;
  rate: number;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export const CustomerFeedbackView: React.FC<CustomerFeedbackViewProps> = ({ feedbackId }) => {
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeedback();
  }, [feedbackId]);

  const fetchFeedback = async () => {
    if (!feedbackId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('feedback')
        .select('*')
        .eq('id', feedbackId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      setFeedback(data as Feedback);
    } catch (error) {
      console.error('Error fetching feedback:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
        <View className="items-center justify-center py-4">
          <ActivityIndicator size="small" color="#0284c7" />
          <Text className="mt-2 text-gray-500">Loading feedback...</Text>
        </View>
      </View>
    );
  }

  if (!feedback) {
    return (
      <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
        <Text className="text-center text-gray-500">Feedback not found</Text>
      </View>
    );
  }

  return (
    <View className="rounded-md border border-gray-200 bg-white p-3 shadow-sm">
      <SectionHeader icon={<Star size={14} color="#0369a1" />} title="Customer Feedback" />

      {/* Rating display with improved alignment */}
      <View className="mb-3 flex-row items-center justify-center py-1">
        <RatingDisplay rating={feedback.rate} size={24} showValue />
      </View>

      {/* Comment section with proper spacing and styling */}
      {feedback.description && (
        <View className="mb-3">
          <Text className="mb-1 text-xs text-gray-500">Customer Comments</Text>
          <View className="rounded-md bg-gray-50 px-3 py-2">
            <Text className="text-sm text-gray-800">{feedback.description}</Text>
          </View>
        </View>
      )}

      {/* Timestamp with improved styling */}
      <View className="flex-row items-center justify-end">
        <Calendar size={12} color="#6b7280" />
        <Text className="ml-1 text-xs text-gray-500">
          {formatDistanceToNow(new Date(feedback.created_at), { addSuffix: true })}
        </Text>
      </View>
    </View>
  );
};
