import { StripeProvider as BaseStripeProvider } from '@stripe/stripe-react-native';
import React from 'react';

interface StripeProviderProps {
  children: React.ReactElement | React.ReactElement[];
}

// Get the publishable key from environment variables
const STRIPE_PUBLISHABLE_KEY = process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_...'; // Replace with your actual key

export default function StripeProvider({ children }: StripeProviderProps) {
  return (
    <BaseStripeProvider
      publishableKey={STRIPE_PUBLISHABLE_KEY}
      merchantIdentifier="merchant.vtech.autonation" // For Apple Pay
      urlScheme="vtech" // For return URLs
    >
      {children}
    </BaseStripeProvider>
  );
}
