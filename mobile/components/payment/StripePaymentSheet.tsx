import { useStripe } from '@stripe/stripe-react-native';
import axios from 'axios';
import { CreditCard } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, Platform, View, Text, TouchableOpacity } from 'react-native';

interface StripePaymentSheetProps {
  orderId: string;
  amount: number;
  onPaymentSuccess?: () => void;
  onPaymentError?: (error: string) => void;
}

export default function StripePaymentSheet({
  orderId,
  amount,
  onPaymentSuccess,
  onPaymentError,
}: StripePaymentSheetProps) {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [loading, setLoading] = useState(false);

  const createPaymentIntent = async () => {
    try {
      console.log('🚀 Creating payment intent for order:', orderId, 'amount:', amount);
      console.log('📱 Platform:', Platform.OS);

      const API_URL = process.env.EXPO_PUBLIC_API_URL || 'https://api.vtechautonation.com';
      console.log('🌐 API URL:', API_URL);

      const requestData = {
        orderId,
        amount: Math.round(amount * 100), // Convert to cents
      };
      console.log('📦 Request data:', requestData);

      // Special configuration for Android
      const axiosConfig = {
        timeout: 15000, // 15 second timeout
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `VTech-Mobile-${Platform.OS}`,
          Accept: 'application/json',
        },
      };

      // For Android emulator, use ******** if localhost
      let apiUrl = API_URL;
      if (Platform.OS === 'android' && API_URL.includes('localhost')) {
        apiUrl = API_URL.replace('localhost', '********');
        console.log('🤖 Android: Modified API URL to:', apiUrl);
      }

      const response = await axios.post(
        `${apiUrl}/api/payment/create-intent`,
        requestData,
        axiosConfig
      );

      console.log('✅ Payment intent created successfully:', response.data);

      const { clientSecret, paymentIntentId } = response.data;
      return { clientSecret, paymentIntentId };
    } catch (error) {
      console.error('❌ Error creating payment intent:', error);

      if (axios.isAxiosError(error)) {
        console.log('📊 Error details:');
        console.log('- Status:', error.response?.status);
        console.log('- Status text:', error.response?.statusText);
        console.log('- Data:', error.response?.data);
        console.log('- Headers:', error.response?.headers);
        console.log('- Config URL:', error.config?.url);
        console.log('- Network error:', error.code);

        const errorMessage = error.response?.data?.error || error.message || 'Network error';
        throw new Error(errorMessage);
      }
      throw error;
    }
  };

  const confirmPayment = async (paymentIntentId: string) => {
    try {
      const API_URL = process.env.EXPO_PUBLIC_API_URL || 'https://api.vtechautonation.com';

      const response = await axios.post(`${API_URL}/api/payment/confirm`, {
        paymentIntentId,
        orderId,
      });

      return response.data;
    } catch (error) {
      console.error('Error confirming payment:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error || error.message;
        throw new Error(errorMessage);
      }
      throw error;
    }
  };

  const handlePayment = async () => {
    try {
      setLoading(true);

      // Step 1: Create payment intent
      const { clientSecret, paymentIntentId } = await createPaymentIntent();

      // Step 2: Initialize payment sheet
      const { error: initError } = await initPaymentSheet({
        merchantDisplayName: 'VTECH AUTONATION LLC',
        paymentIntentClientSecret: clientSecret,
        defaultBillingDetails: {
          name: 'Customer',
        },
        allowsDelayedPaymentMethods: false,
        returnURL: 'vtech://payment-return',
        applePay: {
          merchantCountryCode: 'US',
        },
        googlePay: {
          merchantCountryCode: 'US',
          testEnv: true, // Set to false for production
        },
      });

      if (initError) {
        throw new Error(initError.message);
      }

      // Step 3: Present payment sheet
      const { error: presentError } = await presentPaymentSheet();

      if (presentError) {
        if (presentError.code === 'Canceled') {
          // User canceled, not an error
          return;
        }
        throw new Error(presentError.message);
      }

      // Step 4: Confirm payment on backend
      await confirmPayment(paymentIntentId);

      // Payment successful
      Alert.alert(
        'Payment Successful',
        `Your payment of $${amount.toFixed(2)} has been processed successfully.`,
        [{ text: 'OK', onPress: onPaymentSuccess }]
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      Alert.alert('Payment Error', errorMessage);
      onPaymentError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="rounded-md border border-gray-200 bg-white p-3">
      <View className="mb-3 flex-row items-center">
        <View className="mr-2 h-6 w-6 items-center justify-center rounded-md bg-green-50">
          <CreditCard size={14} color="#059669" />
        </View>
        <Text className="text-sm font-semibold text-gray-800">Payment</Text>
      </View>

      <View className="mb-3">
        <Text className="mb-1 text-xs text-gray-500">Amount Due</Text>
        <Text className="text-lg font-bold text-gray-900">${amount.toFixed(2)}</Text>
      </View>

      <TouchableOpacity
        onPress={handlePayment}
        disabled={loading}
        className={`flex-row items-center justify-center rounded-md px-4 py-3 ${
          loading ? 'bg-gray-400' : 'bg-green-600'
        }`}
        accessibilityLabel={`Pay ${amount.toFixed(2)} dollars`}>
        <CreditCard size={16} color="white" />
        <Text className="ml-2 font-medium text-white">
          {loading ? 'Processing...' : `Pay $${amount.toFixed(2)}`}
        </Text>
      </TouchableOpacity>

      <Text className="mt-2 text-center text-xs text-gray-500">
        Secure payment powered by Stripe
      </Text>
    </View>
  );
}
