import { router } from 'expo-router';
import { Home, User, CalendarClock, MessageSquare, Bot, ClipboardList } from 'lucide-react-native';

// Define route configuration
export const routes = (isCustomer: boolean) => [
  {
    name: 'Home',
    icon: Home,
    onPress: (closeDrawer: () => void) => {
      router.push('/main');
      closeDrawer();
    },
  },
  {
    name: 'Notifications',
    icon: CalendarClock,
    onPress: (closeDrawer: () => void) => {
      router.push('/main/notification');
      closeDrawer();
    },
  },
  // {
  //   name: 'Profile',
  //   icon: User,
  //   onPress: (closeDrawer: () => void) => {
  //     router.push('/main/profile');
  //     closeDrawer();
  //   },
  // },
  {
    name: 'Service Center',
    icon: MessageSquare,
    onPress: (closeDrawer: () => void) => {
      router.push('/main/chat');
      closeDrawer();
    },
  },
  {
    name: 'AI Assistant',
    icon: Bo<PERSON>,
    onPress: (closeDrawer: () => void) => {
      router.push('/main/assistant');
      closeDrawer();
    },
  },
  {
    name: 'Account',
    icon: User,
    onPress: (closeDrawer: () => void) => {
      router.push('/main/account');
      closeDrawer();
    },
  },
  isCustomer
    ? {
        name: 'My Bookings',
        icon: ClipboardList,
        onPress: (closeDrawer: () => void) => {
          router.push('/customer/order-history');
          closeDrawer();
        },
      }
    : {
        name: 'My Orders',
        icon: ClipboardList,
        onPress: (closeDrawer: () => void) => {
          router.push('/technician/order-history');
          closeDrawer();
        },
      },
];
