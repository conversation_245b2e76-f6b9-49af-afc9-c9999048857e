import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  formattedContent?: string;
}

const getApiKey = () => {
  return process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';
};

// System prompt for the automotive expert assistant
const AUTOMOTIVE_EXPERT_PROMPT = `You are vA<PERSON>, an automotive technology expert specializing in all aspects of cars, vehicles, and automotive engineering. 

Your knowledge covers:
- Car mechanics, parts, and systems
- Vehicle maintenance and troubleshooting
- Car models, manufacturers, and specifications
- Automotive technology trends and innovations
- Driving techniques and vehicle safety
- Electric and alternative fuel vehicles

Important rules:
1. Only answer questions related to cars and automotive topics
2. For any questions outside automotive expertise, politely respond with: "I'm sorry, I can only assist with automotive-related questions."
3. Provide accurate, technical, and helpful information
4. Be concise and direct in your responses
5. Use effective Markdown formatting to enhance readability:
   - Use **bold** for key terms, important parts, and component names
   - Use *italic* for emphasis or technical terms when first introduced
   - Use bullet lists (- item) for related points or features
   - Use numbered lists (1. step) for sequential steps or procedures
   - Use ## or ### for section headings to organize longer responses
   - Use > for important notes or warnings about safety
   - Use \`code\` for specific values, measurements, or part numbers
   - Use tables for comparing specifications or models
   - Break lengthy explanations into clear sections with headings
   - Use line breaks efficiently - don't make paragraphs too long

Please respond in the same language the user used to ask their question.`;

// Constants for caching
const CONVERSATION_CACHE_KEY = 'ai_conversation_cache';
const MAX_RETRY_ATTEMPTS = 2;
const RETRY_DELAY = 1000; // 1 second

class AIAssistant {
  private apiKey: string;
  private genAI: GoogleGenerativeAI | null = null;
  private model: any;
  private currentConversation: ChatMessage[];
  private isInitialized: boolean = false;
  private initError: Error | null = null;

  constructor() {
    this.apiKey = getApiKey();
    this.currentConversation = [];
    this.initializeAI();
  }

  private async initializeAI() {
    try {
      if (!this.apiKey) {
        throw new Error('AI API key not configured');
      }

      this.genAI = new GoogleGenerativeAI(this.apiKey);
      this.model = this.genAI.getGenerativeModel({
        model: 'gemini-2.0-flash',
        systemInstruction: {
          role: 'system',
          parts: [
            {
              text: AUTOMOTIVE_EXPERT_PROMPT,
            },
          ],
        },
      });

      // Restore conversation from cache
      await this.loadConversationFromCache();

      this.isInitialized = true;
    } catch (error) {
      this.initError = error instanceof Error ? error : new Error('AI initialization failed');
      this.isInitialized = false;
    }
  }

  private async loadConversationFromCache() {
    try {
      const cachedConversation = await AsyncStorage.getItem(CONVERSATION_CACHE_KEY);
      if (cachedConversation) {
        this.currentConversation = JSON.parse(cachedConversation);
      }
    } catch {
      this.currentConversation = [];
    }
  }

  private async saveConversationToCache() {
    try {
      await AsyncStorage.setItem(CONVERSATION_CACHE_KEY, JSON.stringify(this.currentConversation));
    } catch {
      // Silent fail
    }
  }

  async sendMessage(
    message: string,
    history: ChatMessage[] = [],
    retryCount = 0
  ): Promise<{ text: string; formatted?: string }> {
    // Check initialization
    if (!this.isInitialized) {
      if (this.initError) {
        throw this.initError;
      }

      // Try to reinitialize
      await this.initializeAI();
      if (!this.isInitialized) {
        throw new Error('AI assistant not initialized');
      }
    }

    if (!this.apiKey || !this.genAI || !this.model) {
      throw new Error('AI assistant configuration error');
    }

    try {
      // Update conversation history
      this.currentConversation = [...history];

      // Add the new user message
      this.currentConversation.push({
        role: 'user',
        content: message,
      });

      // Format history for Gemini API
      const formattedHistory = this.currentConversation.map((msg) => ({
        role: msg.role === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }],
      }));

      // Configuration for the model
      const generationConfig = {
        temperature: 0.7,
        topP: 0.95,
        topK: 64,
        maxOutputTokens: 1024,
      };

      // Safety settings
      const safetySettings = [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ];

      // Ensure history starts with a user message
      let chatHistory: { role: string; parts: { text: string }[] }[] = [];
      if (formattedHistory.length > 1) {
        // Find the first user message in history
        const firstUserIndex = formattedHistory.findIndex((msg) => msg.role === 'user');
        if (firstUserIndex > 0) {
          // If first message is not from user, start from the first user message
          chatHistory = formattedHistory.slice(firstUserIndex, -1);
        } else {
          chatHistory = formattedHistory.slice(0, -1);
        }
      }

      // Create a chat session with history
      const chatSession = this.model.startChat({
        generationConfig,
        safetySettings,
        history: chatHistory,
      });

      // Send the message
      const result = await chatSession.sendMessage(message);
      const aiResponse = (result.response.text() || 'No response available.').trim();

      // Process response for formatting
      const processedResponse = this.processFormattedResponse(aiResponse);

      // Add the AI response to conversation history
      this.currentConversation.push({
        role: 'assistant',
        content: processedResponse.text,
        formattedContent: processedResponse.formatted,
      });

      // Save conversation to cache
      await this.saveConversationToCache();

      return processedResponse;
    } catch {
      if (retryCount < MAX_RETRY_ATTEMPTS) {
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
        return this.sendMessage(message, history, retryCount + 1);
      }

      return {
        text: 'Sorry, the AI service is temporarily unavailable.',
        formatted: 'Sorry, the AI service is temporarily unavailable.',
      };
    }
  }

  private processFormattedResponse(response: string): { text: string; formatted: string } {
    // For plain text version, we'll keep the original response
    const text = response;

    // For the formatted version, apply some processing to improve markdown rendering
    let formatted = response;

    // Ensure proper spacing for lists to render correctly
    formatted = formatted.replace(/^(\s*[-*])\s+/gm, '$1 ');
    formatted = formatted.replace(/^(\s*\d+\.)\s+/gm, '$1 ');

    // Ensure headings have space before and after
    formatted = formatted.replace(/^(#{1,3})\s*(.*?)$/gm, '\n$1 $2\n');

    // Ensure blockquotes have proper spacing
    formatted = formatted.replace(/^>\s*(.*?)$/gm, '> $1\n');

    // Better code block formatting
    formatted = formatted.replace(/```([a-z]*)\n/g, '```$1\n');
    formatted = formatted.replace(/\n```/g, '\n```\n');

    // Ensure horizontal rules have space around them
    formatted = formatted.replace(/\n---\n/g, '\n\n---\n\n');

    // Remove excessive newlines (more than 2 consecutive)
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    return {
      text,
      formatted,
    };
  }

  async clearConversation(): Promise<void> {
    this.currentConversation = [];

    try {
      // Clear the cache
      await AsyncStorage.removeItem(CONVERSATION_CACHE_KEY);
    } catch {
      // Silent fail
    }
  }
}

// Create singleton instance
const aiAssistant = new AIAssistant();

export const sendMessageToAI = async (
  message: string,
  history: ChatMessage[] = []
): Promise<{ text: string; formatted?: string }> => {
  try {
    return await aiAssistant.sendMessage(message, history);
  } catch {
    return {
      text: 'AI service is temporarily unavailable.',
      formatted: 'AI service is temporarily unavailable.',
    };
  }
};

export const clearAIConversation = async (): Promise<void> => {
  await aiAssistant.clearConversation();
};
