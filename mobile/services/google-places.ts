import axios from 'axios';
import Constants from 'expo-constants';

const GOOGLE_PLACES_API_KEY =
  process.env.EXPO_PUBLIC_GOOGLE_PLACES_API_KEY ||
  Constants.expoConfig?.extra?.GOOGLE_PLACES_API_KEY;

export interface PlaceAutocompletePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
    main_text_matched_substrings?: Array<{
      offset: number;
      length: number;
    }>;
  };
  matched_substrings?: Array<{
    offset: number;
    length: number;
  }>;
  terms: Array<{
    offset: number;
    value: string;
  }>;
  types?: string[];
}

export interface PlaceDetails {
  place_id: string;
  formatted_address: string;
  name?: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  address_components?: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
}

export interface PlaceAutocompleteParams {
  input: string;
  location?: { lat: number; lng: number };
  radius?: number;
  components?: string;
  types?: string;
  language?: string;
  sessiontoken?: string;
}

class GooglePlacesService {
  private sessionToken: string | null = null;
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/place';

  constructor() {
    this.generateSessionToken();
  }

  private generateSessionToken() {
    this.sessionToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  async autocomplete(params: PlaceAutocompleteParams): Promise<PlaceAutocompletePrediction[]> {
    try {
      const queryParams = new URLSearchParams({
        input: params.input,
        key: GOOGLE_PLACES_API_KEY || '',
        sessiontoken: params.sessiontoken || this.sessionToken || '',
        language: params.language || 'en',
      });

      if (params.location) {
        queryParams.append('location', `${params.location.lat},${params.location.lng}`);
      }

      if (params.radius) {
        queryParams.append('radius', params.radius.toString());
      }

      if (params.components) {
        queryParams.append('components', params.components);
      }

      if (params.types) {
        queryParams.append('types', params.types);
      }

      const response = await axios.get(
        `${this.baseUrl}/autocomplete/json?${queryParams.toString()}`
      );

      if (response.data.status === 'OK' || response.data.status === 'ZERO_RESULTS') {
        return response.data.predictions || [];
      } else {
        console.error('Google Places API error:', response.data.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching place predictions:', error);
      return [];
    }
  }

  async getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
    try {
      const queryParams = new URLSearchParams({
        place_id: placeId,
        key: GOOGLE_PLACES_API_KEY || '',
        fields: 'place_id,formatted_address,name,geometry,address_components',
        sessiontoken: this.sessionToken || '',
      });

      const response = await axios.get(`${this.baseUrl}/details/json?${queryParams.toString()}`);

      // Reset session token after place details request
      this.generateSessionToken();

      if (response.data.status === 'OK') {
        return response.data.result;
      } else {
        console.error('Google Places API error:', response.data.status);
        return null;
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  }

  async reverseGeocode(lat: number, lng: number): Promise<PlaceAutocompletePrediction[]> {
    try {
      const queryParams = new URLSearchParams({
        latlng: `${lat},${lng}`,
        key: GOOGLE_PLACES_API_KEY || '',
        language: 'en',
      });

      const response = await axios.get(
        `https://maps.googleapis.com/maps/api/geocode/json?${queryParams.toString()}`
      );

      if (response.data.status === 'OK') {
        return response.data.results.map((result: any) => ({
          place_id: result.place_id,
          description: result.formatted_address,
          structured_formatting: {
            main_text: result.formatted_address.split(',')[0] || '',
            secondary_text: result.formatted_address.split(',').slice(1).join(',').trim() || '',
          },
          types: result.types,
        }));
      } else {
        console.error('Google Geocoding API error:', response.data.status);
        return [];
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return [];
    }
  }

  resetSessionToken() {
    this.generateSessionToken();
  }
}

export const googlePlacesService = new GooglePlacesService();
