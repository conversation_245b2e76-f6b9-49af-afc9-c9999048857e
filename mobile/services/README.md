# AI Assistant Service

This service provides chat functionality using Google's Gemini AI API.

## Setup

1. Create a `.env` file in the root of the project based on `.env.example`
2. Add your Gemini API key to the `.env` file:
   ```
   EXPO_PUBLIC_GEMINI_API_KEY=your_actual_api_key_here
   ```
3. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn
   ```

## How to Use

The service is used automatically in the chat screen when in AI mode. To integrate it in other parts of the app:

```typescript
import { sendMessageToAI, clearAIConversation } from '~/app/services/ai-assistant';

// Send a message to AI
const response = await sendMessageToAI('Your message here', chatHistory);
// response has both text and formatted properties
console.log(response.text); // Plain text response
console.log(response.formatted); // Formatted text (markdown)

// Clear conversation history
clearAIConversation();
```

## Chat History Format

Chat history should be an array of ChatMessage objects:

```typescript
interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  formattedContent?: string; // Optional formatted content with markdown
}
```

## Error Handling

The service will throw an error if the API request fails. Always use try/catch when calling the service:

```typescript
try {
  const response = await sendMessageToAI(message, history);
  // Handle response
} catch (error) {
  console.error('Error:', error);
  // Handle error
}
```

## Environment Variables

- `EXPO_PUBLIC_GEMINI_API_KEY`: Your Google Gemini API key
