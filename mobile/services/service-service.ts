import { supabase } from '@/lib/supabase';

// Types for services and categories
export interface Category {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  icon: string | null;
  sort_order: number | null;
  is_active: boolean;
  category_data: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  name: string;
  description?: string;
  price: number;
  duration: number; // in minutes
  category_id: string;
  category?: Category; // Related category information (optional)
  is_active: boolean;
  service_data?: {
    tools_required?: string[];
    parts_required?: string[];
    technician_notes?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface GetServicesFilters {
  page?: number;
  limit?: number;
  search?: string;
  category_id?: string;
  is_active?: boolean;
}

export interface GetServicesResponse {
  items: Service[];
  total: number;
}

/**
 * Get services with pagination and filtering
 */
export async function getServices(filters: GetServicesFilters = {}): Promise<GetServicesResponse> {
  try {
    // Start building the query
    let query = supabase.from('services').select(
      `
        *,
        category:categories(*)
      `,
      { count: 'exact' }
    );

    // Apply filters
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters.category_id) {
      // Handle array of category IDs or single ID
      const categoryIds = Array.isArray(filters.category_id)
        ? filters.category_id
        : [filters.category_id];
      query = query.in('category_id', categoryIds);
    }

    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to).order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching services:', error);
      throw new Error(error.message);
    }

    return {
      items: data || [],
      total: count || 0,
    };
  } catch (error) {
    console.error('Error in getServices:', error);
    // Return empty result on error rather than throwing
    return { items: [], total: 0 };
  }
}

/**
 * Get services by category ID
 */
export async function getServicesByCategory(categoryId: string): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('services')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching services by category:', error);
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error(`Error in getServicesByCategory:`, error);
    // Return empty array on error rather than throwing
    return [];
  }
}

/**
 * Get all active services
 */
export async function getActiveServices(): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('services')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching active services:', error);
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getActiveServices:', error);
    // Return empty array on error rather than throwing
    return [];
  }
}

/**
 * Get all active categories
 */
export async function getActiveCategories(): Promise<Category[]> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching active categories:', error);
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getActiveCategories:', error);
    // Return empty array on error rather than throwing
    return [];
  }
}

/**
 * Get service by ID
 */
export async function getServiceById(serviceId: string): Promise<Service | null> {
  try {
    const { data, error } = await supabase
      .from('services')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('id', serviceId)
      .single();

    if (error) {
      console.error('Error fetching service by ID:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in getServiceById:', error);
    return null;
  }
}
