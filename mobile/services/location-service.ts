import * as Location from 'expo-location';
import { Alert } from 'react-native';

import { supabase } from '@/lib/supabase';

// Location tracking interval in milliseconds
const LOCATION_TRACKING_INTERVAL = 60000; // 1 minute

// Interface for location data
interface LocationData {
  lat: number;
  lng: number;
  ts: number;
}

// Class for managing location tracking
export class LocationTrackingService {
  private orderId: string | null = null;
  private technicianId: string | null = null;
  private isTracking: boolean = false;
  private trackingId: string | null = null;
  private intervalId: NodeJS.Timeout | null = null;
  private locationSubscription: Location.LocationSubscription | null = null;

  // Start tracking location for a specific order
  async startTracking(orderId: string, technicianId: string): Promise<boolean> {
    if (this.isTracking) {
      console.log('Already tracking location');
      return false;
    }

    // Request location permissions
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Location permission is required for this feature.');
      return false;
    }

    try {
      // Create a new tracking record in the database
      const { data, error } = await supabase
        .from('technician_locations')
        .insert({
          technician_id: technicianId,
          order_id: orderId,
          is_active: true,
          locations: [],
          started_at: new Date().toISOString(),
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating location tracking record:', error);
        return false;
      }

      this.trackingId = data.id;
      this.orderId = orderId;
      this.technicianId = technicianId;
      this.isTracking = true;

      // Start tracking location
      await this.startLocationUpdates();
      return true;
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      return false;
    }
  }

  // Start location updates
  private async startLocationUpdates() {
    try {
      // Get initial location
      const initialLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Save initial location
      await this.saveLocation({
        lat: initialLocation.coords.latitude,
        lng: initialLocation.coords.longitude,
        ts: initialLocation.timestamp,
      });

      // Set up location updates at specified interval
      this.intervalId = setInterval(async () => {
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          });

          await this.saveLocation({
            lat: location.coords.latitude,
            lng: location.coords.longitude,
            ts: location.timestamp,
          });
        } catch (err) {
          console.error('Error getting current position:', err);
        }
      }, LOCATION_TRACKING_INTERVAL);

      console.log('Location tracking started');
    } catch (error) {
      console.error('Error starting location updates:', error);
      this.stopTracking();
    }
  }

  // Save location to database
  private async saveLocation(locationData: LocationData): Promise<void> {
    if (!this.isTracking || !this.trackingId) {
      return;
    }

    try {
      // Get current locations array
      const { data: currentData, error: fetchError } = await supabase
        .from('technician_locations')
        .select('locations')
        .eq('id', this.trackingId)
        .single();

      if (fetchError) {
        console.error('Error fetching current locations:', fetchError);
        return;
      }

      // Append new location to the array
      const currentLocations = currentData.locations || [];
      const updatedLocations = [
        ...currentLocations,
        {
          lat: locationData.lat,
          lng: locationData.lng,
          ts: locationData.ts,
        },
      ];

      // Update the database
      const { error: updateError } = await supabase
        .from('technician_locations')
        .update({
          locations: updatedLocations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', this.trackingId);

      if (updateError) {
        console.error('Error updating locations:', updateError);
      }
    } catch (error) {
      console.error('Error saving location:', error);
    }
  }

  // Stop tracking location
  async stopTracking(): Promise<boolean> {
    if (!this.isTracking || !this.trackingId) {
      return false;
    }

    try {
      // Clear interval and subscription
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }

      if (this.locationSubscription) {
        this.locationSubscription.remove();
        this.locationSubscription = null;
      }

      // Update database record
      const { error } = await supabase
        .from('technician_locations')
        .update({
          is_active: false,
          ended_at: new Date().toISOString(),
        })
        .eq('id', this.trackingId);

      if (error) {
        console.error('Error updating location tracking record:', error);
        return false;
      }

      // Reset tracking state
      this.isTracking = false;
      this.trackingId = null;
      this.orderId = null;
      this.technicianId = null;

      console.log('Location tracking stopped');
      return true;
    } catch (error) {
      console.error('Failed to stop location tracking:', error);
      return false;
    }
  }

  // Check if location tracking is active
  isActive(): boolean {
    return this.isTracking;
  }

  // Get current tracking ID
  getTrackingId(): string | null {
    return this.trackingId;
  }
}

// Create singleton instance
const locationService = new LocationTrackingService();
export default locationService;
