import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

class ToastService {
  private initialized = false;

  constructor() {
    this.init();
  }

  private async init() {
    if (this.initialized) return;

    // Configure notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldPlaySound: false,
        shouldSetBadge: false,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    });

    // Request permissions on iOS
    if (Platform.OS === 'ios') {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
      }
    }

    this.initialized = true;
  }

  async showToast(message: string, type: ToastType = 'info', title?: string, duration: number = 5) {
    await this.init();

    const getIcon = () => {
      switch (type) {
        case 'success':
          return '✅';
        case 'error':
          return '❌';
        case 'warning':
          return '⚠️';
        case 'info':
        default:
          return 'ℹ️';
      }
    };

    const getTitle = () => {
      if (title) return title;
      switch (type) {
        case 'success':
          return 'Success';
        case 'error':
          return 'Error';
        case 'warning':
          return 'Warning';
        case 'info':
        default:
          return 'Information';
      }
    };

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: `${getIcon()} ${getTitle()}`,
          body: message,
          data: { type },
          categoryIdentifier: 'toast',
        },
        trigger: null, // Show immediately
      });

      // Auto dismiss after duration
      if (duration > 0) {
        setTimeout(async () => {
          await Notifications.dismissAllNotificationsAsync();
        }, duration * 1000);
      }
    } catch (error) {
      console.error('Failed to show toast:', error);
    }
  }

  async hideToast() {
    await Notifications.dismissAllNotificationsAsync();
  }

  async hideAllToasts() {
    await Notifications.dismissAllNotificationsAsync();
  }
}

// Create singleton instance
const toastService = new ToastService();

// Export convenience functions
export const showToast = (message: string, type?: ToastType, title?: string, duration?: number) =>
  toastService.showToast(message, type, title, duration);

export const hideToast = () => toastService.hideToast();
export const hideAllToasts = () => toastService.hideAllToasts();

// Hook for component usage
export const useNotification = () => {
  return {
    showToast,
    hideToast,
    hideAllToasts,
  };
};

export default toastService;
