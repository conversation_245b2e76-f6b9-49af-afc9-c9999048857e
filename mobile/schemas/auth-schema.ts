import { z } from 'zod';

// Function to create schemas with dynamic translation
export const createSchemas = (t: (key: string) => string) => {
  // Common base schema for both user types
  const baseUserSchema = {
    name: z
      .string()
      .min(1, { message: t('register.nameError') })
      .min(2, { message: t('register.nameError') }),
    email: z
      .string()
      .min(1, { message: t('register.emailError') })
      .email({ message: t('register.emailError') }),
    phone: z
      .string()
      .min(1, { message: t('register.phoneError') })
      .regex(/^\d+$/, { message: t('register.phoneFormatError') }),
    address: z.string().min(1, { message: t('register.addressError') }),
    password: z
      .string()
      .min(1, { message: t('register.passwordError') })
      .min(6, { message: t('register.passwordError') }),
    confirmPassword: z.string().min(1, { message: t('register.confirmPasswordError') }),
  };

  // Login schema
  const loginSchema = z.object({
    email: z
      .string()
      .min(1, { message: t('login.emailError') })
      .email({ message: t('login.emailError') }),
    password: z
      .string()
      .min(1, { message: t('login.passwordError') })
      .min(6, { message: t('login.passwordError') }),
  });

  // Customer registration schema
  const customerSchema = z
    .object({
      ...baseUserSchema,
      role: z.literal('customer'),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('register.passwordsDoNotMatch'),
      path: ['confirmPassword'],
    });

  // Technician registration schema
  const technicianSchema = z
    .object({
      ...baseUserSchema,
      role: z.literal('technician'),
      exp: z.number().min(0, { message: t('register.expError') }),
      certs: z.array(z.string()).min(1, { message: t('register.certsError') }),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('register.passwordsDoNotMatch'),
      path: ['confirmPassword'],
    });

  return {
    loginSchema,
    customerSchema,
    technicianSchema,
  };
};

// Static types for the schema values
export type LoginFormValues = {
  email: string;
  password: string;
};

// Base user form values
export type BaseUserFormValues = {
  name: string;
  email: string;
  phone: string;
  address: string;
  password: string;
  confirmPassword: string;
};

// Customer specific form values
export type CustomerFormValues = BaseUserFormValues & {
  role: 'customer';
};

// Technician specific form values
export type TechnicianFormValues = BaseUserFormValues & {
  role: 'technician';
  exp: number;
  certs: string[];
};
