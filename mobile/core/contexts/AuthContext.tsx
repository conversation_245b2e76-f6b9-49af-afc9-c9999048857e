import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter, useSegments } from 'expo-router';
import React, { createContext, useState, useContext, useEffect } from 'react';
import { Alert } from 'react-native';

import { supabase } from '@/lib/supabase';
import { User, UserRole, UserProfileData } from '@/types/user.types';

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string, role?: UserRole) => Promise<void>;
  register: (
    name: string,
    email: string,
    password: string,
    phone?: string,
    address?: string,
    role?: UserRole,
    profileData?: Partial<UserProfileData>
  ) => Promise<void>;
  logout: () => Promise<void>;
  updateUserInfo: (
    firstName: string,
    lastName: string,
    email: string,
    phone?: string,
    address?: string
  ) => Promise<void>;
  changePassword: (oldPassword: string, newPassword: string) => Promise<void>;
  deleteAccount: () => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
};

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage keys
const USER_STORAGE_KEY = '@auth_user';

// AuthProvider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const segments = useSegments();

  // Effect for changing screens based on auth state
  useEffect(() => {
    if (isLoading) return;

    const inAuthGroup = segments[0] === 'auth';

    if (!user && !inAuthGroup) {
      // Not logged in and not on auth screen, redirect to login
      router.replace('/auth/login');
    } else if (user && inAuthGroup) {
      // Logged in but on auth screen, redirect to appropriate home screen
      router.replace('/main');
    }

    // Role-based redirection handled in login/register functions
  }, [user, segments, isLoading]);

  // Check for stored user and Supabase session on app start
  useEffect(() => {
    const loadUser = async () => {
      try {
        // Check for existing Supabase session
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (session?.user) {
          // Get user data from users table using the auth id
          const { data: userData, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (error) {
            console.error('Error fetching user data:', error);
            await AsyncStorage.removeItem(USER_STORAGE_KEY);
            setUser(null);
            setIsLoading(false);
            return;
          }

          if (userData) {
            // Save to storage
            await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
            setUser(userData as User);
          } else {
            console.warn('User exists in auth but not in users table');
            await AsyncStorage.removeItem(USER_STORAGE_KEY);
            setUser(null);
          }
        } else {
          // No valid session, ensure user is logged out
          await AsyncStorage.removeItem(USER_STORAGE_KEY);
          setUser(null);
        }
      } catch (error) {
        console.error('Failed to load user from storage or session', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      if (event === 'SIGNED_IN' && session?.user) {
        // User signed in, fetch user data from users table
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (error) {
          console.error('Error fetching user data on sign in:', error);
          await AsyncStorage.removeItem(USER_STORAGE_KEY);
          setUser(null);
          return;
        }

        if (userData) {
          await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
          setUser(userData as User);
        } else {
          console.warn('User authenticated but not found in users table');
          await AsyncStorage.removeItem(USER_STORAGE_KEY);
          setUser(null);
        }
      } else if (event === 'SIGNED_OUT') {
        // User signed out or deleted
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
        setUser(null);
      } else if (event === 'USER_UPDATED' && session?.user) {
        // User profile updated, fetch latest from users table
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (error) {
          console.error('Error fetching updated user data:', error);
          return;
        }

        if (userData) {
          await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
          setUser(userData as User);
        }
      }
    });

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);

      // Use Supabase for authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        Alert.alert('Login Error', error.message || 'Invalid credentials. Please try again.');
        throw error;
      }

      if (data.user) {
        // Get user data from users table
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (userError || !userData) {
          Alert.alert('Login Error', 'User data not found. Please contact support.');
          throw new Error('User data not found in database');
        }

        // Save user to storage
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));

        // Update state
        setUser(userData as User);

        // Redirect based on role
        setTimeout(() => {
          // Both roles currently go to main, but this can be customized
          router.replace('/main');
        }, 0);
      }
    } catch (error) {
      console.error('Login failed', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (
    name: string,
    email: string,
    password: string,
    phone?: string,
    address?: string,
    role: UserRole = 'customer',
    profileData: Partial<UserProfileData> = {}
  ) => {
    try {
      setIsLoading(true);

      // Step 1: Create the user in auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        Alert.alert(
          'Registration Error',
          error.message || 'Registration failed. Please try again.'
        );
        throw error;
      }

      if (data.user) {
        // Step 2: Create user record in users table with upsert
        const userData: User = {
          id: data.user.id,
          email: data.user.email || email,
          name,
          role,
          phone,
          address,
          profile_data: profileData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Use upsert instead of insert to handle potential duplicates
        const { error: insertError } = await supabase.from('users').upsert(userData);

        if (insertError) {
          console.error('Error creating user record:', insertError);
          Alert.alert('Registration Error', 'Failed to create user record. Please try again.');
          throw insertError;
        }

        // Step 3: Immediately confirm email if session exists
        if (data.session) {
          // User is auto-logged in (email confirmation not enabled on Supabase project)
          await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
          setUser(userData);

          // Redirect to main screen
          setTimeout(() => {
            router.replace('/main');
          }, 0);
        } else {
          // Success message and redirect to login
          Alert.alert(
            'Registration Successful',
            'Your account has been created. You can now log in.',
            [{ text: 'OK', onPress: () => router.replace('/auth/login') }]
          );
        }
      }
    } catch (error) {
      console.error('Registration failed', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update user info function
  const updateUserInfo = async (
    name: string,
    lastName: string, // Giữ tham số này để tương thích với code hiện tại
    email: string,
    phone?: string,
    address?: string
  ) => {
    try {
      // KHÔNG set isLoading = true để tránh unmount toàn bộ app
      // setIsLoading(true);

      if (!user) throw new Error('No user logged in');

      // Create updated user that conforms to User interface
      const updatedUser: User = {
        ...user,
        name, // Sử dụng trực tiếp tên đầy đủ
        email,
        phone,
        address,
        updated_at: new Date().toISOString(), // Update the timestamp
      };

      // Update user record in users table
      const { error } = await supabase
        .from('users')
        .update({
          name: updatedUser.name,
          email: updatedUser.email,
          phone: updatedUser.phone,
          address: updatedUser.address,
          updated_at: updatedUser.updated_at,
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      // Save to storage
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser));

      // Update state
      setUser(updatedUser);
    } catch (error) {
      console.error('Update user info failed', error);
      Alert.alert('Update Failed', 'Could not update user information. Please try again.');
      throw error;
    } finally {
      // Không cần set isLoading = false nữa
      // setIsLoading(false);
    }
  };

  // Change password function
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      setIsLoading(true);

      // Use Supabase to update password
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        Alert.alert(
          'Password Change Error',
          error.message || 'Could not change password. Please try again.'
        );
        throw error;
      }

      Alert.alert('Success', 'Password changed successfully.');
      console.log('Password changed successfully');
    } catch (error) {
      console.error('Change password failed', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete account function
  const deleteAccount = async () => {
    try {
      if (!user) throw new Error('No user logged in');

      // In Supabase client SDK, we use signOut to delete the current session
      // For account deletion, typically you'd call a secure server endpoint
      // that has admin permissions to delete the user
      const { error } = await supabase.auth.signOut();
      if (error) {
        Alert.alert('Error', error.message || 'Sign out failed. Please try again.');
        throw error;
      }

      // Note: Real account deletion would require a server-side function
      // with admin privileges or a custom API endpoint
      console.log('Account deletion would require server-side implementation');

      // Clear local storage
      await AsyncStorage.removeItem(USER_STORAGE_KEY);

      // Thông báo trước khi đặt user = null
      Alert.alert('Account Signed Out', 'You have been signed out successfully.');

      // Update state - việc này sẽ kích hoạt useEffect điều hướng người dùng
      setUser(null);

      // Không cần setTimeout và router.replace
      // useEffect sẽ phát hiện user = null và tự chuyển hướng đến login
    } catch (error) {
      console.error('Delete account failed', error);
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Clear storage
      await AsyncStorage.removeItem(USER_STORAGE_KEY);

      // Update state
      setUser(null);

      // Không đặt isLoading = true để tránh unrender component
      // và không chuyển hướng ngay lập tức - React Navigation sẽ tự điều hướng
      // dựa vào useEffect khi user = null
    } catch (error) {
      console.error('Logout failed', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        register,
        logout,
        updateUserInfo,
        changePassword,
        deleteAccount,
        isLoading,
        isAuthenticated: !!user,
      }}>
      {!isLoading ? children : null}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
