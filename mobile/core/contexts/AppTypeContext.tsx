import Constants from 'expo-constants';
import React, { createContext, useContext } from 'react';

// Đ<PERSON><PERSON> nghĩa các loại ứng dụng có thể có
export type AppType = 'customer' | 'technician';

// Định nghĩa kiểu dữ liệu cho context
type AppTypeContextType = {
  appType: AppType;
  isCustomer: boolean;
  isTechnician: boolean;
};

// Đọc biến môi trường APP_TYPE hoặc sử dụng giá trị mặc định là 'customer'
const getAppTypeFromEnv = (): AppType => {
  const appType =
    Constants.expoConfig?.extra?.EXPO_PUBLIC_APP_TYPE ||
    process.env.EXPO_PUBLIC_APP_TYPE ||
    'customer';

  // Đảm bảo giá trị hợp lệ
  return appType === 'technician' ? 'technician' : 'customer';
};

// Tạo context với giá trị mặc định
const AppTypeContext = createContext<AppTypeContextType>({
  appType: 'customer',
  isCustomer: true,
  isTechnician: false,
});

// Provider component
export function AppTypeProvider({ children }: { children: React.ReactNode }) {
  const appType = getAppTypeFromEnv();
  const isCustomer = appType === 'customer';
  const isTechnician = appType === 'technician';

  return (
    <AppTypeContext.Provider
      value={{
        appType,
        isCustomer,
        isTechnician,
      }}>
      {children}
    </AppTypeContext.Provider>
  );
}

// Hook để sử dụng context này
export function useAppType() {
  const context = useContext(AppTypeContext);
  if (context === undefined) {
    throw new Error('useAppType must be used within an AppTypeProvider');
  }
  return context;
}
