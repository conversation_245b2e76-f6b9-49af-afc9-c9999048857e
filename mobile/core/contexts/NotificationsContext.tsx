import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';

import { useAuth } from './AuthContext';

import { supabase } from '@/lib/supabase';

// Define notification types
export type NotificationType = 'order_status' | 'technician_arrival';

// Define the background notification task name
const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';

// Define the notification context type
type NotificationsContextType = {
  registerForPushNotificationsAsync: () => Promise<void>;
  sendOrderStatusUpdate: (
    userId: string,
    orderId: string,
    status: string,
    details: string
  ) => Promise<void>;
  sendTechnicianArrivalNotice: (
    userId: string,
    orderId: string,
    estimatedTime: number
  ) => Promise<void>;
  expoPushToken: string | null;
};

// Create context
const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

// Define task handler for background notifications
TaskManager.defineTask<Notifications.NotificationTaskPayload>(
  BACKGROUND_NOTIFICATION_TASK,
  async ({ data, error }) => {
    if (error) {
      console.error('Error in background notification task:', error);
      return;
    }

    if (!data) {
      console.warn('No data received in background notification task');
      return;
    }

    // Handle different notification actions depending on type
    try {
      const notificationData = (data.notification as Notifications.Notification)?.request.content
        .data;
      console.log('Background notification data:', notificationData);

      // Handle specific notification types in background
      if (notificationData?.type === 'order_status') {
        // Handle order status update in background
        console.log('Handling order status update in background:', notificationData.status);
      }
    } catch (err) {
      console.error('Error handling background notification:', err);
    }
  }
);

// Configure how notifications appear when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Create provider component
export const NotificationsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const notificationListener = useRef<Notifications.EventSubscription | null>(null);
  const responseListener = useRef<Notifications.EventSubscription | null>(null);

  useEffect(() => {
    // Register for push notifications if user is logged in
    if (user) {
      registerForPushNotificationsAsync();
    }

    try {
      // Register task for background notifications
      Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK).catch((err) => {
        console.error('Error registering notification task:', err);
      });
    } catch (err) {
      console.error('Failed to register background task:', err);
    }

    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received:', notification);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification response received:', response);
      const { notification } = response;
      const data = notification.request.content.data;

      // Handle notification tap here - navigate to appropriate screens, etc.
      if (data.orderId) {
        // Could navigate to order details here
        console.log('Navigate to order details:', data.orderId);
      }
    });

    // Clean up on unmount
    return () => {
      try {
        Notifications.unregisterTaskAsync(BACKGROUND_NOTIFICATION_TASK).catch(console.error);
        if (notificationListener.current) {
          notificationListener.current.remove();
        }
        if (responseListener.current) {
          responseListener.current.remove();
        }
      } catch (err) {
        console.error('Error cleaning up notification listeners:', err);
      }
    };
  }, [user?.id]);

  // Register for push notifications and save token
  const registerForPushNotificationsAsync = async () => {
    if (!user) return;

    try {
      // Set up notification channels for Android (required for Android 8+)
      if (Platform.OS === 'android') {
        // Create a channel for order reminders
        await Notifications.setNotificationChannelAsync('order_reminders', {
          name: 'Order Reminders',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
        });

        // Create a channel for order updates
        await Notifications.setNotificationChannelAsync('order_updates', {
          name: 'Order Status Updates',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 100, 100, 100],
          lightColor: '#3498db',
          sound: 'default',
        });
      }

      // Request notification permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Failed to get push token for push notification!');
        return;
      }

      // Get push token
      const projectId = process.env.EXPO_PUBLIC_PROJECT_ID;
      const tokenConfig = projectId ? { projectId } : {};
      const token = await Notifications.getExpoPushTokenAsync(tokenConfig);

      // Save the token
      setExpoPushToken(token.data);

      // Get device info
      const deviceInfo = {
        platform: Platform.OS,
        osVersion: Platform.Version?.toString() || 'unknown',
        isDevice: Device.isDevice,
        brand: Device.brand || 'unknown',
        modelName: Device.modelName || 'unknown',
      };

      // Check if token already exists to avoid duplicates
      const { data: existingTokens, error: checkError } = await supabase
        .from('user_push_tokens')
        .select('id')
        .eq('user_id', user.id)
        .eq('token', token.data)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking existing token:', checkError);
        return;
      }

      // Save token to the user_push_tokens table
      if (!existingTokens) {
        const { error } = await supabase.from('user_push_tokens').insert({
          user_id: user.id,
          token: token.data,
          device_info: deviceInfo,
        });

        if (error) {
          console.error('Error saving push token to database:', error);
        } else {
          console.log('Push token saved successfully');
        }
      }
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  };

  // Send notification about order status changes (to customers)
  const sendOrderStatusUpdate = async (
    userId: string,
    orderId: string,
    status: string,
    details: string
  ) => {
    try {
      // Get user's push tokens from the database
      const { data, error } = await supabase
        .from('user_push_tokens')
        .select('token')
        .eq('user_id', userId);

      if (error) {
        throw new Error('Failed to get user push tokens');
      }

      if (!data || data.length === 0) {
        console.log('No push tokens found for user');
        return;
      }

      // Send notification to all user devices
      for (const tokenRecord of data) {
        await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: tokenRecord.token,
            title: `Order Status: ${status}`,
            body: details,
            data: { orderId, type: 'order_status', status },
            sound: 'default',
            channelId: 'order_updates',
          }),
        });
      }

      console.log(`Status update sent to user ${userId} for order ${orderId}`);
    } catch (error) {
      console.error('Failed to send status update notification:', error);
    }
  };

  // Send notification that technician is arriving (to customers)
  const sendTechnicianArrivalNotice = async (
    userId: string,
    orderId: string,
    estimatedTime: number
  ) => {
    try {
      // Get user's push tokens from the database
      const { data, error } = await supabase
        .from('user_push_tokens')
        .select('token')
        .eq('user_id', userId);

      if (error) {
        throw new Error('Failed to get user push tokens');
      }

      if (!data || data.length === 0) {
        console.log('No push tokens found for user');
        return;
      }

      // Send notification to all user devices
      for (const tokenRecord of data) {
        await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: tokenRecord.token,
            title: 'Technician On The Way',
            body: `Your technician will arrive in about ${estimatedTime} minutes`,
            data: { orderId, type: 'technician_arrival', estimatedTime },
            sound: 'default',
            channelId: 'order_updates',
          }),
        });
      }

      console.log(`Arrival notice sent to user ${userId} for order ${orderId}`);
    } catch (error) {
      console.error('Failed to send technician arrival notification:', error);
    }
  };

  // Create context value
  const contextValue: NotificationsContextType = {
    registerForPushNotificationsAsync,
    sendOrderStatusUpdate,
    sendTechnicianArrivalNotice,
    expoPushToken,
  };

  return (
    <NotificationsContext.Provider value={contextValue}>{children}</NotificationsContext.Provider>
  );
};

// Custom hook to use notifications context
export const useNotifications = () => {
  const context = useContext(NotificationsContext);

  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }

  return context;
};
