{"permissions": {"allow": ["Bash(find:*)", "Bash(npm run lint)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run format:*)", "Bash(npx tsc:*)", "Bash(npm run start:customer:*)", "<PERSON><PERSON>(chmod:*)", "Bash(cp:*)", "Bash(npx expo prebuild:*)", "Bash(npm run prebuild:customer:*)", "Bash(git checkout:*)", "WebFetch(domain:docs.expo.dev)", "Bash(npm run prebuild:technician:*)", "Bash(ls:*)", "WebFetch(domain:docs.stripe.com)", "Bash(grep:*)", "Bash(rm:*)"], "deny": []}}