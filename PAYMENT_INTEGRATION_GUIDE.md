# Payment Integration Guide - Stripe with React Native

This guide explains how to set up and use the Stripe payment integration in your React Native (Expo) app with Express.js backend.

## Overview

The payment system includes:
- **Backend**: Express.js server with Stripe API integration
- **Frontend**: React Native app with Stripe React Native SDK
- **Database**: Supabase for storing payment and order data
- **Payment Methods**: Credit/Debit cards, Apple Pay, Google Pay

## Setup Instructions

### 1. Backend Setup

#### Install Dependencies
```bash
cd backend
npm install
```

#### Environment Configuration
Copy `.env.example` to `.env` and fill in your values:

```bash
# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:8081

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### Start the Server
```bash
npm run dev
```

The server will run on `http://localhost:3000`

### 2. Database Setup

Run the Supabase migrations to create the required tables:

```bash
cd frontend
supabase migration up
```

This creates:
- `payments` table for payment tracking
- Updates `orders` table with payment-related fields

### 3. Mobile App Setup

#### Environment Configuration
Copy `.env.local.example` to `.env.local` and add your Stripe keys:

```bash
# Add to your existing .env.local file
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
EXPO_PUBLIC_API_URL=http://localhost:3000
```

#### Install Dependencies
The Stripe React Native SDK is already installed. If you need to reinstall:

```bash
cd mobile
npm install @stripe/stripe-react-native --legacy-peer-deps
```

### 4. Stripe Configuration

#### Get Your Stripe Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your **Publishable Key** (starts with `pk_test_`)
3. Get your **Secret Key** (starts with `sk_test_`)

#### Set Up Webhooks (Optional but Recommended)
1. In Stripe Dashboard, go to Developers > Webhooks
2. Add endpoint: `https://your-backend-url.com/api/payment/webhook`
3. Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
4. Copy the webhook secret (starts with `whsec_`)

#### Apple Pay Setup (iOS)
1. Add your Apple Pay merchant ID in `mobile/lib/stripe.ts`
2. Enable Apple Pay in your Stripe dashboard
3. Configure your iOS app with the merchant ID in Xcode

#### Google Pay Setup (Android)
1. Enable Google Pay in your Stripe dashboard
2. The app is configured to use test environment in development

## Usage

### Adding Payment to an Order Screen

```tsx
import { PaymentButton } from '@/components/payment';

// In your order component
<PaymentButton
  orderId={order.id}
  amount={order.total_amount}
  currency="USD"
  disabled={order.status !== 'completed'}
/>
```

### Direct Payment Navigation

```tsx
import { useRouter } from 'expo-router';

const router = useRouter();

// Navigate to payment screen
router.push(\`/main/payment/\${orderId}\`);
```

### Custom Payment Integration

```tsx
import { usePayment } from '@/hooks/usePayment';
import { PaymentScreen } from '@/components/payment';

function CustomPaymentScreen() {
  const {
    state,
    createPaymentIntent,
    processCardPayment,
    processApplePayPayment,
    processGooglePayPayment,
  } = usePayment();

  // Create payment intent
  await createPaymentIntent({
    orderId: 'order_123',
    amount: 2000, // $20.00 in cents
    currency: 'usd',
    customerId: 'customer_123',
  });

  // Process payments based on method
  // See PaymentScreen component for full implementation
}
```

## API Endpoints

### Create Payment Intent
```
POST /api/payment/create-intent
```

Request:
```json
{
  "orderId": "order_123",
  "amount": 2000,
  "currency": "usd",
  "customerId": "customer_123",
  "metadata": {}
}
```

Response:
```json
{
  "paymentIntentId": "pi_123",
  "clientSecret": "pi_123_secret_abc",
  "status": "requires_payment_method"
}
```

### Confirm Payment
```
POST /api/payment/confirm
```

Request:
```json
{
  "paymentIntentId": "pi_123",
  "orderId": "order_123"
}
```

### Get Payment Status
```
GET /api/payment/status/{paymentIntentId}
```

### Webhook Endpoint
```
POST /api/payment/webhook
```

## Database Schema

### Orders Table Updates
- Added `payment_status` column: `'unpaid' | 'pending' | 'paid' | 'failed' | 'refunded'`
- Renamed `amount` to `total_amount`
- Added `payment_metadata` jsonb column
- Updated status enum to include `'paid'`

### Payments Table
- `id`: Stripe PaymentIntent ID (primary key)
- `order_id`: Reference to orders table
- `customer_id`: Reference to users table
- `stripe_payment_intent_id`: Stripe PaymentIntent ID
- `amount`: Amount in cents
- `currency`: Currency code
- `status`: Payment status from Stripe
- `payment_method_type`: Type of payment method used
- `payment_method_details`: JSON details
- `metadata`: Additional metadata
- `failure_reason`: Reason for failure if applicable

## Testing

### Test Credit Cards
Use Stripe's test cards:
- **Success**: 4242 4242 4242 4242
- **Decline**: 4000 0000 0000 0002
- **3D Secure**: 4000 0025 0000 3155

### Test Apple Pay/Google Pay
- Use the simulator/emulator
- Apple Pay works in iOS Simulator
- Google Pay works in Android Emulator

## Security Considerations

1. **Never expose secret keys** in frontend code
2. **Use HTTPS** in production
3. **Validate payments** on backend before fulfilling orders
4. **Set up webhooks** for reliable payment status updates
5. **Implement proper error handling** for payment failures
6. **Use Stripe's security features** like radar for fraud detection

## Troubleshooting

### Common Issues

#### Stripe Not Initializing
- Check that `EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY` is set
- Ensure StripeProvider is wrapping your app correctly

#### Apple Pay Not Working
- Verify merchant ID is correctly configured
- Check that Apple Pay is enabled in Stripe dashboard
- Ensure you're testing on a physical device or simulator

#### Google Pay Not Working
- Verify Google Pay is enabled in Stripe dashboard
- Check that you're using the correct test environment settings

#### Backend Connection Issues
- Verify `EXPO_PUBLIC_API_URL` points to your running backend
- Check that CORS is properly configured for your frontend URL
- Ensure backend is running and accessible

#### Payment Intent Creation Fails
- Check order exists and status is 'completed'
- Verify amount matches order total
- Check Supabase permissions for orders table

### Debug Steps

1. **Check logs** in both frontend and backend
2. **Verify environment variables** are loaded correctly
3. **Test API endpoints** directly with Postman/curl
4. **Check Stripe dashboard** for payment events
5. **Verify database permissions** in Supabase

## Production Deployment

1. **Replace test keys** with live Stripe keys
2. **Set up production webhooks** with your live URL
3. **Configure Apple Pay merchant ID** for production
4. **Set up proper error monitoring** (Sentry, etc.)
5. **Implement proper logging** for payment events
6. **Set up backup payment processing** if needed

## Support

For issues specific to:
- **Stripe Integration**: [Stripe Documentation](https://stripe.com/docs)
- **React Native**: [Stripe React Native Docs](https://stripe.dev/stripe-react-native)
- **Expo**: [Expo Documentation](https://docs.expo.dev/)
- **Supabase**: [Supabase Documentation](https://supabase.com/docs)