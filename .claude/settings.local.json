{"permissions": {"allow": ["Bash(npm install:*)", "Bash(find:*)", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(cp:*)", "Bash(ls:*)", "Bash(yarn dev)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:docs.stripe.com)", "mcp__ide__getDiagnostics", "Bash(npm run format:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(tree:*)", "Bash(yarn format)", "<PERSON><PERSON>(true)", "Bash(npx expo start:*)", "Bash(npx eslint:*)", "Bash(node:*)", "Bash(for dir in */)", "Bash(done)", "Bash(npm ls:*)", "Bash(grep:*)", "WebFetch(domain:developers.google.com)", "WebSearch"], "deny": []}}