import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { stripe, STRIPE_CONFIG } from '../config/stripe';
import { supabase } from '../config/supabase';
import { asyncHandler } from '../middleware/asyncHandler';

const router = Router();

// Validation schemas
const createPaymentIntentSchema = z.object({
  orderId: z.string().min(1),
  amount: z.number().positive(),
  currency: z.string().default('usd'),
  customerId: z.string().optional(),
  metadata: z.record(z.string()).optional(),
});

const confirmPaymentSchema = z.object({
  paymentIntentId: z.string().min(1),
  orderId: z.string().min(1),
});

// Create Payment Intent
router.post('/create-intent', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { orderId, amount, currency = 'usd', customerId, metadata } = createPaymentIntentSchema.parse(req.body);

  // Check if order exists and is valid
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .select('id, status, amount, customer_id')
    .eq('id', orderId)
    .single();

  if (orderError || !order) {
    res.status(404).json({ error: 'Order not found' });
    return;
  }

  if (order.status !== 'completed') {
    res.status(400).json({ error: 'Order is not ready for payment' });
    return;
  }

  // Verify amount matches order total
  const orderAmount = Math.round(order.amount * 100); // Convert to cents
  if (amount !== orderAmount) {
    res.status(400).json({ error: 'Payment amount does not match order total' });
    return;
  }

  try {
    // Create Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      customer: customerId,
      metadata: {
        orderId,
        customerId: order.customer_id,
        ...metadata,
      },
      ...STRIPE_CONFIG,
    });

    // Store payment intent in database
    const { error: insertError } = await supabase
      .from('payments')
      .insert({
        id: paymentIntent.id,
        order_id: orderId,
        customer_id: order.customer_id,
        amount: amount,
        currency: currency,
        status: 'pending',
        stripe_payment_intent_id: paymentIntent.id,
        created_at: new Date().toISOString(),
      });

    if (insertError) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('Failed to store payment intent:', insertError);
      }
      // Continue anyway, as payment intent was created successfully in Stripe
    }

    res.json({
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      status: paymentIntent.status,
    });

  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('Failed to create payment intent:', error);
    }
    res.status(500).json({ error: 'Failed to create payment intent' });
  }
}));

// Confirm Payment
router.post('/confirm', asyncHandler(async (req: Request, res: Response) => {
  const { paymentIntentId, orderId } = confirmPaymentSchema.parse(req.body);

  try {
    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    // Update payment status in database
    const { error: updateError } = await supabase
      .from('payments')
      .update({
        status: paymentIntent.status,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_payment_intent_id', paymentIntentId);

    if (updateError) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('Failed to update payment status:', updateError);
      }
    }

    // If payment succeeded, update order status
    if (paymentIntent.status === 'succeeded') {
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({
          status: 'paid',
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      if (orderUpdateError) {
        if (process.env.NODE_ENV !== 'production') {
          console.error('Failed to update order status:', orderUpdateError);
        }
      }
    }

    res.json({
      paymentIntentId: paymentIntent.id,
      status: paymentIntent.status,
      orderId,
    });

  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('Failed to confirm payment:', error);
    }
    res.status(500).json({ error: 'Failed to confirm payment' });
  }
}));

// Get Payment Status
router.get('/status/:paymentIntentId', asyncHandler(async (req: Request, res: Response) => {
  const { paymentIntentId } = req.params;

  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    res.json({
      paymentIntentId: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
    });

  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('Failed to get payment status:', error);
    }
    res.status(500).json({ error: 'Failed to get payment status' });
  }
}));

// Webhook endpoint for Stripe events
router.post('/webhook', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const sig = req.headers['stripe-signature'] as string;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    res.status(400).json({ error: 'Webhook secret not configured' });
    return;
  }

  try {
    const event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);

    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object;
        if (process.env.NODE_ENV !== 'production') {
          console.log('Payment succeeded:', paymentIntent.id);
        }
        
        // Update payment and order status
        await supabase
          .from('payments')
          .update({
            status: 'succeeded',
            updated_at: new Date().toISOString(),
          })
          .eq('stripe_payment_intent_id', paymentIntent.id);

        if (paymentIntent.metadata?.orderId) {
          await supabase
            .from('orders')
            .update({
              status: 'paid',
              updated_at: new Date().toISOString(),
            })
            .eq('id', paymentIntent.metadata.orderId);
        }
        break;
      }

      case 'payment_intent.payment_failed': {
        const failedPayment = event.data.object;
        if (process.env.NODE_ENV !== 'production') {
          console.log('Payment failed:', failedPayment.id);
        }
        
        await supabase
          .from('payments')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString(),
          })
          .eq('stripe_payment_intent_id', failedPayment.id);
        break;
      }

      default:
        if (process.env.NODE_ENV !== 'production') {
          console.log(`Unhandled event type: ${event.type}`);
        }
    }

    res.json({ received: true });

  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('Webhook error:', error);
    }
    res.status(400).json({ error: 'Webhook validation failed' });
  }
}));

export { router as paymentRoutes };