import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

export const errorHandler = (
  error: any,
  _req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Log error for debugging
  if (process.env.NODE_ENV !== 'production') {
    console.error('Error:', error);
  }

  // Zod validation errors
  if (error instanceof ZodError) {
    res.status(400).json({
      error: 'Validation error',
      details: error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      })),
    });
    return;
  }

  // Stripe errors
  if (error.type && error.type.startsWith('Stripe')) {
    res.status(400).json({
      error: 'Payment processing error',
      message: error.message,
    });
    return;
  }

  // Default server error
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : error.message,
  });
};