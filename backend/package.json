{"name": "vtech-backend", "version": "1.0.0", "description": "Backend server for VTech payment processing", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "express": "^4.18.2", "stripe": "^14.15.0", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "tsx": "^4.7.0", "typescript": "^5.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "jest": "^29.7.0", "@types/jest": "^29.5.11"}, "keywords": ["payment", "stripe", "supabase", "express"], "author": "", "license": "MIT"}