# VTech Backend

Backend server for VTech payment processing with Stripe integration.

## Prerequisites

- Node.js (v18 or higher)
- Yarn package manager
- Stripe account
- Supabase account

## Installation

```bash
# Install dependencies
yarn install

# Copy environment variables
cp .env.example .env
```

## Environment Configuration

Create a `.env` file with the following variables:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Stripe Setup

### 1. Get Stripe Keys

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to **Developers** → **API keys**
3. Copy your **Secret key** (starts with `sk_test_` for test mode)
4. Set `STRIPE_SECRET_KEY` in your `.env` file

### 2. Configure Webhooks

1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://your-domain.com/api/payment/webhook`
4. Select events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Copy the **Signing secret** (starts with `whsec_`)
6. Set `STRIPE_WEBHOOK_SECRET` in your `.env` file

**Note**: The webhook endpoint handles payment completion and automatically updates order status in the database.

### 3. Test Mode vs Live Mode

- **Test mode**: Use `sk_test_` keys for development
- **Live mode**: Use `sk_live_` keys for production

## Available Scripts

```bash
# Development
yarn dev          # Start development server with hot reload

# Production
yarn build        # Build TypeScript to JavaScript
yarn start        # Start production server

# Quality checks
yarn lint         # Run ESLint
yarn test         # Run tests
```

## API Endpoints

### Payment Routes
- `POST /api/payment/create-intent` - Create payment intent for an order
- `POST /api/payment/confirm` - Confirm payment completion
- `GET /api/payment/status/:paymentIntentId` - Get payment status
- `POST /api/payment/webhook` - Stripe webhook handler

### Health Check
- `GET /health` - Server health status

### API Documentation

#### POST /api/payment/create-intent
Creates a payment intent for an existing order.

**Body:**
```json
{
  "orderId": "string",
  "amount": "number (in cents)",
  "currency": "string (default: usd)",
  "customerId": "string (optional)",
  "metadata": "object (optional)"
}
```

**Response:**
```json
{
  "paymentIntentId": "pi_xxx",
  "clientSecret": "pi_xxx_secret_xxx",
  "status": "requires_payment_method"
}
```

#### POST /api/payment/confirm
Confirms a payment and updates order status.

**Body:**
```json
{
  "paymentIntentId": "pi_xxx",
  "orderId": "string"
}
```

#### GET /api/payment/status/:paymentIntentId
Retrieves current payment status.

**Response:**
```json
{
  "paymentIntentId": "pi_xxx",
  "status": "succeeded",
  "amount": 2000,
  "currency": "usd"
}
```

#### POST /api/payment/webhook
Stripe webhook endpoint that handles:
- `payment_intent.succeeded` - Updates payment and order status to paid
- `payment_intent.payment_failed` - Updates payment status to failed

**Headers:**
- `stripe-signature` - Required for webhook verification

## Deployment

### Vercel Deployment

1. Install Vercel CLI: `npm i -g vercel`
2. Set environment variables in Vercel dashboard:
   - `STRIPE_SECRET_KEY`
   - `STRIPE_WEBHOOK_SECRET` 
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`
3. Deploy: `vercel --prod`

### Environment Variables in Vercel

Go to your Vercel project settings and add:

```
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Security Notes

- Never commit `.env` files to version control
- Use test keys during development
- Switch to live keys only in production
- Validate webhook signatures
- Use HTTPS in production

## Support

For issues related to:
- Stripe integration: Check [Stripe Documentation](https://stripe.com/docs)
- Supabase: Check [Supabase Documentation](https://supabase.com/docs)