module.exports = {
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: "module",
  },
  plugins: ["@typescript-eslint"],
  extends: ["eslint:recommended"],
  root: true,
  env: {
    node: true,
    jest: true,
    es6: true,
  },
  ignorePatterns: [".eslintrc.js", "dist/**/*", "node_modules/**/*"],
  rules: {
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/no-unused-vars": "error",
    "prefer-const": "error",
    "no-var": "error",
    "no-console": "warn",
    "no-undef": "off", // TypeScript handles this
    "no-unused-vars": "off", // Use @typescript-eslint/no-unused-vars instead
  },
};
