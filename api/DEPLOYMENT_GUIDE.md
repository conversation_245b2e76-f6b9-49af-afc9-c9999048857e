# Deployment Guide

This guide covers deploying the VTech API to production environments.

## 🚀 Vercel Deployment (Recommended)

### Prerequisites
- Vercel account
- GitHub repository
- Supabase project

### Step 1: Environment Variables

Set these environment variables in your Vercel dashboard:

```env
# Required
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=your-strong-jwt-secret

# Optional
NODE_ENV=production
FRONTEND_URL=https://your-frontend.vercel.app
MOBILE_URL=https://your-mobile-app.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOG_LEVEL=info
```

### Step 2: Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### Step 3: Configure Custom Domain (Optional)

1. Go to Vercel dashboard
2. Select your project
3. Go to Settings > Domains
4. Add your custom domain
5. Update DNS records as instructed

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src ./src

# Build the application
RUN npm run build

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start the application
CMD ["npm", "start"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped
```

### Build and Run

```bash
# Build the image
docker build -t vtech-api .

# Run with environment file
docker run -d \
  --name vtech-api \
  --env-file .env.production \
  -p 3001:3001 \
  vtech-api

# Or use docker-compose
docker-compose up -d
```

## ☁️ AWS Deployment

### AWS Lambda + API Gateway

1. **Install Serverless Framework**
```bash
npm install -g serverless
```

2. **Create serverless.yml**
```yaml
service: vtech-api

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  environment:
    SUPABASE_URL: ${env:SUPABASE_URL}
    SUPABASE_SERVICE_ROLE_KEY: ${env:SUPABASE_SERVICE_ROLE_KEY}
    JWT_SECRET: ${env:JWT_SECRET}

functions:
  api:
    handler: dist/lambda.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true

plugins:
  - serverless-offline
```

3. **Create Lambda Handler**
```typescript
// src/lambda.ts
import serverless from 'serverless-http';
import app from './app';

export const handler = serverless(app);
```

4. **Deploy**
```bash
serverless deploy
```

### AWS ECS (Container Service)

1. **Create ECS Task Definition**
2. **Setup Application Load Balancer**
3. **Configure Auto Scaling**
4. **Deploy container**

## 🔧 Production Configuration

### Environment Variables

```env
# Production Environment
NODE_ENV=production

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
JWT_SECRET=your-very-strong-jwt-secret-at-least-32-characters
CORS_ORIGIN=https://your-frontend.com,https://your-mobile-app.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=warn
LOG_FILE=/var/log/vtech-api.log

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads

# WebSocket
WS_PORT=3002
```

### Security Checklist

- [ ] Use strong JWT secret (32+ characters)
- [ ] Enable HTTPS/TLS
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Use environment variables for secrets
- [ ] Enable request logging
- [ ] Set up monitoring and alerts
- [ ] Regular security updates

### Performance Optimization

1. **Enable Compression**
```typescript
import compression from 'compression';
app.use(compression());
```

2. **Connection Pooling**
```typescript
// Configure Supabase client with connection pooling
const supabase = createClient(url, key, {
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'Connection': 'keep-alive'
    }
  }
});
```

3. **Caching**
```typescript
import NodeCache from 'node-cache';
const cache = new NodeCache({ stdTTL: 600 }); // 10 minutes

// Cache frequently accessed data
app.use('/api/services', (req, res, next) => {
  const key = `services_${req.url}`;
  const cached = cache.get(key);
  
  if (cached) {
    return res.json(cached);
  }
  
  next();
});
```

## 📊 Monitoring & Logging

### Health Checks

The API includes a health check endpoint:

```
GET /health
```

Response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "uptime": 3600,
  "database": "connected",
  "environment": "production"
}
```

### Logging

Configure structured logging:

```typescript
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

### Monitoring Tools

1. **Application Performance Monitoring (APM)**
   - New Relic
   - DataDog
   - Sentry

2. **Infrastructure Monitoring**
   - AWS CloudWatch
   - Vercel Analytics
   - Grafana + Prometheus

3. **Error Tracking**
   - Sentry
   - Bugsnag
   - Rollbar

### Alerts

Set up alerts for:
- High error rates (>5%)
- Slow response times (>2s)
- High memory usage (>80%)
- Database connection failures
- WebSocket connection drops

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🔐 SSL/TLS Configuration

### Let's Encrypt (Free SSL)

```bash
# Install Certbot
sudo apt-get install certbot

# Get certificate
sudo certbot certonly --standalone -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📈 Scaling Considerations

### Horizontal Scaling

1. **Load Balancer**: Distribute traffic across multiple instances
2. **Database Connection Pooling**: Manage database connections efficiently
3. **Redis for Session Storage**: Share sessions across instances
4. **WebSocket Scaling**: Use Redis adapter for Socket.io

### Vertical Scaling

1. **Increase server resources** (CPU, RAM)
2. **Optimize database queries**
3. **Implement caching strategies**
4. **Use CDN for static assets**

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Timeout**
   - Check Supabase connection limits
   - Implement connection pooling
   - Add retry logic

2. **High Memory Usage**
   - Check for memory leaks
   - Optimize large data queries
   - Implement pagination

3. **WebSocket Connection Issues**
   - Check firewall settings
   - Verify SSL/TLS configuration
   - Monitor connection limits

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=debug
DEBUG=socket.io*
```

### Performance Profiling

```bash
# Install clinic.js
npm install -g clinic

# Profile your application
clinic doctor -- node dist/index.js
```

## 📋 Post-Deployment Checklist

- [ ] Health check endpoint responding
- [ ] All environment variables set
- [ ] SSL certificate valid
- [ ] CORS configured correctly
- [ ] Rate limiting working
- [ ] WebSocket connections working
- [ ] Database queries optimized
- [ ] Monitoring and alerts set up
- [ ] Backup strategy in place
- [ ] Documentation updated
