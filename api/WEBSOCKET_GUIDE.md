# WebSocket Real-time Chat Guide

This guide explains how to use the WebSocket real-time chat functionality in the VTech API.

## 🔌 Connection

### Client Connection

```javascript
import io from 'socket.io-client';

// Connect with authentication token
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your-supabase-jwt-token'
  },
  transports: ['websocket', 'polling']
});

// Handle connection events
socket.on('connect', () => {
  console.log('Connected to WebSocket server');
});

socket.on('disconnect', () => {
  console.log('Disconnected from WebSocket server');
});

socket.on('error', (error) => {
  console.error('WebSocket error:', error);
});
```

### Authentication

The WebSocket server requires a valid Supabase JWT token for authentication:

- Send token in `auth.token` during connection
- Or send in `Authorization` header as `Bearer <token>`
- Token is validated with <PERSON><PERSON><PERSON> on each connection

## 📨 Events

### Client → Server Events

#### Join Conversation
```javascript
// Join a conversation room
socket.emit('join_conversation', conversationId);

// Listen for confirmation
socket.on('conversation_joined', (data) => {
  console.log('Joined conversation:', data.conversationId);
});
```

#### Leave Conversation
```javascript
// Leave a conversation room
socket.emit('leave_conversation', conversationId);

// Listen for confirmation
socket.on('conversation_left', (data) => {
  console.log('Left conversation:', data.conversationId);
});
```

#### Typing Indicators
```javascript
// Start typing
socket.emit('typing_start', conversationId);

// Stop typing
socket.emit('typing_stop', conversationId);
```

#### Message Read Status
```javascript
// Mark message as read
socket.emit('message_read', messageId);
```

### Server → Client Events

#### New Message
```javascript
// Listen for new messages
socket.on('new_message', (message) => {
  console.log('New message received:', message);
  // message contains full MessageWithSender object
});
```

#### Message Updates
```javascript
// Listen for message updates (read status, etc.)
socket.on('message_updated', (messageData) => {
  console.log('Message updated:', messageData);
});
```

#### Typing Indicators
```javascript
// Listen for typing indicators from other users
socket.on('user_typing', (data) => {
  console.log(`${data.userName} is ${data.isTyping ? 'typing' : 'stopped typing'}`);
  // data: { userId, userName, isTyping, conversationId }
});
```

#### Message Read Status
```javascript
// Listen for message read confirmations
socket.on('message_read', (data) => {
  console.log('Message read by:', data.readBy);
  // data: { messageId, readBy, readAt }
});
```

## 🏗️ Implementation Examples

### React Hook for Real-time Chat

```typescript
import { useEffect, useState } from 'react';
import io, { Socket } from 'socket.io-client';

interface UseRealtimeChatProps {
  conversationId: string;
  token: string;
  onNewMessage: (message: MessageWithSender) => void;
  onTyping: (data: TypingData) => void;
}

export function useRealtimeChat({
  conversationId,
  token,
  onNewMessage,
  onTyping
}: UseRealtimeChatProps) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('ws://localhost:3001', {
      auth: { token },
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
      // Join conversation room
      newSocket.emit('join_conversation', conversationId);
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
    });

    newSocket.on('new_message', onNewMessage);
    newSocket.on('user_typing', onTyping);

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [conversationId, token]);

  const sendTyping = (isTyping: boolean) => {
    if (socket) {
      socket.emit(isTyping ? 'typing_start' : 'typing_stop', conversationId);
    }
  };

  const markMessageAsRead = (messageId: string) => {
    if (socket) {
      socket.emit('message_read', messageId);
    }
  };

  return {
    socket,
    isConnected,
    sendTyping,
    markMessageAsRead
  };
}
```

### React Native Implementation

```typescript
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

export function useRealtimeChat(conversationId: string, token: string) {
  const [socket, setSocket] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const newSocket = io('ws://your-api-domain.com', {
      auth: { token },
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
      newSocket.emit('join_conversation', conversationId);
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
    });

    newSocket.on('new_message', (message) => {
      setMessages(prev => [...prev, message]);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [conversationId, token]);

  return { socket, messages, isConnected };
}
```

## 🔧 API Integration

### Check User Online Status

```javascript
// Check if a user is online
fetch('/api/realtime/users/user-id/online', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(res => res.json())
.then(data => {
  console.log('User online:', data.data.isOnline);
});
```

### Get WebSocket Server Status

```javascript
// Get server status and connected users count
fetch('/api/realtime/status', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(res => res.json())
.then(data => {
  console.log('Connected users:', data.data.connectedUsers);
});
```

## 🛡️ Security Features

### Authentication
- JWT token validation on connection
- User context attached to socket
- Access control for conversation rooms

### Access Control
- Users can only join conversations they have access to
- Admin/staff can join any conversation
- Conversation access verified before joining

### Rate Limiting
- Connection rate limiting
- Event rate limiting (future enhancement)

## 🚀 Production Deployment

### Environment Variables
```env
# WebSocket Configuration
WS_PORT=3002
FRONTEND_URL=https://your-frontend.com
MOBILE_URL=https://your-mobile-app.com
```

### Vercel Configuration
The WebSocket server runs on the same port as the HTTP server in serverless environments.

### Load Balancing
For multiple instances, consider using Redis adapter:

```typescript
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';

const pubClient = createClient({ url: 'redis://localhost:6379' });
const subClient = pubClient.duplicate();

io.adapter(createAdapter(pubClient, subClient));
```

## 🐛 Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check token validity
   - Verify CORS settings
   - Check network connectivity

2. **Not Receiving Messages**
   - Ensure joined conversation room
   - Check conversation access permissions
   - Verify Supabase realtime is enabled

3. **Typing Indicators Not Working**
   - Check if other users are in the same room
   - Verify event names match exactly

### Debug Mode

Enable debug logging:

```javascript
localStorage.debug = 'socket.io-client:socket';
```

## 📊 Monitoring

### Metrics to Track
- Connected users count
- Message delivery rate
- Connection/disconnection events
- Error rates

### Health Check
```javascript
// Check WebSocket server health
fetch('/api/realtime/status')
  .then(res => res.json())
  .then(data => console.log('Server status:', data.data.status));
```
