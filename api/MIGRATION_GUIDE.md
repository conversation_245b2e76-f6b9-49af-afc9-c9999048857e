# Frontend Migration Guide

This guide explains how to migrate the frontend from direct Supabase calls to the new API endpoints.

## 🎯 Migration Overview

### Before (Direct Supabase)
```typescript
// Direct Supabase calls in frontend
const { data: conversations } = await supabase
  .from('conversations')
  .select('*')
  .eq('user_id', userId);
```

### After (API Calls)
```typescript
// API calls through our backend
const conversations = await apiClient.conversations.getConversations({
  page: 1,
  limit: 20
});
```

## 📚 API Client Library

Create this API client library in your frontend:

### Base API Client

```typescript
// lib/api-client.ts
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  timestamp: string;
}

class ApiClient {
  private baseUrl: string;
  private getToken: () => string | null;

  constructor(baseUrl: string, getToken: () => string | null) {
    this.baseUrl = baseUrl;
    this.getToken = getToken;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = this.getToken();
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'API request failed');
    }

    return response.json();
  }

  // Auth methods
  auth = {
    login: (data: { email: string; password: string }) =>
      this.request<ApiResponse<AuthResponse>>('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    register: (data: RegisterData) =>
      this.request<ApiResponse<AuthResponse>>('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(data),
      }),

    refresh: (refreshToken: string) =>
      this.request<ApiResponse<AuthResponse>>('/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refresh_token: refreshToken }),
      }),

    logout: () =>
      this.request<ApiResponse<{ success: boolean }>>('/api/auth/logout', {
        method: 'POST',
      }),

    getProfile: () =>
      this.request<ApiResponse<User>>('/api/auth/me'),
  };

  // Conversations methods
  conversations = {
    getConversations: (params?: GetConversationsParams) => {
      const query = new URLSearchParams(params as any).toString();
      return this.request<PaginatedResponse<ConversationWithUser>>(
        `/api/conversations?${query}`
      );
    },

    getConversation: (id: string) =>
      this.request<ApiResponse<ConversationWithUser>>(`/api/conversations/${id}`),

    createConversation: (data: CreateConversationData) =>
      this.request<ApiResponse<{ conversation: Conversation; isNew: boolean }>>(
        '/api/conversations',
        {
          method: 'POST',
          body: JSON.stringify(data),
        }
      ),

    markAsRead: (id: string) =>
      this.request<ApiResponse<{ success: boolean }>>(
        `/api/conversations/${id}/read`,
        { method: 'PUT' }
      ),

    getAvailableUsers: (role?: string) => {
      const query = role ? `?role=${role}` : '';
      return this.request<ApiResponse<AvailableUser[]>>(
        `/api/conversations/available-users${query}`
      );
    },
  };

  // Messages methods
  messages = {
    getMessages: (conversationId: string, params?: GetMessagesParams) => {
      const query = new URLSearchParams(params as any).toString();
      return this.request<PaginatedResponse<MessageWithSender>>(
        `/api/conversations/${conversationId}/messages?${query}`
      );
    },

    sendMessage: (conversationId: string, data: CreateMessageData) =>
      this.request<ApiResponse<MessageWithSender>>(
        `/api/conversations/${conversationId}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        }
      ),

    getMessage: (id: string) =>
      this.request<ApiResponse<MessageWithSender>>(`/api/messages/${id}`),

    markAsRead: (id: string) =>
      this.request<ApiResponse<{ success: boolean }>>(
        `/api/messages/${id}/read`,
        { method: 'PUT' }
      ),

    getUnreadCount: () =>
      this.request<ApiResponse<{ count: number }>>('/api/messages/unread-count'),
  };

  // Users methods
  users = {
    getUsers: (params?: GetUsersParams) => {
      const query = new URLSearchParams(params as any).toString();
      return this.request<PaginatedResponse<User>>(`/api/users?${query}`);
    },

    getUser: (id: string) =>
      this.request<ApiResponse<User>>(`/api/users/${id}`),

    updateUser: (id: string, data: UpdateUserData) =>
      this.request<ApiResponse<User>>(`/api/users/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),

    getCurrentUser: () =>
      this.request<ApiResponse<User>>('/api/users/me'),

    updateCurrentUser: (data: UpdateUserData) =>
      this.request<ApiResponse<User>>('/api/users/me', {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
  };
}

// Create and export the API client instance
export const apiClient = new ApiClient(
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  () => {
    // Get token from your auth context/store
    // This depends on your auth implementation
    return localStorage.getItem('supabase.auth.token') || null;
  }
);
```

## 🔄 Migration Steps

### Step 1: Replace Authentication

**Before:**
```typescript
// Old Supabase auth
const { data, error } = await supabase.auth.signInWithPassword({
  email,
  password
});
```

**After:**
```typescript
// New API auth
const response = await apiClient.auth.login({ email, password });
const { user, access_token } = response.data;
```

### Step 2: Replace Conversation Queries

**Before:**
```typescript
// Old direct query
const { data: conversations } = await supabase
  .from('conversations')
  .select(`
    *,
    user:users(id, name, email, phone, role, profile_data)
  `)
  .eq('user_id', userId)
  .order('last_message_at', { ascending: false });
```

**After:**
```typescript
// New API call
const response = await apiClient.conversations.getConversations({
  page: 1,
  limit: 20
});
const conversations = response.data.items;
```

### Step 3: Replace Message Operations

**Before:**
```typescript
// Old message sending
const { data: message } = await supabase
  .from('messages')
  .insert({
    conversation_id: conversationId,
    sender_id: userId,
    content: messageContent,
    is_system: false
  })
  .select(`
    *,
    sender:users(id, name, email, role, profile_data)
  `)
  .single();
```

**After:**
```typescript
// New API call
const response = await apiClient.messages.sendMessage(conversationId, {
  content: messageContent,
  is_system: false
});
const message = response.data;
```

### Step 4: Update Realtime Subscriptions

**Before:**
```typescript
// Old Supabase realtime
const channel = supabase
  .channel(`conversation-${conversationId}`)
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'messages',
    filter: `conversation_id=eq.${conversationId}`
  }, (payload) => {
    // Handle new message
  })
  .subscribe();
```

**After:**
```typescript
// New WebSocket connection
import io from 'socket.io-client';

const socket = io('ws://localhost:3001', {
  auth: { token: getAuthToken() }
});

socket.emit('join_conversation', conversationId);

socket.on('new_message', (message) => {
  // Handle new message
});
```

## 🎣 React Hooks Migration

### useRealtimeMessages Hook

**Before:**
```typescript
// Old hook using Supabase realtime
export function useRealtimeMessages(conversationId: string) {
  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    const channel = supabase
      .channel(`conversation-${conversationId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`
      }, (payload) => {
        setMessages(prev => [...prev, payload.new as Message]);
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId]);

  return messages;
}
```

**After:**
```typescript
// New hook using WebSocket
export function useRealtimeMessages(conversationId: string) {
  const [messages, setMessages] = useState<MessageWithSender[]>([]);
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    const newSocket = io('ws://localhost:3001', {
      auth: { token: getAuthToken() }
    });

    newSocket.on('connect', () => {
      newSocket.emit('join_conversation', conversationId);
    });

    newSocket.on('new_message', (message: MessageWithSender) => {
      setMessages(prev => [...prev, message]);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [conversationId]);

  return { messages, socket };
}
```

### useConversations Hook

**Before:**
```typescript
// Old hook with direct Supabase
export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchConversations() {
      const { data } = await supabase
        .from('conversations')
        .select('*')
        .order('last_message_at', { ascending: false });
      
      setConversations(data || []);
      setLoading(false);
    }

    fetchConversations();
  }, []);

  return { conversations, loading };
}
```

**After:**
```typescript
// New hook with API client
export function useConversations() {
  const [conversations, setConversations] = useState<ConversationWithUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({ page: 1, total: 0 });

  useEffect(() => {
    async function fetchConversations() {
      try {
        const response = await apiClient.conversations.getConversations({
          page: pagination.page,
          limit: 20
        });
        
        setConversations(response.data.items);
        setPagination(prev => ({ 
          ...prev, 
          total: response.data.pagination.total 
        }));
      } catch (error) {
        console.error('Failed to fetch conversations:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchConversations();
  }, [pagination.page]);

  return { conversations, loading, pagination };
}
```

## 🔧 Environment Configuration

Update your environment variables:

```env
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# For production
NEXT_PUBLIC_API_URL=https://your-api-domain.com
NEXT_PUBLIC_WS_URL=wss://your-api-domain.com
```

## 🧪 Testing Migration

### 1. Test Authentication Flow
```typescript
// Test login
const loginResult = await apiClient.auth.login({
  email: '<EMAIL>',
  password: 'password'
});
console.log('Login successful:', loginResult.data.user);

// Test token refresh
const refreshResult = await apiClient.auth.refresh(refreshToken);
console.log('Token refreshed:', refreshResult.data.access_token);
```

### 2. Test Conversation Operations
```typescript
// Test getting conversations
const conversations = await apiClient.conversations.getConversations();
console.log('Conversations:', conversations.data.items);

// Test sending message
const message = await apiClient.messages.sendMessage(conversationId, {
  content: 'Test message'
});
console.log('Message sent:', message.data);
```

### 3. Test WebSocket Connection
```typescript
// Test WebSocket
const socket = io('ws://localhost:3001', {
  auth: { token: getAuthToken() }
});

socket.on('connect', () => {
  console.log('WebSocket connected');
  socket.emit('join_conversation', conversationId);
});

socket.on('new_message', (message) => {
  console.log('New message received:', message);
});
```

## 🚀 Deployment Considerations

### 1. Gradual Migration
- Implement feature flags to switch between old and new implementations
- Migrate one feature at a time
- Keep both implementations running during transition

### 2. Error Handling
```typescript
// Robust error handling
try {
  const response = await apiClient.conversations.getConversations();
  return response.data.items;
} catch (error) {
  // Fallback to direct Supabase if API fails
  console.warn('API failed, falling back to Supabase:', error);
  return await fallbackToSupabase();
}
```

### 3. Performance Monitoring
- Monitor API response times
- Track error rates
- Compare performance with direct Supabase calls

## ✅ Migration Checklist

- [ ] Set up API client library
- [ ] Replace authentication flow
- [ ] Migrate conversation queries
- [ ] Migrate message operations
- [ ] Update realtime subscriptions
- [ ] Update React hooks
- [ ] Test all functionality
- [ ] Update environment configuration
- [ ] Deploy and monitor
- [ ] Remove old Supabase dependencies

## 🐛 Common Issues

### 1. Token Management
**Issue**: Token not being sent with requests
**Solution**: Ensure `getToken()` function returns valid token

### 2. CORS Errors
**Issue**: CORS errors when calling API
**Solution**: Check API CORS configuration matches frontend URL

### 3. WebSocket Connection Issues
**Issue**: WebSocket not connecting
**Solution**: Verify WebSocket URL and authentication token

### 4. Type Mismatches
**Issue**: TypeScript errors with API responses
**Solution**: Update type definitions to match API response format
