import { Response } from 'express';
import { ApiResponse, PaginatedResponse, PaginationParams } from '@/types/api.types';

/**
 * Send success response
 */
export function sendSuccess<T>(
  res: Response,
  data: T,
  message: string = 'Success',
  statusCode: number = 200
): void {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };
  res.status(statusCode).json(response);
}

/**
 * Send error response
 */
export function sendError(
  res: Response,
  message: string,
  statusCode: number = 400,
  errors?: any
): void {
  const response: ApiResponse<null> = {
    success: false,
    message,
    data: null,
    errors,
    timestamp: new Date().toISOString()
  };
  res.status(statusCode).json(response);
}

/**
 * Send paginated response
 */
export function sendPaginatedResponse<T>(
  res: Response,
  data: T[],
  total: number,
  pagination: PaginationParams,
  message: string = 'Success'
): void {
  const response: PaginatedResponse<T> = {
    success: true,
    message,
    data: {
      items: data,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit)
      }
    },
    timestamp: new Date().toISOString()
  };
  res.status(200).json(response);
}

/**
 * Send created response
 */
export function sendCreated<T>(
  res: Response,
  data: T,
  message: string = 'Created successfully'
): void {
  sendSuccess(res, data, message, 201);
}

/**
 * Send no content response
 */
export function sendNoContent(res: Response): void {
  res.status(204).send();
}

/**
 * Send not found response
 */
export function sendNotFound(
  res: Response,
  message: string = 'Resource not found'
): void {
  sendError(res, message, 404);
}

/**
 * Send unauthorized response
 */
export function sendUnauthorized(
  res: Response,
  message: string = 'Unauthorized'
): void {
  sendError(res, message, 401);
}

/**
 * Send forbidden response
 */
export function sendForbidden(
  res: Response,
  message: string = 'Forbidden'
): void {
  sendError(res, message, 403);
}

/**
 * Send validation error response
 */
export function sendValidationError(
  res: Response,
  errors: any,
  message: string = 'Validation failed'
): void {
  sendError(res, message, 400, errors);
}

/**
 * Send internal server error response
 */
export function sendInternalError(
  res: Response,
  message: string = 'Internal server error'
): void {
  sendError(res, message, 500);
}
