import { z } from "zod";

// Auth validation schemas
export const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  phone: z.string().optional(),
  role: z.enum(["customer", "technician"]).default("customer"),
  profile_data: z
    .object({
      avatar_url: z.string().url().optional(),
      address: z.string().optional(),
      date_of_birth: z.string().optional(),
      gender: z.enum(["male", "female", "other"]).optional(),
      emergency_contact: z
        .object({
          name: z.string(),
          phone: z.string(),
          relationship: z.string(),
        })
        .optional(),
      preferences: z
        .object({
          notifications: z.boolean().default(true),
          language: z.string().default("vi"),
          theme: z.enum(["light", "dark"]).default("light"),
        })
        .optional(),
    })
    .optional(),
});

export const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1, "Refresh token is required"),
});

// Conversation validation schemas
export const createConversationSchema = z.object({
  user_id: z.string().uuid("Invalid user ID"),
  user_role: z.enum(["customer", "technician"]),
  initial_message: z.string().optional(),
});

export const getConversationsSchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val) || 1)
    .optional(),
  limit: z
    .string()
    .transform((val) => parseInt(val) || 20)
    .optional(),
  search: z.string().optional(),
  user_role: z.enum(["customer", "technician"]).optional(),
  unread_only: z
    .string()
    .transform((val) => val === "true")
    .optional(),
  created_after: z.string().optional(),
  created_before: z.string().optional(),
});

// Message validation schemas
export const createMessageSchema = z.object({
  content: z.string().min(1, "Message content is required"),
  is_system: z.boolean().optional(),
  message_data: z.record(z.any()).optional(),
});

export const getMessagesSchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val) || 1)
    .optional(),
  limit: z
    .string()
    .transform((val) => parseInt(val) || 50)
    .optional(),
  created_at_lt: z.string().optional(),
});

// User validation schemas
export const updateUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  phone: z.string().optional(),
  profile_data: z
    .object({
      avatar_url: z.string().url().optional(),
      address: z.string().optional(),
      date_of_birth: z.string().optional(),
      gender: z.enum(["male", "female", "other"]).optional(),
      emergency_contact: z
        .object({
          name: z.string(),
          phone: z.string(),
          relationship: z.string(),
        })
        .optional(),
      preferences: z
        .object({
          notifications: z.boolean().optional(),
          language: z.string().optional(),
          theme: z.enum(["light", "dark"]).optional(),
        })
        .optional(),
    })
    .optional(),
});

export const getUsersSchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val) || 1)
    .optional(),
  limit: z
    .string()
    .transform((val) => parseInt(val) || 20)
    .optional(),
  search: z.string().optional(),
  role: z.enum(["admin", "staff", "customer", "technician"]).optional(),
});

// Pagination validation
export const paginationSchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val) || 1)
    .optional(),
  limit: z
    .string()
    .transform((val) => parseInt(val) || 20)
    .optional(),
});

// UUID validation
export const uuidSchema = z.object({
  id: z.string().uuid("Invalid ID format"),
});

// Create user validation (admin only)
export const createUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  phone: z.string().optional(),
  role: z.enum(["admin", "staff", "customer", "technician"]),
  profile_data: z
    .object({
      avatar_url: z.string().url().optional(),
      address: z.string().optional(),
      date_of_birth: z.string().optional(),
      gender: z.enum(["male", "female", "other"]).optional(),
      emergency_contact: z
        .object({
          name: z.string(),
          phone: z.string(),
          relationship: z.string(),
        })
        .optional(),
      preferences: z
        .object({
          notifications: z.boolean().default(true),
          language: z.string().default("vi"),
          theme: z.enum(["light", "dark"]).default("light"),
        })
        .optional(),
    })
    .optional(),
});
