import { Request, Response, NextFunction } from "express";
import { ZodError } from "zod";
import { logger } from "@/utils/logger";
import { sendError, sendInternalError } from "@/utils/response";

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  public statusCode: number;
  public errors?: any;

  constructor(message: string, statusCode: number = 500, errors?: any) {
    super(message);
    this.statusCode = statusCode;
    this.errors = errors;
    this.name = "ApiError";
  }
}

/**
 * Error handling middleware
 */
export function errorMiddleware(
  error: any,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _next: NextFunction
): void {
  logger.error("Error caught by middleware:", {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationErrors = error.errors.map((err) => ({
      field: err.path.join("."),
      message: err.message,
      code: err.code,
    }));

    sendError(res, "Validation failed", 400, validationErrors);
    return;
  }

  // Handle custom API errors
  if (error instanceof ApiError) {
    sendError(res, error.message, error.statusCode, error.errors);
    return;
  }

  // Handle Supabase errors
  if (error.code && error.message) {
    let statusCode = 500;
    let message = error.message;

    // Map common Supabase error codes
    switch (error.code) {
      case "PGRST116": // Not found
        statusCode = 404;
        message = "Resource not found";
        break;
      case "PGRST301": // Forbidden
        statusCode = 403;
        message = "Access denied";
        break;
      case "23505": // Unique violation
        statusCode = 409;
        message = "Resource already exists";
        break;
      case "23503": // Foreign key violation
        statusCode = 400;
        message = "Invalid reference";
        break;
      case "42501": // Insufficient privilege
        statusCode = 403;
        message = "Insufficient permissions";
        break;
    }

    sendError(res, message, statusCode);
    return;
  }

  // Handle JWT errors
  if (error.name === "JsonWebTokenError") {
    sendError(res, "Invalid token", 401);
    return;
  }

  if (error.name === "TokenExpiredError") {
    sendError(res, "Token expired", 401);
    return;
  }

  // Handle multer errors (file upload)
  if (error.code === "LIMIT_FILE_SIZE") {
    sendError(res, "File too large", 413);
    return;
  }

  if (error.code === "LIMIT_FILE_COUNT") {
    sendError(res, "Too many files", 413);
    return;
  }

  if (error.code === "LIMIT_UNEXPECTED_FILE") {
    sendError(res, "Unexpected file field", 400);
    return;
  }

  // Handle syntax errors
  if (error instanceof SyntaxError && "body" in error) {
    sendError(res, "Invalid JSON in request body", 400);
    return;
  }

  // Default internal server error
  sendInternalError(res, "An unexpected error occurred");
}

/**
 * 404 handler middleware
 */
export function notFoundMiddleware(
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _next: NextFunction
): void {
  sendError(res, `Route ${req.originalUrl} not found`, 404);
}

/**
 * Async error handler wrapper
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
