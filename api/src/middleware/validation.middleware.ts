import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { logger } from '@/utils/logger';

/**
 * Validation middleware using Zod schemas
 */
export function validateRequest(
  schema: ZodSchema,
  property: 'body' | 'query' | 'params' = 'body'
) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const dataToValidate = req[property];
      
      // Parse and validate the data
      const validatedData = schema.parse(dataToValidate);
      
      // Replace the original data with validated data
      req[property] = validatedData;
      
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }));
        
        logger.warn('Validation failed:', { errors: validationErrors });
        
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          data: null,
          errors: validationErrors,
          timestamp: new Date().toISOString()
        });
        return;
      }
      
      logger.error('Validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error',
        data: null,
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Validate pagination parameters
 */
export function validatePagination(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    
    // Validate page and limit
    if (page < 1) {
      res.status(400).json({
        success: false,
        message: 'Page must be greater than 0',
        data: null,
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    if (limit < 1 || limit > 100) {
      res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100',
        data: null,
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    // Add validated pagination to request
    req.query.page = page.toString();
    req.query.limit = limit.toString();
    
    next();
  } catch (error) {
    logger.error('Pagination validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Pagination validation error',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
}
