import { Response, NextFunction } from "express";
import { supabase } from "@/config/database";
import { AuthenticatedRequest } from "@/types/api.types";
import { AuthUser } from "@/types/auth.types";
import { logger } from "@/utils/logger";

/**
 * Authentication middleware that validates Supabase JWT tokens
 */
export async function authMiddleware(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      res.status(401).json({
        success: false,
        message: "Authorization token required",
        data: null,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Validate token with Supabase
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      logger.warn("Invalid token attempt:", { error: error?.message });
      res.status(401).json({
        success: false,
        message: "Invalid or expired token",
        data: null,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Attach user to request
    req.user = user as unknown as AuthUser;

    logger.debug("User authenticated:", { userId: user.id, email: user.email });
    next();
  } catch (error) {
    logger.error("Authentication middleware error:", error);
    res.status(500).json({
      success: false,
      message: "Authentication error",
      data: null,
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export async function optionalAuthMiddleware(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      next();
      return;
    }

    const token = authHeader.substring(7);

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (!error && user) {
      req.user = user as unknown as AuthUser;
      logger.debug("Optional auth - user authenticated:", { userId: user.id });
    }

    next();
  } catch (error) {
    logger.error("Optional authentication middleware error:", error);
    next(); // Continue without authentication
  }
}

/**
 * Role-based authorization middleware
 */
export function requireRole(...roles: string[]) {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: "Authentication required",
        data: null,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const userRole = req.user.user_metadata?.role;

    if (!userRole || !roles.includes(userRole)) {
      logger.warn("Insufficient permissions:", {
        userId: req.user.id,
        userRole,
        requiredRoles: roles,
      });

      res.status(403).json({
        success: false,
        message: "Insufficient permissions",
        data: null,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    next();
  };
}

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole("admin");

/**
 * Admin or staff middleware
 */
export const requireAdminOrStaff = requireRole("admin", "staff");
