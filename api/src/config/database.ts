import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { logger } from "@/utils/logger";

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are required");
}

/**
 * Supabase client with service role for server-side operations
 * This client bypasses RLS and has full database access
 */
export const supabase: SupabaseClient = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    db: {
      schema: "public",
    },
  }
);

/**
 * Test database connection
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const { error } = await supabase.from("users").select("count").limit(1);

    if (error) {
      logger.error("Database connection test failed:", error);
      return false;
    }

    logger.info("Database connection successful");
    return true;
  } catch (error) {
    logger.error("Database connection error:", error);
    return false;
  }
}

/**
 * Create a user-scoped client for RLS operations
 * This client respects Row Level Security policies
 */
export function createUserScopedClient(userToken: string): SupabaseClient {
  return createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
      },
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
}
