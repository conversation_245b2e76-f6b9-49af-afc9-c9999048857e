import express from "express";
import helmet from "helmet";
import { config, validateConfig } from "@/config/app";
import { testDatabaseConnection } from "@/config/database";
import { corsMiddleware } from "@/middleware/cors.middleware";
import { generalRateLimit } from "@/middleware/rate-limit.middleware";
import {
  errorMiddleware,
  notFoundMiddleware,
} from "@/middleware/error.middleware";
import { logger } from "@/utils/logger";

// Validate configuration
validateConfig();

const app = express();

// Trust proxy for accurate IP addresses
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  })
);

// CORS middleware
app.use(corsMiddleware);

// Rate limiting
app.use("/api", generalRateLimit);

// Body parsing middleware
app.use(
  express.json({
    limit: "10mb",
    verify: (req, res, buf) => {
      try {
        JSON.parse(buf.toString());
      } catch (e) {
        (res as any).status(400).json({
          success: false,
          message: "Invalid JSON in request body",
          data: null,
          timestamp: new Date().toISOString(),
        });
        throw new Error("Invalid JSON");
      }
    },
  })
);

app.use(
  express.urlencoded({
    extended: true,
    limit: "10mb",
  })
);

// Request logging middleware
app.use((req, res, next) => {
  logger.http(`${req.method} ${req.url}`, {
    ip: req.ip,
    userAgent: req.get("User-Agent"),
    timestamp: new Date().toISOString(),
  });
  next();
});

// Health check endpoint
app.get("/health", async (req, res) => {
  try {
    const dbConnected = await testDatabaseConnection();

    res.json({
      status: dbConnected ? "ok" : "error",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "1.0.0",
      uptime: process.uptime(),
      database: dbConnected ? "connected" : "disconnected",
      environment: config.nodeEnv,
    });
  } catch (error) {
    logger.error("Health check failed:", error);
    res.status(500).json({
      status: "error",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "1.0.0",
      uptime: process.uptime(),
      database: "disconnected",
      environment: config.nodeEnv,
    });
  }
});

// Root endpoint
app.get("/", (req, res) => {
  res.json({
    success: true,
    message: "VTech API Server",
    data: {
      version: process.env.npm_package_version || "1.0.0",
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
});

// API routes
import authRoutes from "@/routes/auth.routes";
import userRoutes from "@/routes/users.routes";
import conversationRoutes from "@/routes/conversations.routes";
import messageRoutes from "@/routes/messages.routes";
import realtimeRoutes from "@/routes/realtime.routes";

app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/conversations", conversationRoutes);
app.use("/api", messageRoutes); // Messages routes include conversation prefix
app.use("/api/realtime", realtimeRoutes);

// TODO: Add remaining routes
// app.use('/api/orders', orderRoutes);
// app.use('/api/services', serviceRoutes);
// app.use('/api/categories', categoryRoutes);
// app.use('/api/files', fileRoutes);
// app.use('/api/notifications', notificationRoutes);

// 404 handler
app.use(notFoundMiddleware);

// Error handling middleware (must be last)
app.use(errorMiddleware);

export default app;
