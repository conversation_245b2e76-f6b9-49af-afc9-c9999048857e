import { Router } from 'express';
import { ConversationsController } from '@/controllers/conversations.controller';
import { authMiddleware, requireAdminOrStaff, requireAdmin } from '@/middleware/auth.middleware';
import { validateRequest } from '@/middleware/validation.middleware';
import { 
  createConversationSchema,
  getConversationsSchema,
  uuidSchema
} from '@/utils/validation';

const router = Router();
const conversationsController = new ConversationsController();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * GET /api/conversations/stats
 * Get conversation statistics (admin/staff only)
 */
router.get(
  '/stats',
  requireAdminOrStaff,
  conversationsController.getConversationStats
);

/**
 * GET /api/conversations/available-users
 * Get users available for creating conversations (admin/staff only)
 */
router.get(
  '/available-users',
  requireAdminOrStaff,
  conversationsController.getAvailableUsers
);

/**
 * GET /api/conversations
 * Get conversations for the current user
 */
router.get(
  '/',
  validateRequest(getConversationsSchema, 'query'),
  conversationsController.getConversations
);

/**
 * POST /api/conversations
 * Create a new conversation (admin/staff only)
 */
router.post(
  '/',
  requireAdminOrStaff,
  validateRequest(createConversationSchema, 'body'),
  conversationsController.createConversation
);

/**
 * GET /api/conversations/:id
 * Get conversation details
 */
router.get(
  '/:id',
  validateRequest(uuidSchema, 'params'),
  conversationsController.getConversation
);

/**
 * PUT /api/conversations/:id/read
 * Mark conversation as read
 */
router.put(
  '/:id/read',
  validateRequest(uuidSchema, 'params'),
  conversationsController.markAsRead
);

/**
 * DELETE /api/conversations/:id
 * Delete conversation (admin only)
 */
router.delete(
  '/:id',
  requireAdmin,
  validateRequest(uuidSchema, 'params'),
  conversationsController.deleteConversation
);

export default router;
