import { Router } from "express";
import { UsersController } from "@/controllers/users.controller";
import {
  authMiddleware,
  requireAdmin,
  requireAdminOrStaff,
} from "@/middleware/auth.middleware";
import { validateRequest } from "@/middleware/validation.middleware";
import {
  updateUserSchema,
  createUserSchema,
  getUsersSchema,
  uuidSchema,
} from "@/utils/validation";

const router = Router();
const usersController = new UsersController();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * GET /api/users/stats
 * Get user statistics (admin only)
 */
router.get("/stats", requireAdmin, usersController.getUserStats);

/**
 * GET /api/users/me
 * Get current user profile
 */
router.get("/me", usersController.getCurrentUser);

/**
 * PUT /api/users/me
 * Update current user profile
 */
router.put(
  "/me",
  validateRequest(updateUserSchema, "body"),
  usersController.updateCurrentUser
);

/**
 * GET /api/users
 * Get users with filters and pagination (admin/staff only)
 */
router.get(
  "/",
  requireAdminOrStaff,
  validateRequest(getUsersSchema, "query"),
  usersController.getUsers
);

/**
 * POST /api/users
 * Create new user (admin only)
 */
router.post(
  "/",
  requireAdmin,
  validateRequest(createUserSchema, "body"),
  usersController.createUser
);

/**
 * GET /api/users/:id
 * Get user by ID
 */
router.get(
  "/:id",
  validateRequest(uuidSchema, "params"),
  usersController.getUserById
);

/**
 * PUT /api/users/:id
 * Update user profile
 */
router.put(
  "/:id",
  validateRequest(uuidSchema, "params"),
  validateRequest(updateUserSchema, "body"),
  usersController.updateUser
);

/**
 * DELETE /api/users/:id
 * Delete user (admin only)
 */
router.delete(
  "/:id",
  requireAdmin,
  validateRequest(uuidSchema, "params"),
  usersController.deleteUser
);

export default router;
