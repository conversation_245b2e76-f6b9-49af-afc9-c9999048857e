import { Router } from 'express';
import { AuthController } from '@/controllers/auth.controller';
import { authMiddleware } from '@/middleware/auth.middleware';
import { authRateLimit } from '@/middleware/rate-limit.middleware';
import { validateRequest } from '@/middleware/validation.middleware';
import { 
  loginSchema,
  registerSchema,
  refreshTokenSchema
} from '@/utils/validation';

const router = Router();
const authController = new AuthController();

/**
 * POST /api/auth/login
 * User login
 */
router.post(
  '/login',
  authRateLimit,
  validateRequest(loginSchema, 'body'),
  authController.login
);

/**
 * POST /api/auth/register
 * User registration
 */
router.post(
  '/register',
  authRateLimit,
  validateRequest(registerSchema, 'body'),
  authController.register
);

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
router.post(
  '/refresh',
  validateRequest(refreshTokenSchema, 'body'),
  authController.refresh
);

/**
 * POST /api/auth/logout
 * User logout (requires authentication)
 */
router.post(
  '/logout',
  authMiddleware,
  authController.logout
);

/**
 * GET /api/auth/me
 * Get current user profile (requires authentication)
 */
router.get(
  '/me',
  authMiddleware,
  authController.getProfile
);

/**
 * GET /api/auth/verify
 * Verify token validity (requires authentication)
 */
router.get(
  '/verify',
  authMiddleware,
  authController.verify
);

export default router;
