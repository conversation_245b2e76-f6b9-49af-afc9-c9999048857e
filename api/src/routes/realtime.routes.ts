import { Router } from "express";
import { authMiddleware } from "@/middleware/auth.middleware";
import { RealtimeService } from "@/services/realtime.service";
import { AuthenticatedRequest } from "@/types/api.types";

const router = Router();
const realtimeService = RealtimeService.getInstance();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * GET /api/realtime/status
 * Get WebSocket server status
 */
router.get("/status", (req: AuthenticatedRequest, res) => {
  const connectedUsers = realtimeService.getConnectedUsersCount();

  return res.json({
    success: true,
    message: "WebSocket server status",
    data: {
      status: "running",
      connectedUsers,
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * GET /api/realtime/users/:userId/online
 * Check if a user is online
 */
router.get("/users/:userId/online", (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;
  const isOnline = realtimeService.isUserOnline(userId);

  return res.json({
    success: true,
    message: "User online status",
    data: {
      userId,
      isOnline,
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * POST /api/realtime/broadcast/user/:userId
 * Send a message to a specific user (admin only)
 */
router.post("/broadcast/user/:userId", (req: AuthenticatedRequest, res) => {
  const userRole = req.user?.user_metadata?.role;

  if (userRole !== "admin" && userRole !== "staff") {
    return res.status(403).json({
      success: false,
      message: "Insufficient permissions",
      data: null,
      timestamp: new Date().toISOString(),
    });
  }

  const { userId } = req.params;
  const { event, data } = req.body;

  if (!event || !data) {
    return res.status(400).json({
      success: false,
      message: "Event and data are required",
      data: null,
      timestamp: new Date().toISOString(),
    });
  }

  realtimeService.broadcastToUser(userId, event, data);

  return res.json({
    success: true,
    message: "Message broadcasted to user",
    data: {
      userId,
      event,
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * POST /api/realtime/broadcast/conversation/:conversationId
 * Send a message to a conversation (admin only)
 */
router.post(
  "/broadcast/conversation/:conversationId",
  (req: AuthenticatedRequest, res) => {
    const userRole = req.user?.user_metadata?.role;

    if (userRole !== "admin" && userRole !== "staff") {
      return res.status(403).json({
        success: false,
        message: "Insufficient permissions",
        data: null,
        timestamp: new Date().toISOString(),
      });
    }

    const { conversationId } = req.params;
    const { event, data } = req.body;

    if (!event || !data) {
      return res.status(400).json({
        success: false,
        message: "Event and data are required",
        data: null,
        timestamp: new Date().toISOString(),
      });
    }

    realtimeService.broadcastToConversation(conversationId, event, data);

    return res.json({
      success: true,
      message: "Message broadcasted to conversation",
      data: {
        conversationId,
        event,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    });
  }
);

export default router;
