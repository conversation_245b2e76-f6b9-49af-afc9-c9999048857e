import { Router } from "express";
import { MessagesController } from "@/controllers/messages.controller";
import { authMiddleware, requireAdmin } from "@/middleware/auth.middleware";
import { validateRequest } from "@/middleware/validation.middleware";
import { z } from "zod";
import {
  createMessageSchema,
  getMessagesSchema,
  uuidSchema,
} from "@/utils/validation";

const router = Router();
const messagesController = new MessagesController();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * GET /api/messages/unread-count
 * Get unread message count for current user
 */
router.get("/unread-count", messagesController.getUnreadCount);

/**
 * GET /api/conversations/:conversationId/messages
 * Get messages for a conversation
 */
router.get(
  "/conversations/:conversationId/messages",
  validateRequest(z.object({ conversationId: z.string().uuid() }), "params"),
  validateRequest(getMessagesSchema, "query"),
  messagesController.getMessages
);

/**
 * POST /api/conversations/:conversationId/messages
 * Send a new message
 */
router.post(
  "/conversations/:conversationId/messages",
  validateRequest(z.object({ conversationId: z.string().uuid() }), "params"),
  validateRequest(createMessageSchema, "body"),
  messagesController.sendMessage
);

/**
 * POST /api/conversations/:conversationId/messages/bulk-read
 * Mark all messages in conversation as read
 */
router.post(
  "/conversations/:conversationId/messages/bulk-read",
  validateRequest(z.object({ conversationId: z.string().uuid() }), "params"),
  messagesController.markConversationMessagesAsRead
);

/**
 * GET /api/messages/:id
 * Get specific message
 */
router.get(
  "/:id",
  validateRequest(uuidSchema, "params"),
  messagesController.getMessage
);

/**
 * PUT /api/messages/:id/read
 * Mark message as read
 */
router.put(
  "/:id/read",
  validateRequest(uuidSchema, "params"),
  messagesController.markMessageAsRead
);

/**
 * DELETE /api/messages/:id
 * Delete message (admin only)
 */
router.delete(
  "/:id",
  requireAdmin,
  validateRequest(uuidSchema, "params"),
  messagesController.deleteMessage
);

export default router;
