import { Request, Response } from 'express';
import { BaseController } from './base.controller';
import { AuthService } from '@/services/auth.service';
import { AuthenticatedRequest } from '@/types/api.types';
import { LoginData, RegisterData } from '@/types/auth.types';

export class AuthController extends BaseController {
  private authService: AuthService;

  constructor() {
    super();
    this.authService = new AuthService();
  }

  /**
   * POST /api/auth/login
   * User login
   */
  login = this.asyncHandler(async (req: Request, res: Response) => {
    const data: LoginData = req.body;
    
    this.logAction('login', undefined, { email: data.email });
    
    try {
      const result = await this.authService.login(data);
      
      this.logAction('login_success', result.user.id, { email: data.email });
      
      this.sendSuccess(
        res,
        result,
        'Login successful'
      );
    } catch (error) {
      this.logError('login', error, undefined);
      throw error;
    }
  });

  /**
   * POST /api/auth/register
   * User registration
   */
  register = this.asyncHandler(async (req: Request, res: Response) => {
    const data: RegisterData = req.body;
    
    this.logAction('register', undefined, { email: data.email, role: data.role });
    
    try {
      const result = await this.authService.register(data);
      
      this.logAction('register_success', result.user.id, { email: data.email, role: data.role });
      
      // If email confirmation is required
      if (!result.access_token) {
        this.sendSuccess(
          res,
          { user: result.user },
          'Registration successful. Please check your email for confirmation.',
          201
        );
        return;
      }
      
      this.sendSuccess(
        res,
        result,
        'Registration successful',
        201
      );
    } catch (error) {
      this.logError('register', error, undefined);
      throw error;
    }
  });

  /**
   * POST /api/auth/refresh
   * Refresh access token
   */
  refresh = this.asyncHandler(async (req: Request, res: Response) => {
    const { refresh_token } = req.body;
    
    this.logAction('refresh_token');
    
    try {
      const result = await this.authService.refreshToken(refresh_token);
      
      this.logAction('refresh_token_success', result.user.id);
      
      this.sendSuccess(
        res,
        result,
        'Token refreshed successfully'
      );
    } catch (error) {
      this.logError('refresh_token', error);
      throw error;
    }
  });

  /**
   * POST /api/auth/logout
   * User logout
   */
  logout = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const userId = req.user?.id;
    
    this.logAction('logout', userId);
    
    try {
      await this.authService.logout(token);
      
      this.logAction('logout_success', userId);
      
      this.sendSuccess(
        res,
        { success: true },
        'Logout successful'
      );
    } catch (error) {
      this.logError('logout', error, userId);
      // Don't throw error for logout - always return success
      this.sendSuccess(
        res,
        { success: true },
        'Logout successful'
      );
    }
  });

  /**
   * GET /api/auth/me
   * Get current user profile
   */
  getProfile = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = this.extractUserId(req);
    
    this.logAction('get_profile', userId);
    
    try {
      const user = await this.authService.getUserProfile(userId);
      
      this.logAction('get_profile_success', userId);
      
      this.sendSuccess(
        res,
        user,
        'Profile retrieved successfully'
      );
    } catch (error) {
      this.logError('get_profile', error, userId);
      throw error;
    }
  });

  /**
   * GET /api/auth/verify
   * Verify token validity
   */
  verify = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = this.extractUserId(req);
    const userRole = this.extractUserRole(req);
    
    this.logAction('verify_token', userId);
    
    this.sendSuccess(
      res,
      {
        valid: true,
        user: {
          id: userId,
          role: userRole
        }
      },
      'Token is valid'
    );
  });
}
