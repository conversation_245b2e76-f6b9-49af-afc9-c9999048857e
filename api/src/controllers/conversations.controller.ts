import { Response } from "express";
import { <PERSON><PERSON><PERSON>roller } from "./base.controller";
import { ConversationService } from "@/services/conversation.service";
import { AuthenticatedRequest } from "@/types/api.types";
import {
  GetConversationsFilters,
  CreateConversationData,
} from "@/types/conversation.types";
import { UserRole } from "@/types/user.types";

export class ConversationsController extends BaseController {
  private conversationService: ConversationService;

  constructor() {
    super();
    this.conversationService = new ConversationService();
  }

  /**
   * GET /api/conversations
   * Get conversations for the current user
   */
  getConversations = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);
      const pagination = this.extractPagination(req);

      const filters: GetConversationsFilters = {
        ...pagination,
        search: req.query.search as string,
        user_role: req.query.user_role as "customer" | "technician",
        unread_only: req.query.unread_only === "true",
        created_after: req.query.created_after as string,
        created_before: req.query.created_before as string,
      };

      this.logAction("get_conversations", userId, { filters });

      const result = await this.conversationService.getConversations(
        userId,
        userRole,
        filters
      );

      this.sendPaginatedResponse(
        res,
        result.items,
        result.total,
        pagination,
        "Conversations retrieved successfully"
      );
    }
  );

  /**
   * POST /api/conversations
   * Create a new conversation
   */
  createConversation = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);
      // const userRole = this.extractUserRole(req);

      // Only admin/staff can create conversations
      if (!this.isAdminOrStaff(req)) {
        return this.sendForbidden(
          res,
          "Only admin and staff can create conversations"
        );
      }

      const data: CreateConversationData = req.body;

      this.logAction("create_conversation", userId, { data });

      const result = await this.conversationService.createConversation(
        userId,
        data
      );

      const statusCode = result.isNew ? 201 : 200;
      const message = result.isNew
        ? "Conversation created successfully"
        : "Existing conversation retrieved";

      this.sendSuccess(res, result, message, statusCode);
    }
  );

  /**
   * GET /api/conversations/:id
   * Get conversation details
   */
  getConversation = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.id;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      this.logAction("get_conversation", userId, { conversationId });

      const conversation = await this.conversationService.getConversationById(
        conversationId,
        userId,
        userRole
      );

      if (!conversation) {
        return this.sendNotFound(res, "Conversation not found");
      }

      this.sendSuccess(
        res,
        conversation,
        "Conversation retrieved successfully"
      );
    }
  );

  /**
   * PUT /api/conversations/:id/read
   * Mark conversation as read
   */
  markAsRead = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.id;
      const userId = this.extractUserId(req);

      this.logAction("mark_conversation_read", userId, { conversationId });

      await this.conversationService.markAsRead(conversationId, userId);

      this.sendSuccess(res, { success: true }, "Conversation marked as read");
    }
  );

  /**
   * GET /api/conversations/available-users
   * Get users available for creating conversations
   */
  getAvailableUsers = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);

      // Only admin/staff can access this
      if (!this.isAdminOrStaff(req)) {
        return this.sendForbidden(res, "Insufficient permissions");
      }

      const role = req.query.role as UserRole;

      this.logAction("get_available_users", userId, { role });

      const users = await this.conversationService.getAvailableUsers(role);

      this.sendSuccess(res, users, "Available users retrieved successfully");
    }
  );

  /**
   * GET /api/conversations/stats
   * Get conversation statistics
   */
  getConversationStats = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      this.logAction("get_conversation_stats", userId);

      const stats = await this.conversationService.getConversationStats(
        userId,
        userRole
      );

      this.sendSuccess(
        res,
        stats,
        "Conversation statistics retrieved successfully"
      );
    }
  );

  /**
   * DELETE /api/conversations/:id
   * Delete conversation (admin only)
   */
  deleteConversation = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.id;
      const userId = this.extractUserId(req);

      // Only admin can delete conversations
      if (!this.isAdmin(req)) {
        return this.sendForbidden(res, "Only admin can delete conversations");
      }

      this.logAction("delete_conversation", userId, { conversationId });

      // Check if conversation exists
      const conversation = await this.conversationService.getConversationById(
        conversationId,
        userId,
        "admin"
      );

      if (!conversation) {
        return this.sendNotFound(res, "Conversation not found");
      }

      // Delete conversation (this will cascade to messages)
      const { error } = await this.conversationService
        .getClient()
        .from("conversations")
        .delete()
        .eq("id", conversationId);

      if (error) {
        this.logError("delete_conversation", error, userId);
        throw new Error("Failed to delete conversation");
      }

      this.sendSuccess(
        res,
        { success: true },
        "Conversation deleted successfully"
      );
    }
  );
}
