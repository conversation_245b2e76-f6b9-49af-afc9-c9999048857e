import { Response } from "express";
import { Base<PERSON><PERSON>roller } from "./base.controller";
import { MessageService } from "@/services/message.service";
import { RealtimeService } from "@/services/realtime.service";
import { AuthenticatedRequest } from "@/types/api.types";
import {
  GetMessagesFilters,
  CreateMessageData,
} from "@/types/conversation.types";

export class MessagesController extends BaseController {
  private messageService: MessageService;
  private realtimeService: RealtimeService;

  constructor() {
    super();
    this.messageService = new MessageService();
    this.realtimeService = RealtimeService.getInstance();
  }

  /**
   * GET /api/conversations/:conversationId/messages
   * Get messages for a conversation
   */
  getMessages = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.conversationId;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);
      const pagination = this.extractPagination(req);

      const filters: GetMessagesFilters = {
        conversation_id: conversationId,
        ...pagination,
        created_at_lt: req.query.created_at_lt as string,
        created_at_gt: req.query.created_at_gt as string,
      };

      this.logAction("get_messages", userId, { conversationId, filters });

      // Verify user has access to this conversation (for non-admin/staff)
      if (userRole !== "admin" && userRole !== "staff") {
        const hasAccess = await this.messageService.verifyConversationAccess(
          conversationId,
          userId
        );

        if (!hasAccess) {
          return this.sendForbidden(res, "Access denied to this conversation");
        }
      }

      const result = await this.messageService.getMessages(filters);

      this.sendPaginatedResponse(
        res,
        result.items,
        result.total,
        pagination,
        "Messages retrieved successfully"
      );
    }
  );

  /**
   * POST /api/conversations/:conversationId/messages
   * Send a new message
   */
  sendMessage = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.conversationId;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      const data: CreateMessageData = {
        ...req.body,
        conversation_id: conversationId,
        sender_id: userId,
      };

      this.logAction("send_message", userId, {
        conversationId,
        content: data.content,
      });

      // Verify user has access to this conversation (for non-admin/staff)
      if (userRole !== "admin" && userRole !== "staff") {
        const hasAccess = await this.messageService.verifyConversationAccess(
          conversationId,
          userId
        );

        if (!hasAccess) {
          return this.sendForbidden(res, "Access denied to this conversation");
        }
      }

      const message = await this.messageService.sendMessage(data, userRole);

      // Note: Message broadcasting is handled automatically by Supabase realtime
      // The RealtimeService listens to database changes and broadcasts to connected clients

      this.sendCreated(res, message, "Message sent successfully");
    }
  );

  /**
   * GET /api/messages/:id
   * Get specific message
   */
  getMessage = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const messageId = req.params.id;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      this.logAction("get_message", userId, { messageId });

      // Admin/staff can access any message, others need access verification
      let message;
      if (userRole === "admin" || userRole === "staff") {
        const { data, error } = await this.messageService
          .getClient()
          .from("messages")
          .select(
            `
          *,
          sender:users(id, name, email, role, profile_data)
        `
          )
          .eq("id", messageId)
          .single();

        if (error || !data) {
          return this.sendNotFound(res, "Message not found");
        }
        message = data;
      } else {
        message = await this.messageService.getMessageById(messageId, userId);
      }

      if (!message) {
        return this.sendNotFound(res, "Message not found");
      }

      this.sendSuccess(res, message, "Message retrieved successfully");
    }
  );

  /**
   * PUT /api/messages/:id/read
   * Mark message as read
   */
  markMessageAsRead = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const messageId = req.params.id;
      const userId = this.extractUserId(req);

      this.logAction("mark_message_read", userId, { messageId });

      await this.messageService.markMessageAsRead(messageId, userId);

      this.sendSuccess(res, { success: true }, "Message marked as read");
    }
  );

  /**
   * DELETE /api/messages/:id
   * Delete message (admin only)
   */
  deleteMessage = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const messageId = req.params.id;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      this.logAction("delete_message", userId, { messageId });

      await this.messageService.deleteMessage(messageId, userId, userRole);

      this.sendSuccess(res, { success: true }, "Message deleted successfully");
    }
  );

  /**
   * GET /api/messages/unread-count
   * Get unread message count for current user
   */
  getUnreadCount = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);

      this.logAction("get_unread_count", userId);

      const count = await this.messageService.getUnreadMessageCount(userId);

      this.sendSuccess(
        res,
        { count },
        "Unread message count retrieved successfully"
      );
    }
  );

  /**
   * POST /api/conversations/:conversationId/messages/bulk-read
   * Mark all messages in conversation as read
   */
  markConversationMessagesAsRead = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const conversationId = req.params.conversationId;
      const userId = this.extractUserId(req);
      const userRole = this.extractUserRole(req);

      this.logAction("mark_conversation_messages_read", userId, {
        conversationId,
      });

      // Verify user has access to this conversation (for non-admin/staff)
      if (userRole !== "admin" && userRole !== "staff") {
        const hasAccess = await this.messageService.verifyConversationAccess(
          conversationId,
          userId
        );

        if (!hasAccess) {
          return this.sendForbidden(res, "Access denied to this conversation");
        }
      }

      // Mark all unread messages in conversation as read
      const { error } = await this.messageService
        .getClient()
        .from("messages")
        .update({
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("conversation_id", conversationId)
        .is("read_at", null)
        .neq("sender_id", userId); // Don't update user's own messages

      if (error) {
        this.logError("mark_conversation_messages_read", error, userId);
        throw new Error("Failed to mark messages as read");
      }

      this.sendSuccess(
        res,
        { success: true },
        "All messages in conversation marked as read"
      );
    }
  );
}
