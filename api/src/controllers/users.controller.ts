import { Response } from "express";
import { BaseController } from "./base.controller";
import { UserService } from "@/services/user.service";
import { AuthenticatedRequest } from "@/types/api.types";
import {
  GetUsersFilters,
  UpdateUserData,
  CreateUserData,
} from "@/types/user.types";

export class UsersController extends BaseController {
  private userService: UserService;

  constructor() {
    super();
    this.userService = new UserService();
  }

  /**
   * GET /api/users
   * Get users with filters and pagination
   */
  getUsers = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);
      const pagination = this.extractPagination(req);

      const filters: GetUsersFilters = {
        ...pagination,
        search: req.query.search as string,
        role: req.query.role as any,
        created_after: req.query.created_after as string,
        created_before: req.query.created_before as string,
      };

      this.logAction("get_users", requesterId, { filters });

      const result = await this.userService.getUsers(
        requesterId,
        requesterRole,
        filters
      );

      this.sendPaginatedResponse(
        res,
        result.items,
        result.total,
        pagination,
        "Users retrieved successfully"
      );
    }
  );

  /**
   * GET /api/users/:id
   * Get user by ID
   */
  getUserById = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.params.id;
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);

      this.logAction("get_user_by_id", requesterId, { targetUserId: userId });

      const user = await this.userService.getUserById(
        userId,
        requesterId,
        requesterRole
      );

      this.sendSuccess(res, user, "User retrieved successfully");
    }
  );

  /**
   * PUT /api/users/:id
   * Update user profile
   */
  updateUser = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.params.id;
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);
      const data: UpdateUserData = req.body;

      this.logAction("update_user", requesterId, {
        targetUserId: userId,
        data,
      });

      const updatedUser = await this.userService.updateUser(
        userId,
        data,
        requesterId,
        requesterRole
      );

      this.sendSuccess(res, updatedUser, "User updated successfully");
    }
  );

  /**
   * POST /api/users
   * Create new user (admin only)
   */
  createUser = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);
      const data: CreateUserData = req.body;

      this.logAction("create_user", requesterId, {
        data: { ...data, password: "[REDACTED]" },
      });

      const newUser = await this.userService.createUser(
        data,
        requesterId,
        requesterRole
      );

      this.sendCreated(res, newUser, "User created successfully");
    }
  );

  /**
   * DELETE /api/users/:id
   * Delete user (admin only)
   */
  deleteUser = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.params.id;
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);

      this.logAction("delete_user", requesterId, { targetUserId: userId });

      await this.userService.deleteUser(userId, requesterId, requesterRole);

      this.sendSuccess(res, { success: true }, "User deleted successfully");
    }
  );

  /**
   * GET /api/users/stats
   * Get user statistics (admin only)
   */
  getUserStats = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const requesterId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);

      this.logAction("get_user_stats", requesterId);

      const stats = await this.userService.getUserStats(
        requesterId,
        requesterRole
      );

      this.sendSuccess(res, stats, "User statistics retrieved successfully");
    }
  );

  /**
   * GET /api/users/me
   * Get current user profile (alias for auth/me)
   */
  getCurrentUser = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);

      this.logAction("get_current_user", userId);

      const user = await this.userService.getUserById(
        userId,
        userId,
        requesterRole
      );

      this.sendSuccess(
        res,
        user,
        "Current user profile retrieved successfully"
      );
    }
  );

  /**
   * PUT /api/users/me
   * Update current user profile
   */
  updateCurrentUser = this.asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = this.extractUserId(req);
      const requesterRole = this.extractUserRole(req);
      const data: UpdateUserData = req.body;

      this.logAction("update_current_user", userId, { data });

      const updatedUser = await this.userService.updateUser(
        userId,
        data,
        userId,
        requesterRole
      );

      this.sendSuccess(res, updatedUser, "Profile updated successfully");
    }
  );
}
