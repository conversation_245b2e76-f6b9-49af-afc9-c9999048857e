import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, PaginationParams } from '@/types/api.types';
import { AuthUser } from '@/types/auth.types';
import { logger } from '@/utils/logger';
import {
  sendSuccess,
  sendError,
  sendPaginatedResponse,
  sendCreated,
  sendNotFound,
  sendUnauthorized,
  sendForbidden,
  sendValidationError,
  sendInternalError
} from '@/utils/response';

/**
 * Base controller class with common functionality
 */
export abstract class BaseController {
  /**
   * Async handler wrapper for controller methods
   */
  protected asyncHandler = (fn: Function) => {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  };

  /**
   * Send success response
   */
  protected sendSuccess<T>(
    res: Response,
    data: T,
    message: string = 'Success',
    statusCode: number = 200
  ): void {
    sendSuccess(res, data, message, statusCode);
  }

  /**
   * Send error response
   */
  protected sendError(
    res: Response,
    message: string,
    statusCode: number = 400,
    errors?: any
  ): void {
    sendError(res, message, statusCode, errors);
  }

  /**
   * Send paginated response
   */
  protected sendPaginatedResponse<T>(
    res: Response,
    data: T[],
    total: number,
    pagination: PaginationParams,
    message: string = 'Success'
  ): void {
    sendPaginatedResponse(res, data, total, pagination, message);
  }

  /**
   * Send created response
   */
  protected sendCreated<T>(
    res: Response,
    data: T,
    message: string = 'Created successfully'
  ): void {
    sendCreated(res, data, message);
  }

  /**
   * Send not found response
   */
  protected sendNotFound(
    res: Response,
    message: string = 'Resource not found'
  ): void {
    sendNotFound(res, message);
  }

  /**
   * Send unauthorized response
   */
  protected sendUnauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): void {
    sendUnauthorized(res, message);
  }

  /**
   * Send forbidden response
   */
  protected sendForbidden(
    res: Response,
    message: string = 'Forbidden'
  ): void {
    sendForbidden(res, message);
  }

  /**
   * Send validation error response
   */
  protected sendValidationError(
    res: Response,
    errors: any,
    message: string = 'Validation failed'
  ): void {
    sendValidationError(res, errors, message);
  }

  /**
   * Send internal server error response
   */
  protected sendInternalError(
    res: Response,
    message: string = 'Internal server error'
  ): void {
    sendInternalError(res, message);
  }

  /**
   * Extract pagination parameters from request
   */
  protected extractPagination(req: Request): PaginationParams {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    return { page, limit };
  }

  /**
   * Extract user ID from authenticated request
   */
  protected extractUserId(req: AuthenticatedRequest): string {
    if (!req.user?.id) {
      throw new Error('User ID not found in request');
    }
    return req.user.id;
  }

  /**
   * Extract user role from authenticated request
   */
  protected extractUserRole(req: AuthenticatedRequest): string {
    return req.user?.user_metadata?.role || 'customer';
  }

  /**
   * Extract user from authenticated request
   */
  protected extractUser(req: AuthenticatedRequest): AuthUser {
    if (!req.user) {
      throw new Error('User not found in request');
    }
    return req.user;
  }

  /**
   * Check if user is admin or staff
   */
  protected isAdminOrStaff(req: AuthenticatedRequest): boolean {
    const role = this.extractUserRole(req);
    return role === 'admin' || role === 'staff';
  }

  /**
   * Check if user is admin
   */
  protected isAdmin(req: AuthenticatedRequest): boolean {
    const role = this.extractUserRole(req);
    return role === 'admin';
  }

  /**
   * Log controller action
   */
  protected logAction(
    action: string,
    userId?: string,
    details?: any
  ): void {
    logger.info(`Controller action: ${action}`, {
      userId,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log controller error
   */
  protected logError(
    action: string,
    error: any,
    userId?: string
  ): void {
    logger.error(`Controller error in ${action}:`, {
      error: error.message || error,
      stack: error.stack,
      userId,
      timestamp: new Date().toISOString()
    });
  }
}
