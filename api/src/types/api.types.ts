import { Request } from 'express';
import { AuthUser } from './auth.types';

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  errors?: any;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  timestamp: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface AuthenticatedRequest extends Request {
  user?: AuthUser;
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: any;
  timestamp: string;
}

export interface HealthCheckResponse {
  status: 'ok' | 'error';
  timestamp: string;
  version: string;
  uptime: number;
  database: 'connected' | 'disconnected';
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}
