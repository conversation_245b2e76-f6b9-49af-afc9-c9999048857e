export interface UploadedFile {
  id: string;
  filename: string;
  original_name: string;
  mime_type: string;
  size: number;
  url: string;
  bucket: string;
  path: string;
  uploaded_by: string;
  file_data?: FileData;
  created_at: string;
  updated_at: string;
}

export interface FileData {
  alt_text?: string;
  description?: string;
  tags?: string[];
  metadata?: Record<string, unknown>;
  is_public?: boolean;
  expires_at?: string;
}

export interface FileUploadResponse {
  file: UploadedFile;
  url: string;
}

export interface FileUploadOptions {
  bucket?: string;
  folder?: string;
  filename?: string;
  is_public?: boolean;
  expires_in?: number; // seconds
  max_size?: number; // bytes
  allowed_types?: string[];
}

export interface GetFilesFilters {
  page?: number;
  limit?: number;
  bucket?: string;
  mime_type?: string;
  uploaded_by?: string;
  created_after?: string;
  created_before?: string;
  search?: string;
}

export interface GetFilesResponse {
  items: UploadedFile[];
  total: number;
}

export interface FileStats {
  total_files: number;
  total_size: number; // in bytes
  files_by_type: Record<string, number>;
  files_this_month: number;
  storage_used: number; // in bytes
}
