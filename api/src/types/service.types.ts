export interface Service {
  id: string;
  name: string;
  description: string;
  category_id: string;
  base_price: number;
  estimated_duration: number; // in minutes
  is_active: boolean;
  service_data?: ServiceData;
  created_at: string;
  updated_at: string;
}

export interface ServiceData {
  images?: string[];
  features?: string[];
  requirements?: string[];
  materials_included?: boolean;
  warranty_period?: number; // in days
  difficulty_level?: 'easy' | 'medium' | 'hard';
  tools_required?: string[];
  safety_notes?: string[];
}

export interface ServiceWithCategory extends Service {
  category: {
    id: string;
    name: string;
    description: string;
    icon?: string;
  };
}

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface CreateServiceData {
  name: string;
  description: string;
  category_id: string;
  base_price: number;
  estimated_duration: number;
  is_active?: boolean;
  service_data?: Partial<ServiceData>;
}

export interface UpdateServiceData {
  name?: string;
  description?: string;
  category_id?: string;
  base_price?: number;
  estimated_duration?: number;
  is_active?: boolean;
  service_data?: Partial<ServiceData>;
}

export interface CreateCategoryData {
  name: string;
  description: string;
  icon?: string;
  is_active?: boolean;
  sort_order?: number;
}

export interface UpdateCategoryData {
  name?: string;
  description?: string;
  icon?: string;
  is_active?: boolean;
  sort_order?: number;
}

export interface GetServicesFilters {
  page?: number;
  limit?: number;
  category_id?: string;
  is_active?: boolean;
  search?: string;
  min_price?: number;
  max_price?: number;
  max_duration?: number;
}

export interface GetServicesResponse {
  items: ServiceWithCategory[];
  total: number;
}

export interface GetCategoriesFilters {
  page?: number;
  limit?: number;
  is_active?: boolean;
  search?: string;
}

export interface GetCategoriesResponse {
  items: ServiceCategory[];
  total: number;
}

export interface ServiceStats {
  total_services: number;
  active_services: number;
  services_by_category: Record<string, number>;
  most_popular_services: {
    service_id: string;
    service_name: string;
    order_count: number;
  }[];
  average_service_price: number;
}
