export interface DashboardStats {
  users: {
    total: number;
    new_this_month: number;
    active_this_week: number;
    by_role: Record<string, number>;
  };
  orders: {
    total: number;
    pending: number;
    completed_this_month: number;
    total_revenue: number;
    by_status: Record<string, number>;
  };
  conversations: {
    total: number;
    unread: number;
    messages_today: number;
    by_role: Record<string, number>;
  };
  services: {
    total: number;
    active: number;
    most_popular: Array<{
      id: string;
      name: string;
      order_count: number;
    }>;
  };
  files: {
    total: number;
    total_size: number;
    uploaded_this_month: number;
    by_type: Record<string, number>;
  };
  notifications: {
    total_sent: number;
    sent_this_week: number;
    delivery_rate: number;
    by_type: Record<string, number>;
  };
}

export interface SystemHealth {
  status: "healthy" | "warning" | "critical";
  database: {
    status: "connected" | "disconnected";
    response_time: number;
    active_connections: number;
  };
  storage: {
    status: "available" | "unavailable";
    used_space: number;
    available_space: number;
  };
  websocket: {
    status: "running" | "stopped";
    connected_clients: number;
    uptime: number;
  };
  api: {
    status: "operational" | "degraded" | "down";
    response_time: number;
    error_rate: number;
  };
  last_checked: string;
}

export interface ActivityLog {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  details?: Record<string, unknown>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface GetActivityLogsFilters {
  page?: number;
  limit?: number;
  user_id?: string;
  action?: string;
  resource_type?: string;
  created_after?: string;
  created_before?: string;
}

export interface GetActivityLogsResponse {
  items: ActivityLog[];
  total: number;
}

export interface BackupInfo {
  id: string;
  type: "manual" | "scheduled";
  status: "pending" | "in_progress" | "completed" | "failed";
  size: number;
  tables_included: string[];
  created_by?: string;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface SystemConfig {
  id: string;
  key: string;
  value: unknown;
  description?: string;
  is_public: boolean;
  updated_by: string;
  updated_at: string;
}

export interface UpdateSystemConfigData {
  value: unknown;
  description?: string;
  is_public?: boolean;
}
