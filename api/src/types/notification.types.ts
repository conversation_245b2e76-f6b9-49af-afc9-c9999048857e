export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  is_read: boolean;
  action_url?: string;
  notification_data?: NotificationData;
  sent_at?: string;
  read_at?: string;
  created_at: string;
  updated_at: string;
}

export type NotificationType =
  | "order_created"
  | "order_updated"
  | "order_completed"
  | "order_cancelled"
  | "message_received"
  | "payment_received"
  | "payment_failed"
  | "system_announcement"
  | "reminder"
  | "promotion";

export type NotificationPriority = "low" | "medium" | "high" | "urgent";

export interface NotificationData {
  order_id?: string;
  conversation_id?: string;
  message_id?: string;
  payment_id?: string;
  image_url?: string;
  action_button?: {
    text: string;
    url: string;
  };
  metadata?: Record<string, unknown>;
}

export interface PushToken {
  id: string;
  user_id: string;
  token: string;
  platform: "ios" | "android" | "web";
  device_info?: DeviceInfo;
  is_active: boolean;
  last_used_at: string;
  created_at: string;
  updated_at: string;
}

export interface DeviceInfo {
  device_id?: string;
  device_name?: string;
  os_version?: string;
  app_version?: string;
  user_agent?: string;
}

export interface CreateNotificationData {
  user_id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  action_url?: string;
  notification_data?: Partial<NotificationData>;
  send_push?: boolean;
}

export interface BulkNotificationData {
  user_ids: string[];
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  action_url?: string;
  notification_data?: Partial<NotificationData>;
  send_push?: boolean;
}

export interface UpdateNotificationData {
  is_read?: boolean;
  read_at?: string;
}

export interface GetNotificationsFilters {
  page?: number;
  limit?: number;
  type?: NotificationType;
  priority?: NotificationPriority;
  is_read?: boolean;
  created_after?: string;
  created_before?: string;
}

export interface GetNotificationsResponse {
  items: Notification[];
  total: number;
  unread_count: number;
}

export interface NotificationStats {
  total_notifications: number;
  unread_notifications: number;
  notifications_by_type: Record<NotificationType, number>;
  notifications_by_priority: Record<NotificationPriority, number>;
  notifications_this_week: number;
  push_delivery_rate: number;
}

export interface PushNotificationPayload {
  title: string;
  body: string;
  data?: Record<string, unknown>;
  badge?: number;
  sound?: string;
  click_action?: string;
  icon?: string;
  image?: string;
}
