import { UserProfile } from "./user.types";

// Basic conversation type
export interface Conversation {
  id: string;
  user_id: string;
  user_role: "customer" | "technician";
  last_message?: string;
  last_message_at: string;
  read_at?: string;
  conversation_data?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Conversation with user details
export interface ConversationWithUser extends Conversation {
  user: UserProfile;
}

// Message type
export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  is_system: boolean;
  read_at?: string;
  message_data?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Message with sender details
export interface MessageWithSender extends Message {
  sender: UserProfile;
}

// Filters for fetching conversations
export interface GetConversationsFilters {
  page?: number;
  limit?: number;
  search?: string;
  user_role?: "customer" | "technician";
  unread_only?: boolean;
  created_after?: string;
  created_before?: string;
}

// Conversation list response
export interface GetConversationsResponse {
  items: ConversationWithUser[];
  total: number;
}

// Filters for fetching messages
export interface GetMessagesFilters {
  conversation_id: string;
  page?: number;
  limit?: number;
  created_at_lt?: string; // For loading earlier messages
  created_at_gt?: string; // For loading newer messages
}

// Message list response
export interface GetMessagesResponse {
  items: MessageWithSender[];
  total: number;
}

// Create conversation data
export interface CreateConversationData {
  user_id: string;
  user_role: "customer" | "technician";
  initial_message?: string;
  conversation_data?: Record<string, any>;
}

// Create message data
export interface CreateMessageData {
  conversation_id: string;
  content: string;
  is_system?: boolean;
  message_data?: Record<string, any>;
  sender_id?: string;
}

// Mark as read data
export interface MarkAsReadData {
  conversation_id: string;
  user_id?: string;
}

// Optimistic message type for client-side rendering
export interface OptimisticMessage
  extends Omit<Message, "id" | "created_at" | "updated_at"> {
  id?: string;
  temp_id?: string;
  isPending?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Conversation statistics
export interface ConversationStats {
  total_conversations: number;
  unread_conversations: number;
  conversations_by_role: Record<"customer" | "technician", number>;
  messages_today: number;
  average_response_time: number; // in minutes
}

// Available users for creating conversations
export interface AvailableUser {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: "customer" | "technician";
}
