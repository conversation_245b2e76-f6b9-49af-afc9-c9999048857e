export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: UserR<PERSON>;
  profile_data?: UserProfileData;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'staff' | 'customer' | 'technician';

export interface UserProfileData {
  avatar_url?: string;
  address?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  emergency_contact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences?: {
    notifications: boolean;
    language: string;
    theme: 'light' | 'dark';
  };
}

export interface UserProfile extends User {
  // Extended user profile with additional computed fields
  full_name?: string;
  display_name?: string;
  avatar_url?: string;
}

export interface GetUsersFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  created_after?: string;
  created_before?: string;
}

export interface GetUsersResponse {
  items: User[];
  total: number;
}

export interface UpdateUserData {
  name?: string;
  phone?: string;
  profile_data?: Partial<UserProfileData>;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role: UserRole;
  profile_data?: Partial<UserProfileData>;
}

export interface UserStats {
  total_users: number;
  users_by_role: Record<UserRole, number>;
  new_users_this_month: number;
  active_users_this_week: number;
}
