export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: UserR<PERSON>;
  profile_data?: UserProfileData;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'staff' | 'customer' | 'technician';

export interface UserProfileData {
  avatar_url?: string;
  address?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  emergency_contact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences?: {
    notifications: boolean;
    language: string;
    theme: 'light' | 'dark';
  };
}

export interface AuthUser {
  id: string;
  email: string;
  user_metadata: {
    role: UserRole;
    name?: string;
  };
  app_metadata: Record<string, any>;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
  sub: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role?: UserRole;
  profile_data?: Partial<UserProfileData>;
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface RefreshTokenData {
  refresh_token: string;
}

export interface JWTPayload {
  sub: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}
