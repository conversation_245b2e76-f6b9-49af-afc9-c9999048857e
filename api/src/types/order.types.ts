export interface Order {
  id: string;
  customer_id: string;
  service_id: string;
  technician_id?: string;
  status: OrderStatus;
  priority: OrderPriority;
  scheduled_date?: string;
  completed_date?: string;
  total_amount: number;
  payment_status: PaymentStatus;
  order_data?: OrderData;
  created_at: string;
  updated_at: string;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'refunded';

export type OrderPriority = 'low' | 'medium' | 'high' | 'urgent';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partial';

export interface OrderData {
  address?: string;
  contact_phone?: string;
  notes?: string;
  images?: string[];
  estimated_duration?: number; // in minutes
  actual_duration?: number; // in minutes
  materials_cost?: number;
  labor_cost?: number;
  additional_charges?: {
    name: string;
    amount: number;
    description?: string;
  }[];
}

export interface OrderWithDetails extends Order {
  customer: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  service: {
    id: string;
    name: string;
    category: string;
    base_price: number;
  };
  technician?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
}

export interface CreateOrderData {
  service_id: string;
  customer_id?: string; // Optional if creating for current user
  scheduled_date?: string;
  priority?: OrderPriority;
  order_data?: Partial<OrderData>;
}

export interface UpdateOrderData {
  status?: OrderStatus;
  technician_id?: string;
  scheduled_date?: string;
  priority?: OrderPriority;
  payment_status?: PaymentStatus;
  order_data?: Partial<OrderData>;
}

export interface GetOrdersFilters {
  page?: number;
  limit?: number;
  status?: OrderStatus;
  priority?: OrderPriority;
  payment_status?: PaymentStatus;
  customer_id?: string;
  technician_id?: string;
  service_id?: string;
  created_after?: string;
  created_before?: string;
  scheduled_after?: string;
  scheduled_before?: string;
}

export interface GetOrdersResponse {
  items: OrderWithDetails[];
  total: number;
}

export interface OrderStats {
  total_orders: number;
  orders_by_status: Record<OrderStatus, number>;
  orders_by_priority: Record<OrderPriority, number>;
  total_revenue: number;
  pending_revenue: number;
  orders_this_month: number;
  completed_orders_this_month: number;
}
