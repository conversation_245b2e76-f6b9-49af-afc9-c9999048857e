import { SupabaseService } from "./supabase.service";
import { RealtimeService } from "./realtime.service";
import {
  DashboardStats,
  SystemHealth,
  ActivityLog,
  GetActivityLogsFilters,
  GetActivityLogsResponse,
} from "@/types/admin.types";
import { logger } from "@/utils/logger";
import { ApiError } from "@/middleware/error.middleware";

export class AdminService extends SupabaseService {
  private realtimeService: RealtimeService;

  constructor() {
    super(); // Use service role client
    this.realtimeService = RealtimeService.getInstance();
  }

  /**
   * Get dashboard statistics
   */
  async getDashboardStats(
    userId: string,
    userRole: string
  ): Promise<DashboardStats> {
    try {
      // Only admin can view dashboard stats
      if (userRole !== "admin") {
        throw new ApiError("Only admin can view dashboard statistics", 403);
      }

      // Get user stats
      const { count: totalUsers } = await this.getClient()
        .from("users")
        .select("*", { count: "exact", head: true });

      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const { count: newUsersThisMonth } = await this.getClient()
        .from("users")
        .select("*", { count: "exact", head: true })
        .gte("created_at", thisMonth.toISOString());

      const { data: usersData } = await this.getClient()
        .from("users")
        .select("role");

      const usersByRole =
        usersData?.reduce((acc, user) => {
          acc[user.role] = (acc[user.role] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      // Get order stats
      const { count: totalOrders } = await this.getClient()
        .from("orders")
        .select("*", { count: "exact", head: true });

      const { count: pendingOrders } = await this.getClient()
        .from("orders")
        .select("*", { count: "exact", head: true })
        .eq("status", "pending");

      const { count: completedOrdersThisMonth } = await this.getClient()
        .from("orders")
        .select("*", { count: "exact", head: true })
        .eq("status", "completed")
        .gte("created_at", thisMonth.toISOString());

      const { data: ordersData } = await this.getClient()
        .from("orders")
        .select("status, total_amount");

      const ordersByStatus =
        ordersData?.reduce((acc, order) => {
          acc[order.status] = (acc[order.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      const totalRevenue =
        ordersData?.reduce((sum, order) => sum + order.total_amount, 0) || 0;

      // Get conversation stats
      const { count: totalConversations } = await this.getClient()
        .from("conversations")
        .select("*", { count: "exact", head: true });

      const { count: unreadConversations } = await this.getClient()
        .from("conversations")
        .select("*", { count: "exact", head: true })
        .is("read_at", null);

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { count: messagesToday } = await this.getClient()
        .from("messages")
        .select("*", { count: "exact", head: true })
        .gte("created_at", today.toISOString());

      const { data: conversationsData } = await this.getClient()
        .from("conversations")
        .select("user_role");

      const conversationsByRole =
        conversationsData?.reduce((acc, conv) => {
          acc[conv.user_role] = (acc[conv.user_role] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      // Get service stats
      const { count: totalServices } = await this.getClient()
        .from("services")
        .select("*", { count: "exact", head: true });

      const { count: activeServices } = await this.getClient()
        .from("services")
        .select("*", { count: "exact", head: true })
        .eq("is_active", true);

      // Get file stats
      const { count: totalFiles } = await this.getClient()
        .from("files")
        .select("*", { count: "exact", head: true });

      const { data: filesData } = await this.getClient()
        .from("files")
        .select("size, mime_type, created_at");

      const totalFileSize =
        filesData?.reduce((sum, file) => sum + file.size, 0) || 0;

      const filesThisMonth =
        filesData?.filter((file) => new Date(file.created_at) >= thisMonth)
          .length || 0;

      const filesByType =
        filesData?.reduce((acc, file) => {
          const type = file.mime_type.split("/")[0];
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

      logger.info("Dashboard statistics retrieved successfully:", { userId });

      return {
        users: {
          total: totalUsers || 0,
          new_this_month: newUsersThisMonth || 0,
          active_this_week: 0, // TODO: Implement active users calculation
          by_role: usersByRole,
        },
        orders: {
          total: totalOrders || 0,
          pending: pendingOrders || 0,
          completed_this_month: completedOrdersThisMonth || 0,
          total_revenue: totalRevenue,
          by_status: ordersByStatus,
        },
        conversations: {
          total: totalConversations || 0,
          unread: unreadConversations || 0,
          messages_today: messagesToday || 0,
          by_role: conversationsByRole,
        },
        services: {
          total: totalServices || 0,
          active: activeServices || 0,
          most_popular: [], // TODO: Implement most popular services
        },
        files: {
          total: totalFiles || 0,
          total_size: totalFileSize,
          uploaded_this_month: filesThisMonth,
          by_type: filesByType,
        },
        notifications: {
          total_sent: 0, // TODO: Implement notification stats
          sent_this_week: 0,
          delivery_rate: 0,
          by_type: {},
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get dashboard stats service error:", error);
      throw new ApiError("Failed to get dashboard statistics", 500);
    }
  }

  /**
   * Get system health status
   */
  async getSystemHealth(
    userId: string,
    userRole: string
  ): Promise<SystemHealth> {
    try {
      // Only admin can view system health
      if (userRole !== "admin") {
        throw new ApiError("Only admin can view system health", 403);
      }

      const startTime = Date.now();

      // Test database connection
      let dbStatus: "connected" | "disconnected" = "disconnected";
      let dbResponseTime = 0;

      try {
        const dbStart = Date.now();
        await this.getClient().from("users").select("count").limit(1);
        dbResponseTime = Date.now() - dbStart;
        dbStatus = "connected";
      } catch (error) {
        logger.error("Database health check failed:", error);
      }

      // Get WebSocket status
      const wsConnectedClients = this.realtimeService.getConnectedUsersCount();
      const wsStatus = "running"; // Assume running if we can get client count

      // Calculate API response time
      const apiResponseTime = Date.now() - startTime;

      const health: SystemHealth = {
        status: dbStatus === "connected" ? "healthy" : "critical",
        database: {
          status: dbStatus,
          response_time: dbResponseTime,
          active_connections: 0, // TODO: Get actual connection count
        },
        storage: {
          status: "available", // TODO: Check storage status
          used_space: 0,
          available_space: 0,
        },
        websocket: {
          status: wsStatus,
          connected_clients: wsConnectedClients,
          uptime: process.uptime(),
        },
        api: {
          status: "operational",
          response_time: apiResponseTime,
          error_rate: 0, // TODO: Calculate error rate
        },
        last_checked: new Date().toISOString(),
      };

      logger.info("System health checked:", { userId, status: health.status });

      return health;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get system health service error:", error);
      throw new ApiError("Failed to get system health", 500);
    }
  }

  /**
   * Log admin activity
   */
  async logActivity(
    userId: string,
    action: string,
    resourceType: string,
    resourceId?: string,
    details?: Record<string, unknown>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      const activityLog = {
        user_id: userId,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        details,
        ip_address: ipAddress,
        user_agent: userAgent,
        created_at: new Date().toISOString(),
      };

      const { error } = await this.getClient()
        .from("activity_logs")
        .insert(activityLog);

      if (error) {
        logger.error("Failed to log activity:", error);
        // Don't throw error as this is logging functionality
      }
    } catch (error) {
      logger.error("Log activity service error:", error);
      // Don't throw error as this is logging functionality
    }
  }

  /**
   * Get activity logs
   */
  async getActivityLogs(
    userId: string,
    userRole: string,
    filters: GetActivityLogsFilters = {}
  ): Promise<GetActivityLogsResponse> {
    try {
      // Only admin can view activity logs
      if (userRole !== "admin") {
        throw new ApiError("Only admin can view activity logs", 403);
      }

      let query = this.getClient()
        .from("activity_logs")
        .select(
          `
          *,
          user:users(id, name, email, role)
        `,
          { count: "exact" }
        );

      // Apply filters
      if (filters.user_id) {
        query = query.eq("user_id", filters.user_id);
      }

      if (filters.action) {
        query = query.ilike("action", `%${filters.action}%`);
      }

      if (filters.resource_type) {
        query = query.eq("resource_type", filters.resource_type);
      }

      if (filters.created_after) {
        query = query.gte("created_at", filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte("created_at", filters.created_before);
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 50;
      const start = (page - 1) * limit;

      query = query
        .order("created_at", { ascending: false })
        .range(start, start + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error("Failed to get activity logs:", error);
        throw new ApiError("Failed to get activity logs", 500);
      }

      logger.info("Activity logs retrieved successfully:", {
        userId,
        count: data?.length || 0,
        total: count || 0,
      });

      return {
        items: data || [],
        total: count || 0,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get activity logs service error:", error);
      throw new ApiError("Failed to get activity logs", 500);
    }
  }
}
