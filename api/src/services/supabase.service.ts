import { SupabaseClient } from "@supabase/supabase-js";
import { supabase, createUserScopedClient } from "@/config/database";
import { logger } from "@/utils/logger";

/**
 * Supabase service for database operations
 */
export class SupabaseService {
  private client: SupabaseClient;

  constructor(userToken?: string) {
    this.client = userToken ? createUserScopedClient(userToken) : supabase;
  }

  /**
   * Get the Supabase client instance
   */
  getClient(): SupabaseClient {
    return this.client;
  }

  /**
   * Execute a query with error handling and logging
   */
  async executeQuery<T>(
    queryFn: (client: SupabaseClient) => Promise<any>,
    operation: string
  ): Promise<T> {
    try {
      logger.debug(`Executing ${operation}`);

      const result = await queryFn(this.client);

      if (result.error) {
        logger.error(`${operation} failed:`, result.error);
        throw new Error(result.error.message);
      }

      logger.debug(`${operation} completed successfully`);
      return result.data;
    } catch (error) {
      logger.error(`${operation} error:`, error);
      throw error;
    }
  }

  /**
   * Execute a query with pagination
   */
  async executePaginatedQuery<T>(
    queryFn: (client: SupabaseClient) => any,
    operation: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ items: T[]; total: number }> {
    try {
      logger.debug(
        `Executing paginated ${operation} - page: ${page}, limit: ${limit}`
      );

      const start = (page - 1) * limit;
      const end = start + limit - 1;

      const query = queryFn(this.client);
      const { data, error, count } = await query.range(start, end);

      if (error) {
        logger.error(`${operation} failed:`, error);
        throw new Error(error.message);
      }

      logger.debug(
        `${operation} completed - returned ${data?.length || 0} items`
      );

      return {
        items: data || [],
        total: count || 0,
      };
    } catch (error) {
      logger.error(`${operation} error:`, error);
      throw error;
    }
  }

  /**
   * Validate user access to a resource
   */
  async validateUserAccess(
    userId: string,
    resourceType: string,
    resourceId: string,
    userRole?: string
  ): Promise<boolean> {
    try {
      // Admin and staff have access to everything
      if (userRole === "admin" || userRole === "staff") {
        return true;
      }

      // Check resource-specific access
      switch (resourceType) {
        case "conversation": {
          const { data: conversation } = await this.client
            .from("conversations")
            .select("user_id")
            .eq("id", resourceId)
            .single();

          return conversation?.user_id === userId;
        }

        case "message": {
          const { data: message } = await this.client
            .from("messages")
            .select("conversation_id, conversations!inner(user_id)")
            .eq("id", resourceId)
            .single();

          return (message as any)?.conversations?.user_id === userId;
        }

        case "order": {
          const { data: order } = await this.client
            .from("orders")
            .select("customer_id")
            .eq("id", resourceId)
            .single();

          return order?.customer_id === userId;
        }

        default:
          return false;
      }
    } catch (error) {
      logger.error(
        `Access validation error for ${resourceType}:${resourceId}:`,
        error
      );
      return false;
    }
  }
}
