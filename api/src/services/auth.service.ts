import { SupabaseService } from "./supabase.service";
import {
  LoginData,
  RegisterData,
  AuthResponse,
  User,
} from "@/types/auth.types";
import { logger } from "@/utils/logger";
import { ApiError } from "@/middleware/error.middleware";

export class AuthService extends SupabaseService {
  constructor() {
    super(); // Use service role client
  }

  /**
   * User login
   */
  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const { email, password } = data;

      // Use Supabase auth for login
      const { data: authData, error: authError } =
        await this.getClient().auth.signInWithPassword({
          email,
          password,
        });

      if (authError) {
        logger.warn("Login failed:", { email, error: authError.message });
        throw new ApiError("Invalid credentials", 401);
      }

      if (!authData.user || !authData.session) {
        throw new ApiError("Login failed", 401);
      }

      // Get user profile from database
      const { data: userProfile, error: profileError } = await this.getClient()
        .from("users")
        .select("*")
        .eq("id", authData.user.id)
        .single();

      if (profileError || !userProfile) {
        logger.error("User profile not found:", {
          userId: authData.user.id,
          error: profileError,
        });
        throw new ApiError("User profile not found", 404);
      }

      logger.info("User logged in successfully:", {
        userId: authData.user.id,
        email,
      });

      return {
        user: userProfile as User,
        access_token: authData.session.access_token,
        refresh_token: authData.session.refresh_token,
        expires_in: authData.session.expires_in || 3600,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Login service error:", error);
      throw new ApiError("Login failed", 500);
    }
  }

  /**
   * User registration
   */
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const {
        name,
        email,
        password,
        phone,
        role = "customer",
        profile_data,
      } = data;

      // Create user in Supabase Auth
      const { data: authData, error: authError } =
        await this.getClient().auth.signUp({
          email,
          password,
          options: {
            data: {
              name,
              role,
            },
          },
        });

      if (authError) {
        logger.warn("Registration failed:", {
          email,
          error: authError.message,
        });

        if (authError.message.includes("already registered")) {
          throw new ApiError("Email already registered", 409);
        }

        throw new ApiError(authError.message, 400);
      }

      if (!authData.user) {
        throw new ApiError("Registration failed", 400);
      }

      // Create user profile in database
      const userProfileData = {
        id: authData.user.id,
        email,
        name,
        phone,
        role,
        profile_data: profile_data || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: userProfile, error: profileError } = await this.getClient()
        .from("users")
        .insert(userProfileData)
        .select()
        .single();

      if (profileError) {
        logger.error("Failed to create user profile:", {
          userId: authData.user.id,
          error: profileError,
        });

        // Clean up auth user if profile creation fails
        await this.getClient().auth.admin.deleteUser(authData.user.id);

        throw new ApiError("Failed to create user profile", 500);
      }

      logger.info("User registered successfully:", {
        userId: authData.user.id,
        email,
        role,
      });

      // If session exists (email confirmation disabled), return tokens
      if (authData.session) {
        return {
          user: userProfile as User,
          access_token: authData.session.access_token,
          refresh_token: authData.session.refresh_token,
          expires_in: authData.session.expires_in || 3600,
        };
      }

      // If email confirmation required, return user without tokens
      return {
        user: userProfile as User,
        access_token: "",
        refresh_token: "",
        expires_in: 0,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Registration service error:", error);
      throw new ApiError("Registration failed", 500);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const { data: authData, error: authError } =
        await this.getClient().auth.refreshSession({
          refresh_token: refreshToken,
        });

      if (authError || !authData.session) {
        logger.warn("Token refresh failed:", { error: authError?.message });
        throw new ApiError("Invalid refresh token", 401);
      }

      // Get user profile
      const { data: userProfile, error: profileError } = await this.getClient()
        .from("users")
        .select("*")
        .eq("id", authData.user!.id)
        .single();

      if (profileError || !userProfile) {
        logger.error("User profile not found during refresh:", {
          userId: authData.user!.id,
        });
        throw new ApiError("User profile not found", 404);
      }

      logger.info("Token refreshed successfully:", {
        userId: authData.user!.id,
      });

      return {
        user: userProfile as User,
        access_token: authData.session.access_token,
        refresh_token: authData.session.refresh_token,
        expires_in: authData.session.expires_in || 3600,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Token refresh service error:", error);
      throw new ApiError("Token refresh failed", 500);
    }
  }

  /**
   * User logout
   */
  async logout(token?: string): Promise<void> {
    try {
      if (token) {
        // Create user-scoped client for logout
        const userClient = this.getClient();
        await userClient.auth.signOut();
      }

      logger.info("User logged out successfully");
    } catch (error) {
      logger.error("Logout service error:", error);
      // Don't throw error for logout - it should always succeed
    }
  }

  /**
   * Get user profile
   */
  async getUserProfile(userId: string): Promise<User> {
    try {
      const { data: userProfile, error } = await this.getClient()
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();

      if (error || !userProfile) {
        logger.error("User profile not found:", { userId, error });
        throw new ApiError("User profile not found", 404);
      }

      return userProfile as User;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get user profile service error:", error);
      throw new ApiError("Failed to get user profile", 500);
    }
  }

  /**
   * Change user password
   */
  async changePassword(
    userId: string,
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      // First verify the old password by trying to get the user's email
      const { data: userProfile, error: profileError } = await this.getClient()
        .from("users")
        .select("email")
        .eq("id", userId)
        .single();

      if (profileError || !userProfile) {
        logger.error("User not found for password change:", { userId });
        throw new ApiError("User not found", 404);
      }

      // Verify old password by attempting to sign in
      const { error: signInError } = await this.getClient().auth.signInWithPassword({
        email: userProfile.email,
        password: oldPassword,
      });

      if (signInError) {
        logger.warn("Old password verification failed:", { userId });
        throw new ApiError("Current password is incorrect", 401);
      }

      // Update the password using admin API
      const { error: updateError } = await this.getClient().auth.admin.updateUserById(
        userId,
        { password: newPassword }
      );

      if (updateError) {
        logger.error("Password update failed:", { userId, error: updateError });
        throw new ApiError("Failed to update password", 500);
      }

      logger.info("Password changed successfully:", { userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Change password service error:", error);
      throw new ApiError("Failed to change password", 500);
    }
  }
}
