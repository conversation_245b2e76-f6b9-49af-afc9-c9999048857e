import { SupabaseService } from "./supabase.service";
import {
  UploadedFile,
  FileUploadOptions,
  GetFilesFilters,
  GetFilesResponse,
  FileStats,
} from "@/types/file.types";
import { logger } from "@/utils/logger";
import { ApiError } from "@/middleware/error.middleware";
import { v4 as uuidv4 } from "uuid";
import path from "path";

export class FileService extends SupabaseService {
  constructor() {
    super(); // Use service role client
  }

  /**
   * Upload file to Supabase Storage
   */
  async uploadFile(
    file: Express.Multer.File,
    userId: string,
    options: FileUploadOptions = {}
  ): Promise<UploadedFile> {
    try {
      const {
        bucket = "uploads",
        folder = "general",
        filename,
        is_public = false,
        expires_in,
      } = options;

      // Generate unique filename
      const fileExtension = path.extname(file.originalname);
      const uniqueFilename = filename || `${uuidv4()}${fileExtension}`;
      const filePath = `${folder}/${uniqueFilename}`;

      // Upload to Supabase Storage
      const { error: uploadError } = await this.getClient()
        .storage.from(bucket)
        .upload(filePath, file.buffer, {
          contentType: file.mimetype,
          upsert: false,
        });

      if (uploadError) {
        logger.error("File upload failed:", uploadError);
        throw new ApiError("File upload failed", 500);
      }

      // Get public URL
      const { data: urlData } = this.getClient()
        .storage.from(bucket)
        .getPublicUrl(filePath);

      // Save file record to database
      const fileRecord = {
        id: uuidv4(),
        filename: uniqueFilename,
        original_name: file.originalname,
        mime_type: file.mimetype,
        size: file.size,
        url: urlData.publicUrl,
        bucket,
        path: filePath,
        uploaded_by: userId,
        file_data: {
          is_public,
          expires_at: expires_in
            ? new Date(Date.now() + expires_in * 1000).toISOString()
            : null,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: savedFile, error: saveError } = await this.getClient()
        .from("files")
        .insert(fileRecord)
        .select()
        .single();

      if (saveError) {
        logger.error("Failed to save file record:", saveError);

        // Clean up uploaded file
        await this.getClient().storage.from(bucket).remove([filePath]);

        throw new ApiError("Failed to save file record", 500);
      }

      logger.info("File uploaded successfully:", {
        fileId: savedFile.id,
        filename: uniqueFilename,
        size: file.size,
        userId,
      });

      return savedFile as UploadedFile;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("File upload service error:", error);
      throw new ApiError("File upload failed", 500);
    }
  }

  /**
   * Get files with filters
   */
  async getFiles(
    userId: string,
    userRole: string,
    filters: GetFilesFilters = {}
  ): Promise<GetFilesResponse> {
    try {
      let query = this.getClient()
        .from("files")
        .select("*", { count: "exact" });

      // Apply role-based filtering
      if (userRole !== "admin" && userRole !== "staff") {
        query = query.eq("uploaded_by", userId);
      }

      // Apply filters
      if (filters.bucket) {
        query = query.eq("bucket", filters.bucket);
      }

      if (filters.mime_type) {
        query = query.ilike("mime_type", `${filters.mime_type}%`);
      }

      if (
        filters.uploaded_by &&
        (userRole === "admin" || userRole === "staff")
      ) {
        query = query.eq("uploaded_by", filters.uploaded_by);
      }

      if (filters.search) {
        query = query.or(
          `filename.ilike.%${filters.search}%,original_name.ilike.%${filters.search}%`
        );
      }

      if (filters.created_after) {
        query = query.gte("created_at", filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte("created_at", filters.created_before);
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const start = (page - 1) * limit;

      query = query
        .order("created_at", { ascending: false })
        .range(start, start + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error("Failed to get files:", error);
        throw new ApiError("Failed to get files", 500);
      }

      logger.info("Files retrieved successfully:", {
        userId,
        count: data?.length || 0,
        total: count || 0,
      });

      return {
        items: data || [],
        total: count || 0,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get files service error:", error);
      throw new ApiError("Failed to get files", 500);
    }
  }

  /**
   * Get file by ID
   */
  async getFileById(
    fileId: string,
    userId: string,
    userRole: string
  ): Promise<UploadedFile | null> {
    try {
      let query = this.getClient().from("files").select("*").eq("id", fileId);

      // Apply access control
      if (userRole !== "admin" && userRole !== "staff") {
        query = query.eq("uploaded_by", userId);
      }

      const { data: file, error } = await query.single();

      if (error) {
        if (error.code === "PGRST116") {
          return null;
        }
        logger.error("Failed to get file:", error);
        throw new ApiError("Failed to get file", 500);
      }

      return file as UploadedFile;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get file by ID service error:", error);
      throw new ApiError("Failed to get file", 500);
    }
  }

  /**
   * Delete file
   */
  async deleteFile(
    fileId: string,
    userId: string,
    userRole: string
  ): Promise<void> {
    try {
      // Get file record
      const file = await this.getFileById(fileId, userId, userRole);

      if (!file) {
        throw new ApiError("File not found", 404);
      }

      // Check permissions
      if (
        userRole !== "admin" &&
        userRole !== "staff" &&
        file.uploaded_by !== userId
      ) {
        throw new ApiError("Insufficient permissions to delete this file", 403);
      }

      // Delete from storage
      const { error: storageError } = await this.getClient()
        .storage.from(file.bucket)
        .remove([file.path]);

      if (storageError) {
        logger.warn("Failed to delete file from storage:", storageError);
        // Continue with database deletion even if storage deletion fails
      }

      // Delete from database
      const { error: dbError } = await this.getClient()
        .from("files")
        .delete()
        .eq("id", fileId);

      if (dbError) {
        logger.error("Failed to delete file record:", dbError);
        throw new ApiError("Failed to delete file record", 500);
      }

      logger.info("File deleted successfully:", {
        fileId,
        filename: file.filename,
        deletedBy: userId,
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Delete file service error:", error);
      throw new ApiError("Failed to delete file", 500);
    }
  }

  /**
   * Get file statistics
   */
  async getFileStats(userId: string, userRole: string): Promise<FileStats> {
    try {
      // Only admin can view full statistics
      if (userRole !== "admin") {
        throw new ApiError("Only admin can view file statistics", 403);
      }

      // Get total files count
      const { count: totalFiles, error: totalError } = await this.getClient()
        .from("files")
        .select("*", { count: "exact", head: true });

      if (totalError) {
        throw new ApiError("Failed to get file statistics", 500);
      }

      // Get total size and files by type
      const { data: filesData, error: filesError } = await this.getClient()
        .from("files")
        .select("size, mime_type");

      if (filesError) {
        throw new ApiError("Failed to get file data", 500);
      }

      const totalSize = filesData.reduce((sum, file) => sum + file.size, 0);
      const filesByType = filesData.reduce((acc, file) => {
        const type = file.mime_type.split("/")[0];
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Get files this month
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const { count: filesThisMonth, error: monthError } =
        await this.getClient()
          .from("files")
          .select("*", { count: "exact", head: true })
          .gte("created_at", thisMonth.toISOString());

      if (monthError) {
        throw new ApiError("Failed to get monthly file statistics", 500);
      }

      logger.info("File statistics retrieved successfully:", { userId });

      return {
        total_files: totalFiles || 0,
        total_size: totalSize,
        files_by_type: filesByType,
        files_this_month: filesThisMonth || 0,
        storage_used: totalSize,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error("Get file stats service error:", error);
      throw new ApiError("Failed to get file statistics", 500);
    }
  }
}
