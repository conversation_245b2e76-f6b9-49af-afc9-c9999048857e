import { SupabaseService } from './supabase.service';
import { 
  User, 
  UserRole, 
  GetUsersFilters, 
  GetUsersResponse, 
  UpdateUserData,
  CreateUserData,
  UserStats
} from '@/types/user.types';
import { logger } from '@/utils/logger';
import { ApiError } from '@/middleware/error.middleware';

export class UserService extends SupabaseService {
  constructor() {
    super(); // Use service role client
  }

  /**
   * Get users with filters and pagination
   */
  async getUsers(
    requesterId: string,
    requesterRole: string,
    filters: GetUsersFilters = {}
  ): Promise<GetUsersResponse> {
    try {
      // Only admin and staff can view all users
      if (requesterRole !== 'admin' && requesterRole !== 'staff') {
        throw new ApiError('Insufficient permissions to view users', 403);
      }

      let query = this.getClient()
        .from('users')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`
        );
      }

      if (filters.role) {
        query = query.eq('role', filters.role);
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const start = (page - 1) * limit;

      query = query
        .order('created_at', { ascending: false })
        .range(start, start + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error('Failed to get users:', error);
        throw new ApiError('Failed to get users', 500);
      }

      logger.info('Users retrieved successfully:', { 
        requesterId, 
        count: data?.length || 0,
        total: count || 0
      });

      return {
        items: data || [],
        total: count || 0
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get users service error:', error);
      throw new ApiError('Failed to get users', 500);
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(
    userId: string,
    requesterId: string,
    requesterRole: string
  ): Promise<User> {
    try {
      // Users can view their own profile, admin/staff can view any profile
      if (userId !== requesterId && requesterRole !== 'admin' && requesterRole !== 'staff') {
        throw new ApiError('Insufficient permissions to view this user', 403);
      }

      const { data: user, error } = await this.getClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !user) {
        logger.error('User not found:', { userId, error });
        throw new ApiError('User not found', 404);
      }

      logger.info('User retrieved successfully:', { userId, requesterId });

      return user as User;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get user by ID service error:', error);
      throw new ApiError('Failed to get user', 500);
    }
  }

  /**
   * Update user profile
   */
  async updateUser(
    userId: string,
    data: UpdateUserData,
    requesterId: string,
    requesterRole: string
  ): Promise<User> {
    try {
      // Users can update their own profile, admin/staff can update any profile
      if (userId !== requesterId && requesterRole !== 'admin' && requesterRole !== 'staff') {
        throw new ApiError('Insufficient permissions to update this user', 403);
      }

      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };

      const { data: updatedUser, error } = await this.getClient()
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update user:', { userId, error });
        throw new ApiError('Failed to update user', 500);
      }

      if (!updatedUser) {
        throw new ApiError('User not found', 404);
      }

      logger.info('User updated successfully:', { userId, requesterId });

      return updatedUser as User;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Update user service error:', error);
      throw new ApiError('Failed to update user', 500);
    }
  }

  /**
   * Create new user (admin only)
   */
  async createUser(
    data: CreateUserData,
    requesterId: string,
    requesterRole: string
  ): Promise<User> {
    try {
      // Only admin can create users
      if (requesterRole !== 'admin') {
        throw new ApiError('Only admin can create users', 403);
      }

      const { name, email, password, phone, role, profile_data } = data;

      // Create user in Supabase Auth
      const { data: authData, error: authError } = await this.getClient().auth.admin.createUser({
        email,
        password,
        user_metadata: {
          name,
          role
        },
        email_confirm: true // Auto-confirm email for admin-created users
      });

      if (authError || !authData.user) {
        logger.error('Failed to create auth user:', { email, error: authError });
        
        if (authError?.message.includes('already registered')) {
          throw new ApiError('Email already registered', 409);
        }
        
        throw new ApiError('Failed to create user account', 500);
      }

      // Create user profile in database
      const userProfileData = {
        id: authData.user.id,
        email,
        name,
        phone,
        role,
        profile_data: profile_data || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: userProfile, error: profileError } = await this.getClient()
        .from('users')
        .insert(userProfileData)
        .select()
        .single();

      if (profileError) {
        logger.error('Failed to create user profile:', { userId: authData.user.id, error: profileError });
        
        // Clean up auth user if profile creation fails
        await this.getClient().auth.admin.deleteUser(authData.user.id);
        
        throw new ApiError('Failed to create user profile', 500);
      }

      logger.info('User created successfully:', { 
        userId: authData.user.id, 
        email, 
        role,
        createdBy: requesterId
      });

      return userProfile as User;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Create user service error:', error);
      throw new ApiError('Failed to create user', 500);
    }
  }

  /**
   * Delete user (admin only)
   */
  async deleteUser(
    userId: string,
    requesterId: string,
    requesterRole: string
  ): Promise<void> {
    try {
      // Only admin can delete users
      if (requesterRole !== 'admin') {
        throw new ApiError('Only admin can delete users', 403);
      }

      // Cannot delete self
      if (userId === requesterId) {
        throw new ApiError('Cannot delete your own account', 400);
      }

      // Check if user exists
      const { data: user, error: checkError } = await this.getClient()
        .from('users')
        .select('id, email, role')
        .eq('id', userId)
        .single();

      if (checkError || !user) {
        throw new ApiError('User not found', 404);
      }

      // Delete user from database (this will cascade to related records)
      const { error: deleteError } = await this.getClient()
        .from('users')
        .delete()
        .eq('id', userId);

      if (deleteError) {
        logger.error('Failed to delete user from database:', { userId, error: deleteError });
        throw new ApiError('Failed to delete user', 500);
      }

      // Delete user from Supabase Auth
      const { error: authDeleteError } = await this.getClient().auth.admin.deleteUser(userId);

      if (authDeleteError) {
        logger.warn('Failed to delete user from auth (user may not exist in auth):', { 
          userId, 
          error: authDeleteError 
        });
        // Don't throw error here as the database record is already deleted
      }

      logger.info('User deleted successfully:', { 
        userId, 
        email: user.email,
        deletedBy: requesterId
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Delete user service error:', error);
      throw new ApiError('Failed to delete user', 500);
    }
  }

  /**
   * Get user statistics (admin only)
   */
  async getUserStats(
    requesterId: string,
    requesterRole: string
  ): Promise<UserStats> {
    try {
      // Only admin can view user statistics
      if (requesterRole !== 'admin') {
        throw new ApiError('Only admin can view user statistics', 403);
      }

      // Get total users count
      const { count: totalUsers, error: totalError } = await this.getClient()
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (totalError) {
        throw new ApiError('Failed to get user statistics', 500);
      }

      // Get users by role
      const { data: roleData, error: roleError } = await this.getClient()
        .from('users')
        .select('role')
        .not('role', 'is', null);

      if (roleError) {
        throw new ApiError('Failed to get user role statistics', 500);
      }

      const usersByRole = roleData.reduce((acc, user) => {
        acc[user.role as UserRole] = (acc[user.role as UserRole] || 0) + 1;
        return acc;
      }, {} as Record<UserRole, number>);

      // Get new users this month
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const { count: newUsersThisMonth, error: monthError } = await this.getClient()
        .from('users')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', thisMonth.toISOString());

      if (monthError) {
        throw new ApiError('Failed to get monthly user statistics', 500);
      }

      // Get active users this week (users who have conversations or orders)
      const thisWeek = new Date();
      thisWeek.setDate(thisWeek.getDate() - 7);

      const { count: activeUsersThisWeek, error: weekError } = await this.getClient()
        .from('users')
        .select('*', { count: 'exact', head: true })
        .or(`conversations.last_message_at.gte.${thisWeek.toISOString()},orders.created_at.gte.${thisWeek.toISOString()}`);

      // Don't throw error if this query fails, just set to 0
      const activeCount = weekError ? 0 : (activeUsersThisWeek || 0);

      logger.info('User statistics retrieved successfully:', { requesterId });

      return {
        total_users: totalUsers || 0,
        users_by_role: usersByRole,
        new_users_this_month: newUsersThisMonth || 0,
        active_users_this_week: activeCount
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get user stats service error:', error);
      throw new ApiError('Failed to get user statistics', 500);
    }
  }
}
