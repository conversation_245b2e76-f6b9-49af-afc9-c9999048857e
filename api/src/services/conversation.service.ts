import { SupabaseService } from './supabase.service';
import { 
  Conversation,
  ConversationWithUser,
  GetConversationsFilters,
  GetConversationsResponse,
  CreateConversationData,
  MarkAsReadData,
  ConversationStats,
  AvailableUser
} from '@/types/conversation.types';
import { UserRole } from '@/types/user.types';
import { logger } from '@/utils/logger';
import { ApiError } from '@/middleware/error.middleware';

export class ConversationService extends SupabaseService {
  constructor() {
    super(); // Use service role client
  }

  /**
   * Get conversations for the current user
   */
  async getConversations(
    userId: string,
    userRole: string,
    filters: GetConversationsFilters = {}
  ): Promise<GetConversationsResponse> {
    try {
      let query = this.getClient()
        .from('conversations')
        .select(`
          *,
          user:users(id, name, email, phone, role, profile_data)
        `, { count: 'exact' });

      // Apply role-based filtering
      if (userRole === 'admin' || userRole === 'staff') {
        // Admin/staff can see all conversations
        if (filters.user_role) {
          query = query.eq('user_role', filters.user_role);
        }
      } else {
        // Regular users can only see their own conversations
        query = query.eq('user_id', userId);
      }

      // Apply additional filters
      if (filters.search) {
        query = query.or(
          `user.name.ilike.%${filters.search}%,user.email.ilike.%${filters.search}%,last_message.ilike.%${filters.search}%`
        );
      }

      if (filters.unread_only) {
        query = query.is('read_at', null);
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const start = (page - 1) * limit;

      // Order by last message time (most recent first)
      query = query
        .order('last_message_at', { ascending: false })
        .range(start, start + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error('Failed to get conversations:', error);
        throw new ApiError('Failed to get conversations', 500);
      }

      logger.info('Conversations retrieved successfully:', { 
        userId, 
        userRole,
        count: data?.length || 0,
        total: count || 0
      });

      return {
        items: data || [],
        total: count || 0
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get conversations service error:', error);
      throw new ApiError('Failed to get conversations', 500);
    }
  }

  /**
   * Get conversation by ID
   */
  async getConversationById(
    conversationId: string,
    userId: string,
    userRole: string
  ): Promise<ConversationWithUser | null> {
    try {
      let query = this.getClient()
        .from('conversations')
        .select(`
          *,
          user:users(id, name, email, phone, role, profile_data)
        `)
        .eq('id', conversationId);

      // Apply access control
      if (userRole !== 'admin' && userRole !== 'staff') {
        query = query.eq('user_id', userId);
      }

      const { data: conversation, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        logger.error('Failed to get conversation:', error);
        throw new ApiError('Failed to get conversation', 500);
      }

      logger.info('Conversation retrieved successfully:', { conversationId, userId });

      return conversation as ConversationWithUser;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get conversation by ID service error:', error);
      throw new ApiError('Failed to get conversation', 500);
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    creatorId: string,
    data: CreateConversationData
  ): Promise<{ conversation: Conversation; isNew: boolean }> {
    try {
      const { user_id, user_role, initial_message, conversation_data } = data;

      // Check if conversation already exists for this user
      const { data: existingConversation, error: checkError } = await this.getClient()
        .from('conversations')
        .select('id')
        .eq('user_id', user_id)
        .maybeSingle();

      if (checkError) {
        logger.error('Failed to check existing conversation:', checkError);
        throw new ApiError('Failed to check existing conversation', 500);
      }

      // If conversation exists, return it
      if (existingConversation) {
        const existing = await this.getConversationById(existingConversation.id, creatorId, 'admin');
        return { 
          conversation: existing as Conversation, 
          isNew: false 
        };
      }

      // Create new conversation
      const conversationData = {
        user_id,
        user_role,
        conversation_data: conversation_data || {},
        last_message_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newConversation, error: createError } = await this.getClient()
        .from('conversations')
        .insert(conversationData)
        .select()
        .single();

      if (createError) {
        logger.error('Failed to create conversation:', createError);
        throw new ApiError('Failed to create conversation', 500);
      }

      // If initial message is provided, create it
      if (initial_message && newConversation) {
        const { error: messageError } = await this.getClient()
          .from('messages')
          .insert({
            conversation_id: newConversation.id,
            sender_id: creatorId,
            content: initial_message,
            is_system: true, // Messages from admin/staff are system messages
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (messageError) {
          logger.warn('Failed to create initial message:', messageError);
          // Don't throw error as conversation was created successfully
        }
      }

      logger.info('Conversation created successfully:', { 
        conversationId: newConversation.id,
        userId: user_id,
        creatorId
      });

      return { 
        conversation: newConversation as Conversation, 
        isNew: true 
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Create conversation service error:', error);
      throw new ApiError('Failed to create conversation', 500);
    }
  }

  /**
   * Mark conversation as read
   */
  async markAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      // Verify user has access to this conversation
      const hasAccess = await this.validateUserAccess(userId, 'conversation', conversationId);
      
      if (!hasAccess) {
        throw new ApiError('Access denied to this conversation', 403);
      }

      // Update conversation read timestamp
      const { error: conversationError } = await this.getClient()
        .from('conversations')
        .update({ 
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId);

      if (conversationError) {
        logger.error('Failed to mark conversation as read:', conversationError);
        throw new ApiError('Failed to mark conversation as read', 500);
      }

      // Mark unread messages as read
      const { error: messagesError } = await this.getClient()
        .from('messages')
        .update({ 
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('conversation_id', conversationId)
        .is('read_at', null);

      if (messagesError) {
        logger.warn('Failed to mark messages as read:', messagesError);
        // Don't throw error as conversation was marked as read
      }

      logger.info('Conversation marked as read:', { conversationId, userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Mark as read service error:', error);
      throw new ApiError('Failed to mark conversation as read', 500);
    }
  }

  /**
   * Get available users for creating conversations
   */
  async getAvailableUsers(role?: UserRole): Promise<AvailableUser[]> {
    try {
      let query = this.getClient()
        .from('users')
        .select('id, name, email, phone, role');

      // Filter by role if specified
      if (role === 'customer' || role === 'technician') {
        query = query.eq('role', role);
      } else {
        // Only get customers and technicians
        query = query.in('role', ['customer', 'technician']);
      }

      const { data: allUsers, error: usersError } = await query;

      if (usersError) {
        logger.error('Failed to get users:', usersError);
        throw new ApiError('Failed to get users', 500);
      }

      // Get existing conversations
      const { data: conversations, error: convError } = await this.getClient()
        .from('conversations')
        .select('user_id');

      if (convError) {
        logger.error('Failed to get existing conversations:', convError);
        throw new ApiError('Failed to get existing conversations', 500);
      }

      // Create set of user IDs that already have conversations
      const existingUserIds = new Set(conversations.map(c => c.user_id));

      // Filter out users who already have conversations
      const availableUsers = allUsers.filter(
        user => !existingUserIds.has(user.id)
      );

      logger.info('Available users retrieved successfully:', { 
        total: availableUsers.length,
        role
      });

      return availableUsers as AvailableUser[];
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get available users service error:', error);
      throw new ApiError('Failed to get available users', 500);
    }
  }

  /**
   * Get conversation statistics
   */
  async getConversationStats(
    userId: string,
    userRole: string
  ): Promise<ConversationStats> {
    try {
      // Only admin/staff can view full statistics
      if (userRole !== 'admin' && userRole !== 'staff') {
        throw new ApiError('Insufficient permissions to view statistics', 403);
      }

      // Get total conversations
      const { count: totalConversations, error: totalError } = await this.getClient()
        .from('conversations')
        .select('*', { count: 'exact', head: true });

      if (totalError) {
        throw new ApiError('Failed to get conversation statistics', 500);
      }

      // Get unread conversations
      const { count: unreadConversations, error: unreadError } = await this.getClient()
        .from('conversations')
        .select('*', { count: 'exact', head: true })
        .is('read_at', null);

      if (unreadError) {
        throw new ApiError('Failed to get unread conversation statistics', 500);
      }

      // Get conversations by role
      const { data: roleData, error: roleError } = await this.getClient()
        .from('conversations')
        .select('user_role');

      if (roleError) {
        throw new ApiError('Failed to get conversation role statistics', 500);
      }

      const conversationsByRole = roleData.reduce((acc, conv) => {
        acc[conv.user_role as 'customer' | 'technician'] = 
          (acc[conv.user_role as 'customer' | 'technician'] || 0) + 1;
        return acc;
      }, {} as Record<'customer' | 'technician', number>);

      // Get messages today
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { count: messagesToday, error: messagesError } = await this.getClient()
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (messagesError) {
        throw new ApiError('Failed to get message statistics', 500);
      }

      logger.info('Conversation statistics retrieved successfully:', { userId });

      return {
        total_conversations: totalConversations || 0,
        unread_conversations: unreadConversations || 0,
        conversations_by_role: conversationsByRole,
        messages_today: messagesToday || 0,
        average_response_time: 0 // TODO: Implement response time calculation
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get conversation stats service error:', error);
      throw new ApiError('Failed to get conversation statistics', 500);
    }
  }
}
