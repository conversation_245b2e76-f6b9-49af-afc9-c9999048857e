import { SupabaseService } from './supabase.service';
import { 
  Message,
  MessageWithSender,
  GetMessagesFilters,
  GetMessagesResponse,
  CreateMessageData
} from '@/types/conversation.types';
import { logger } from '@/utils/logger';
import { ApiError } from '@/middleware/error.middleware';

export class MessageService extends SupabaseService {
  constructor() {
    super(); // Use service role client
  }

  /**
   * Get messages for a conversation
   */
  async getMessages(filters: GetMessagesFilters): Promise<GetMessagesResponse> {
    try {
      const { conversation_id, page = 1, limit = 50, created_at_lt, created_at_gt } = filters;

      let query = this.getClient()
        .from('messages')
        .select(`
          *,
          sender:users(id, name, email, role, profile_data)
        `, { count: 'exact' })
        .eq('conversation_id', conversation_id);

      // Apply time-based filters for pagination
      if (created_at_lt) {
        query = query.lt('created_at', created_at_lt);
      }

      if (created_at_gt) {
        query = query.gt('created_at', created_at_gt);
      }

      // Pagination
      const start = (page - 1) * limit;

      // Order by creation time (newest first for pagination, will reverse later)
      query = query
        .order('created_at', { ascending: false })
        .range(start, start + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error('Failed to get messages:', error);
        throw new ApiError('Failed to get messages', 500);
      }

      // Reverse the order so oldest messages come first (chat order)
      const messages = (data || []).reverse();

      logger.info('Messages retrieved successfully:', { 
        conversationId: conversation_id,
        count: messages.length,
        total: count || 0
      });

      return {
        items: messages as MessageWithSender[],
        total: count || 0
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get messages service error:', error);
      throw new ApiError('Failed to get messages', 500);
    }
  }

  /**
   * Send a message
   */
  async sendMessage(data: CreateMessageData, senderRole: string): Promise<MessageWithSender> {
    try {
      const { conversation_id, content, is_system, message_data, sender_id } = data;

      // Determine if message is from system based on sender role
      const isSystemUser = senderRole === 'admin' || senderRole === 'staff';
      const messageIsSystem = is_system !== undefined ? is_system : isSystemUser;

      const messageData = {
        conversation_id,
        sender_id: sender_id!,
        content,
        is_system: messageIsSystem,
        message_data: message_data || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newMessage, error: createError } = await this.getClient()
        .from('messages')
        .insert(messageData)
        .select(`
          *,
          sender:users(id, name, email, role, profile_data)
        `)
        .single();

      if (createError) {
        logger.error('Failed to send message:', createError);
        throw new ApiError('Failed to send message', 500);
      }

      // Update conversation last_message and last_message_at
      const { error: updateError } = await this.getClient()
        .from('conversations')
        .update({
          last_message: content,
          last_message_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', conversation_id);

      if (updateError) {
        logger.warn('Failed to update conversation last message:', updateError);
        // Don't throw error as message was created successfully
      }

      logger.info('Message sent successfully:', { 
        messageId: newMessage.id,
        conversationId: conversation_id,
        senderId: sender_id,
        isSystem: messageIsSystem
      });

      return newMessage as MessageWithSender;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Send message service error:', error);
      throw new ApiError('Failed to send message', 500);
    }
  }

  /**
   * Get message by ID
   */
  async getMessageById(messageId: string, userId: string): Promise<MessageWithSender | null> {
    try {
      const { data: message, error } = await this.getClient()
        .from('messages')
        .select(`
          *,
          sender:users(id, name, email, role, profile_data),
          conversation:conversations(id, user_id)
        `)
        .eq('id', messageId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        logger.error('Failed to get message:', error);
        throw new ApiError('Failed to get message', 500);
      }

      // Check if user has access to this message's conversation
      const hasAccess = await this.verifyConversationAccess(
        message.conversation.id,
        userId
      );

      if (!hasAccess) {
        throw new ApiError('Access denied to this message', 403);
      }

      logger.info('Message retrieved successfully:', { messageId, userId });

      return message as MessageWithSender;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Get message by ID service error:', error);
      throw new ApiError('Failed to get message', 500);
    }
  }

  /**
   * Verify user has access to a conversation
   */
  async verifyConversationAccess(conversationId: string, userId: string): Promise<boolean> {
    try {
      const { data: conversation, error } = await this.getClient()
        .from('conversations')
        .select('user_id')
        .eq('id', conversationId)
        .single();

      if (error || !conversation) {
        return false;
      }

      // User has access if they own the conversation
      return conversation.user_id === userId;
    } catch (error) {
      logger.error('Failed to verify conversation access:', error);
      return false;
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      // First verify user has access to this message
      const message = await this.getMessageById(messageId, userId);
      
      if (!message) {
        throw new ApiError('Message not found or access denied', 404);
      }

      // Update message read timestamp
      const { error } = await this.getClient()
        .from('messages')
        .update({ 
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        logger.error('Failed to mark message as read:', error);
        throw new ApiError('Failed to mark message as read', 500);
      }

      logger.info('Message marked as read:', { messageId, userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Mark message as read service error:', error);
      throw new ApiError('Failed to mark message as read', 500);
    }
  }

  /**
   * Delete message (admin only)
   */
  async deleteMessage(messageId: string, userId: string, userRole: string): Promise<void> {
    try {
      // Only admin can delete messages
      if (userRole !== 'admin') {
        throw new ApiError('Only admin can delete messages', 403);
      }

      // Check if message exists
      const { data: message, error: checkError } = await this.getClient()
        .from('messages')
        .select('id, conversation_id, content')
        .eq('id', messageId)
        .single();

      if (checkError || !message) {
        throw new ApiError('Message not found', 404);
      }

      // Delete message
      const { error: deleteError } = await this.getClient()
        .from('messages')
        .delete()
        .eq('id', messageId);

      if (deleteError) {
        logger.error('Failed to delete message:', deleteError);
        throw new ApiError('Failed to delete message', 500);
      }

      logger.info('Message deleted successfully:', { 
        messageId,
        conversationId: message.conversation_id,
        deletedBy: userId
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error('Delete message service error:', error);
      throw new ApiError('Failed to delete message', 500);
    }
  }

  /**
   * Get unread message count for a user
   */
  async getUnreadMessageCount(userId: string): Promise<number> {
    try {
      // Get conversations for this user
      const { data: conversations, error: convError } = await this.getClient()
        .from('conversations')
        .select('id')
        .eq('user_id', userId);

      if (convError || !conversations) {
        logger.error('Failed to get user conversations:', convError);
        return 0;
      }

      const conversationIds = conversations.map(c => c.id);

      if (conversationIds.length === 0) {
        return 0;
      }

      // Count unread messages in user's conversations
      const { count, error: countError } = await this.getClient()
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .in('conversation_id', conversationIds)
        .is('read_at', null)
        .neq('sender_id', userId); // Don't count user's own messages

      if (countError) {
        logger.error('Failed to get unread message count:', countError);
        return 0;
      }

      return count || 0;
    } catch (error) {
      logger.error('Get unread message count service error:', error);
      return 0;
    }
  }
}
