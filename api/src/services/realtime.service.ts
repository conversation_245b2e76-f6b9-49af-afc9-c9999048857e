import { Server as SocketIOServer, Socket } from "socket.io";
import { Server as HTTPServer } from "http";
import { supabase } from "@/config/database";
import { MessageWithSender } from "@/types/conversation.types";
import { AuthUser } from "@/types/auth.types";
import { logger } from "@/utils/logger";
import { config } from "@/config/app";

interface AuthenticatedSocket extends Socket {
  user?: AuthUser;
}

interface SocketData {
  userId: string;
  userRole: string;
  conversationIds: Set<string>;
}

export class RealtimeService {
  private static instance: RealtimeService;
  private io: SocketIOServer | null = null;
  private connectedUsers = new Map<string, Set<string>>(); // userId -> Set of socketIds
  private socketData = new Map<string, SocketData>(); // socketId -> user data
  private conversationChannels = new Map<string, any>(); // conversationId -> Supabase channel

  private constructor() {}

  public static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  /**
   * Initialize WebSocket server
   */
  public initialize(server: HTTPServer): void {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: config.cors.origin,
        credentials: config.cors.credentials,
        methods: ["GET", "POST"],
      },
      path: "/socket.io",
      transports: ["websocket", "polling"],
    });

    this.setupMiddleware();
    this.setupEventHandlers();

    logger.info("WebSocket server initialized");
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware(): void {
    if (!this.io) return;

    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token =
          socket.handshake.auth.token ||
          socket.handshake.headers.authorization?.replace("Bearer ", "");

        if (!token) {
          return next(new Error("Authentication token required"));
        }

        // Validate token with Supabase
        const {
          data: { user },
          error,
        } = await supabase.auth.getUser(token);

        if (error || !user) {
          logger.warn("WebSocket authentication failed:", {
            error: error?.message,
          });
          return next(new Error("Invalid or expired token"));
        }

        socket.user = user as unknown as AuthUser;

        logger.debug("WebSocket user authenticated:", {
          userId: user.id,
          socketId: socket.id,
        });

        next();
      } catch (error) {
        logger.error("WebSocket authentication error:", error);
        next(new Error("Authentication failed"));
      }
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on("connection", (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });
  }

  /**
   * Handle new socket connection
   */
  private handleConnection(socket: AuthenticatedSocket): void {
    if (!socket.user) return;

    const userId = socket.user.id;
    const userRole = socket.user.user_metadata?.role || "customer";

    logger.info("WebSocket client connected:", {
      userId,
      userRole,
      socketId: socket.id,
    });

    // Track connected user
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId)!.add(socket.id);

    // Store socket data
    this.socketData.set(socket.id, {
      userId,
      userRole,
      conversationIds: new Set(),
    });

    // Setup socket event handlers
    this.setupSocketHandlers(socket);

    // Handle disconnection
    socket.on("disconnect", () => {
      this.handleDisconnection(socket);
    });
  }

  /**
   * Setup individual socket event handlers
   */
  private setupSocketHandlers(socket: AuthenticatedSocket): void {
    // Join conversation room
    socket.on("join_conversation", async (conversationId: string) => {
      await this.joinConversation(socket, conversationId);
    });

    // Leave conversation room
    socket.on("leave_conversation", (conversationId: string) => {
      this.leaveConversation(socket, conversationId);
    });

    // Handle typing indicators
    socket.on("typing_start", (conversationId: string) => {
      this.handleTyping(socket, conversationId, true);
    });

    socket.on("typing_stop", (conversationId: string) => {
      this.handleTyping(socket, conversationId, false);
    });

    // Handle message read status
    socket.on("message_read", (messageId: string) => {
      this.handleMessageRead(socket, messageId);
    });
  }

  /**
   * Join conversation room and setup realtime subscription
   */
  private async joinConversation(
    socket: AuthenticatedSocket,
    conversationId: string
  ): Promise<void> {
    try {
      if (!socket.user) return;

      const userId = socket.user.id;
      const userRole = socket.user.user_metadata?.role || "customer";

      // Verify user has access to this conversation
      const { data: conversation, error } = await supabase
        .from("conversations")
        .select("user_id")
        .eq("id", conversationId)
        .single();

      if (error || !conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      // Check access permissions
      const hasAccess =
        userRole === "admin" ||
        userRole === "staff" ||
        conversation.user_id === userId;

      if (!hasAccess) {
        socket.emit("error", { message: "Access denied to this conversation" });
        return;
      }

      // Join socket room
      socket.join(`conversation:${conversationId}`);

      // Track conversation for this socket
      const socketData = this.socketData.get(socket.id);
      if (socketData) {
        socketData.conversationIds.add(conversationId);
      }

      // Setup Supabase realtime subscription if not already exists
      if (!this.conversationChannels.has(conversationId)) {
        this.setupConversationChannel(conversationId);
      }

      logger.info("User joined conversation:", {
        userId,
        conversationId,
        socketId: socket.id,
      });

      socket.emit("conversation_joined", { conversationId });
    } catch (error) {
      logger.error("Error joining conversation:", error);
      socket.emit("error", { message: "Failed to join conversation" });
    }
  }

  /**
   * Leave conversation room
   */
  private leaveConversation(
    socket: AuthenticatedSocket,
    conversationId: string
  ): void {
    socket.leave(`conversation:${conversationId}`);

    const socketData = this.socketData.get(socket.id);
    if (socketData) {
      socketData.conversationIds.delete(conversationId);
    }

    logger.info("User left conversation:", {
      userId: socket.user?.id,
      conversationId,
      socketId: socket.id,
    });

    socket.emit("conversation_left", { conversationId });
  }

  /**
   * Setup Supabase realtime channel for conversation
   */
  private setupConversationChannel(conversationId: string): void {
    const channel = supabase
      .channel(`conversation-${conversationId}`)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "messages",
          filter: `conversation_id=eq.${conversationId}`,
        },
        async (payload) => {
          await this.handleNewMessage(conversationId, payload.new);
        }
      )
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "messages",
          filter: `conversation_id=eq.${conversationId}`,
        },
        async (payload) => {
          await this.handleMessageUpdate(conversationId, payload.new);
        }
      )
      .subscribe();

    this.conversationChannels.set(conversationId, channel);

    logger.info("Supabase realtime channel setup:", { conversationId });
  }

  /**
   * Handle new message from Supabase realtime
   */
  private async handleNewMessage(
    conversationId: string,
    messageData: any
  ): Promise<void> {
    try {
      // Get full message with sender details
      const { data: message, error } = await supabase
        .from("messages")
        .select(
          `
          *,
          sender:users(id, name, email, role, profile_data)
        `
        )
        .eq("id", messageData.id)
        .single();

      if (error || !message) {
        logger.error("Failed to get message details:", error);
        return;
      }

      // Broadcast to conversation room
      this.broadcastToConversation(conversationId, "new_message", message);

      logger.info("New message broadcasted:", {
        conversationId,
        messageId: message.id,
        senderId: message.sender_id,
      });
    } catch (error) {
      logger.error("Error handling new message:", error);
    }
  }

  /**
   * Handle message update from Supabase realtime
   */
  private async handleMessageUpdate(
    conversationId: string,
    messageData: any
  ): Promise<void> {
    try {
      // Broadcast message update to conversation room
      this.broadcastToConversation(
        conversationId,
        "message_updated",
        messageData
      );

      logger.debug("Message update broadcasted:", {
        conversationId,
        messageId: messageData.id,
      });
    } catch (error) {
      logger.error("Error handling message update:", error);
    }
  }

  /**
   * Handle typing indicators
   */
  private handleTyping(
    socket: AuthenticatedSocket,
    conversationId: string,
    isTyping: boolean
  ): void {
    if (!socket.user) return;

    const typingData = {
      userId: socket.user.id,
      userName: socket.user.user_metadata?.name || "User",
      isTyping,
      conversationId,
    };

    // Broadcast to others in the conversation (exclude sender)
    socket.to(`conversation:${conversationId}`).emit("user_typing", typingData);

    logger.debug("Typing indicator:", {
      userId: socket.user.id,
      conversationId,
      isTyping,
    });
  }

  /**
   * Handle message read status
   */
  private handleMessageRead(
    socket: AuthenticatedSocket,
    messageId: string
  ): void {
    if (!socket.user) return;

    // Broadcast read status to relevant users
    // This could be enhanced to only notify the message sender
    socket.broadcast.emit("message_read", {
      messageId,
      readBy: socket.user.id,
      readAt: new Date().toISOString(),
    });

    logger.debug("Message read status broadcasted:", {
      messageId,
      readBy: socket.user.id,
    });
  }

  /**
   * Handle socket disconnection
   */
  private handleDisconnection(socket: AuthenticatedSocket): void {
    if (!socket.user) return;

    const userId = socket.user.id;

    // Remove from connected users
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.delete(socket.id);
      if (userSockets.size === 0) {
        this.connectedUsers.delete(userId);
      }
    }

    // Clean up socket data
    this.socketData.delete(socket.id);

    logger.info("WebSocket client disconnected:", {
      userId,
      socketId: socket.id,
    });
  }

  /**
   * Broadcast message to conversation room
   */
  public broadcastToConversation(
    conversationId: string,
    event: string,
    data: any
  ): void {
    if (!this.io) return;

    this.io.to(`conversation:${conversationId}`).emit(event, data);
  }

  /**
   * Broadcast message to specific user
   */
  public broadcastToUser(userId: string, event: string, data: any): void {
    if (!this.io) return;

    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.forEach((socketId) => {
        this.io!.to(socketId).emit(event, data);
      });
    }
  }

  /**
   * Get connected users count
   */
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Check if user is online
   */
  public isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    // Unsubscribe from all Supabase channels
    this.conversationChannels.forEach((channel) => {
      supabase.removeChannel(channel);
    });
    this.conversationChannels.clear();

    // Clear tracking data
    this.connectedUsers.clear();
    this.socketData.clear();

    logger.info("Realtime service cleaned up");
  }
}
