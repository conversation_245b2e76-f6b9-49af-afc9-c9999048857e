import "module-alias/register";
import app from "./app";
import { createServer } from "http";
import { config } from "@/config/app";
import { logger } from "@/utils/logger";
import { testDatabaseConnection } from "@/config/database";
import { RealtimeService } from "@/services/realtime.service";

/**
 * Start the server
 */
async function startServer() {
  try {
    // Test database connection
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      logger.error("Failed to connect to database");
      process.exit(1);
    }

    // Create HTTP server
    const server = createServer(app);

    // Initialize WebSocket server
    const realtimeService = RealtimeService.getInstance();
    realtimeService.initialize(server);

    // Start the server
    server.listen(config.port, () => {
      logger.info(`🚀 VTech API Server started successfully!`);
      logger.info(`📍 Server running on port ${config.port}`);
      logger.info(`🌍 Environment: ${config.nodeEnv}`);
      logger.info(`📊 Health check: http://localhost:${config.port}/health`);
      logger.info(`🔗 API Base URL: http://localhost:${config.port}/api`);
      logger.info(
        `⚡ WebSocket server: ws://localhost:${config.port}/socket.io`
      );
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Cleanup realtime service
      realtimeService.cleanup();

      server.close(() => {
        logger.info("HTTP server closed");
        process.exit(0);
      });

      // Force close after 30 seconds
      setTimeout(() => {
        logger.error(
          "Could not close connections in time, forcefully shutting down"
        );
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.error("Uncaught Exception:", error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason, promise) => {
      logger.error("Unhandled Rejection at:", promise, "reason:", reason);
      process.exit(1);
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Start the server
startServer();

// Export app for serverless deployment
export default app;
