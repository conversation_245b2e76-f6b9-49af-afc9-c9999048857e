{"name": "vtech-api", "version": "1.0.0", "description": "VTech API Server - Express.js TypeScript Serverless", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node -r module-alias/register dist/index.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "_moduleAliases": {"@": "dist"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.88", "@supabase/supabase-js": "^2.49.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.5", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.19.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.2", "typescript": "^5.9.2"}, "engines": {"node": ">=18.0.0"}, "author": {"name": "LEDHCG", "url": "https://github.com/ledhcg"}, "license": "MIT"}