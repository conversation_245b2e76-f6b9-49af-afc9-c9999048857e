# VTech API Server

Express.js TypeScript serverless API for VTech application with Supabase integration.

## 🚀 Features

- **Authentication**: JWT-based auth with Supabase
- **Real-time Chat**: Messages and conversations with WebSocket support
- **User Management**: CRUD operations with role-based access
- **Type Safety**: Full TypeScript implementation
- **Serverless**: Vercel deployment ready
- **Security**: Rate limiting, CORS, input validation
- **Logging**: Structured logging with Winston

## 📁 Project Structure

```
api/
├── src/
│   ├── controllers/          # Request/Response handling
│   ├── routes/               # Route definitions
│   ├── services/             # Business logic
│   ├── middleware/           # Auth, validation, CORS
│   ├── types/                # TypeScript definitions
│   ├── utils/                # Helpers, validation schemas
│   ├── config/               # App configuration
│   └── app.ts                # Express app setup
├── vercel.json               # Serverless deployment
└── package.json
```

## 🛠️ Installation

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Update .env with your values
```

## 🔧 Environment Variables

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=http://localhost:3000
MOBILE_URL=http://localhost:8081
```

## 🚀 Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Lint code
npm run lint
```

## 📊 API Endpoints

### Authentication
```
POST /api/auth/login          # User login
POST /api/auth/register       # User registration
POST /api/auth/refresh        # Refresh token
POST /api/auth/logout         # User logout
GET  /api/auth/me             # Get current user
GET  /api/auth/verify         # Verify token
```

### Users
```
GET    /api/users             # Get users (admin/staff)
POST   /api/users             # Create user (admin)
GET    /api/users/me          # Get current user
PUT    /api/users/me          # Update current user
GET    /api/users/stats       # User statistics (admin)
GET    /api/users/:id         # Get user by ID
PUT    /api/users/:id         # Update user
DELETE /api/users/:id         # Delete user (admin)
```

### Conversations
```
GET    /api/conversations                    # Get conversations
POST   /api/conversations                    # Create conversation (admin/staff)
GET    /api/conversations/stats              # Conversation statistics
GET    /api/conversations/available-users    # Available users for chat
GET    /api/conversations/:id               # Get conversation
PUT    /api/conversations/:id/read          # Mark as read
DELETE /api/conversations/:id               # Delete conversation (admin)
```

### Messages
```
GET    /api/conversations/:id/messages              # Get messages
POST   /api/conversations/:id/messages              # Send message
POST   /api/conversations/:id/messages/bulk-read    # Mark all as read
GET    /api/messages/unread-count                   # Unread count
GET    /api/messages/:id                            # Get message
PUT    /api/messages/:id/read                       # Mark message as read
DELETE /api/messages/:id                            # Delete message (admin)
```

## 🔐 Authentication

The API uses Supabase JWT tokens for authentication:

1. **Frontend/Mobile** sends Supabase JWT in Authorization header
2. **API** validates JWT with Supabase and extracts user info
3. **Database operations** use service role for RLS bypass
4. **Access control** implemented in application layer

```typescript
// Example request
fetch('/api/conversations', {
  headers: {
    'Authorization': `Bearer ${supabaseToken}`,
    'Content-Type': 'application/json'
  }
});
```

## 🛡️ Security Features

- **Rate Limiting**: 100 requests per 15 minutes
- **Auth Rate Limiting**: 5 login attempts per 15 minutes
- **CORS**: Configured for frontend and mobile origins
- **Input Validation**: Zod schemas for all endpoints
- **Error Sanitization**: No sensitive data in error responses
- **Helmet**: Security headers

## 📝 Response Format

All API responses follow a consistent format:

```typescript
// Success Response
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Error Response
{
  "success": false,
  "message": "Error message",
  "data": null,
  "errors": [ ... ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Paginated Response
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": {
    "items": [ ... ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🚀 Deployment

### Vercel Deployment

1. Install Vercel CLI: `npm i -g vercel`
2. Set environment variables in Vercel dashboard
3. Deploy: `vercel --prod`

### Environment Variables for Production

Set these in your Vercel dashboard:
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `JWT_SECRET`
- `FRONTEND_URL`
- `MOBILE_URL`

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📚 Architecture

### MVC Pattern
- **Controllers**: Handle HTTP requests/responses
- **Services**: Business logic and data operations
- **Routes**: Endpoint definitions and middleware
- **Middleware**: Authentication, validation, error handling

### Database Access
- **Service Role Client**: For database operations
- **User-Scoped Client**: For RLS operations (future use)
- **Access Control**: Application-level permissions

### Error Handling
- **Centralized**: All errors handled by error middleware
- **Typed**: Custom ApiError class for consistent errors
- **Logged**: All errors logged with context

## 🔄 Real-time Features

Real-time chat functionality will be implemented in Phase 3:
- WebSocket server with Socket.io
- Supabase realtime proxy
- Message broadcasting
- Connection management

## 📖 Development Guidelines

1. **Type Safety**: Always use TypeScript types
2. **Error Handling**: Use ApiError for consistent errors
3. **Logging**: Log all important actions and errors
4. **Validation**: Validate all inputs with Zod
5. **Security**: Follow principle of least privilege
6. **Testing**: Write tests for all business logic

## 🤝 Contributing

1. Follow TypeScript best practices
2. Use consistent naming conventions
3. Add proper error handling
4. Include logging for debugging
5. Write tests for new features
6. Update documentation

## 📄 License

MIT License - see LICENSE file for details
